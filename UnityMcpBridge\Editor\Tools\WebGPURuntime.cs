using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles WebGPU Runtime operations for high-performance web applications.
    /// </summary>
    public static class WebGPURuntime
    {
        private static readonly List<string> ValidActions = new List<string>
        {
            "create",
            "modify",
            "delete",
            "setup",
            "configure",
            "optimize",
            "validate",
            "test",
            "get_info",
            "get_status",
            "list"
        };

        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error(
                    $"Unknown action: '{action}'. Valid actions are: {validActionsList}"
                );
            }

            try
            {
                // Route to specific WebGPU operations based on parameters
                if (@params.ContainsKey("shader_name") || @params.ContainsKey("compute_kernel"))
                {
                    return HandleComputeShaders(@params);
                }
                else if (@params.ContainsKey("rendering_pipeline") || @params.ContainsKey("indirect_buffer_size"))
                {
                    return HandleIndirectRendering(@params);
                }
                else if (@params.ContainsKey("character_name") || @params.ContainsKey("bone_count"))
                {
                    return HandleGPUSkinning(@params);
                }
                else if (@params.ContainsKey("vfx_name") || @params.ContainsKey("particle_count"))
                {
                    return HandleVFXSystem(@params);
                }
                else if (@params.ContainsKey("fallback_renderer") || @params.ContainsKey("feature_detection"))
                {
                    return HandleCompatibilityDetection(@params);
                }
                else
                {
                    return Response.Error("Unable to determine WebGPU operation type from parameters.");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[WebGPURuntime] Action '{action}' failed: {e}");
                return Response.Error(
                    $"Internal error processing WebGPU action '{action}': {e.Message}"
                );
            }
        }

        private static object HandleComputeShaders(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            string shaderName = @params["shader_name"]?.ToString();
            string computeKernel = @params["compute_kernel"]?.ToString();
            var threadGroups = @params["thread_groups"]?.ToObject<int[]>();
            var bufferBindings = @params["buffer_bindings"]?.ToObject<List<Dictionary<string, object>>>();
            var dispatchSize = @params["dispatch_size"]?.ToObject<int[]>();
            string optimizationLevel = @params["optimization_level"]?.ToString() ?? "medium";
            bool enableDebugging = @params["enable_debugging"]?.ToObject<bool>() ?? false;

            try
            {
                switch (action)
                {
                    case "create":
                        return CreateComputeShader(shaderName, computeKernel, threadGroups, bufferBindings, optimizationLevel, enableDebugging);
                    case "modify":
                        return ModifyComputeShader(shaderName, computeKernel, threadGroups, bufferBindings);
                    case "delete":
                        return DeleteComputeShader(shaderName);
                    case "list":
                        return ListComputeShaders();
                    case "get_info":
                        return GetComputeShaderInfo(shaderName);
                    default:
                        return Response.Error($"Unsupported action '{action}' for compute shaders.");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in compute shader operation: {e.Message}");
            }
        }

        private static object CreateComputeShader(string shaderName, string computeKernel, int[] threadGroups, 
            List<Dictionary<string, object>> bufferBindings, string optimizationLevel, bool enableDebugging)
        {
            if (string.IsNullOrEmpty(shaderName))
            {
                return Response.Error("Shader name is required for creation.");
            }

            if (string.IsNullOrEmpty(computeKernel))
            {
                computeKernel = "CSMain";
            }

            if (threadGroups == null || threadGroups.Length != 3)
            {
                threadGroups = new int[] { 8, 8, 1 };
            }

            try
            {
                // Create compute shader directory if it doesn't exist
                string shaderDir = "Assets/Shaders/Compute";
                if (!AssetDatabase.IsValidFolder(shaderDir))
                {
                    string[] folders = shaderDir.Split('/');
                    string currentPath = folders[0];
                    for (int i = 1; i < folders.Length; i++)
                    {
                        string newPath = currentPath + "/" + folders[i];
                        if (!AssetDatabase.IsValidFolder(newPath))
                        {
                            AssetDatabase.CreateFolder(currentPath, folders[i]);
                        }
                        currentPath = newPath;
                    }
                }

                string shaderPath = $"{shaderDir}/{shaderName}.compute";
                
                // Generate compute shader content optimized for WebGPU
                string shaderContent = GenerateWebGPUComputeShader(shaderName, computeKernel, threadGroups, 
                    bufferBindings, optimizationLevel, enableDebugging);

                // Write shader file
                File.WriteAllText(shaderPath, shaderContent);
                AssetDatabase.ImportAsset(shaderPath);
                AssetDatabase.Refresh();

                // Load and validate the created compute shader
                ComputeShader computeShader = AssetDatabase.LoadAssetAtPath<ComputeShader>(shaderPath);
                if (computeShader == null)
                {
                    return Response.Error($"Failed to create compute shader at {shaderPath}");
                }

                // Validate kernel exists
                int kernelIndex = computeShader.FindKernel(computeKernel);
                if (kernelIndex < 0)
                {
                    return Response.Error($"Kernel '{computeKernel}' not found in compute shader.");
                }

                var shaderInfo = new
                {
                    name = shaderName,
                    path = shaderPath,
                    kernel = computeKernel,
                    kernelIndex = kernelIndex,
                    threadGroups = threadGroups,
                    optimizationLevel = optimizationLevel,
                    debuggingEnabled = enableDebugging,
                    webGPUCompatible = true
                };

                return Response.Success($"WebGPU compute shader '{shaderName}' created successfully.", shaderInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create compute shader: {e.Message}");
            }
        }

        private static string GenerateWebGPUComputeShader(string shaderName, string kernelName, int[] threadGroups,
            List<Dictionary<string, object>> bufferBindings, string optimizationLevel, bool enableDebugging)
        {
            var shader = new System.Text.StringBuilder();
            
            // Shader header with WebGPU compatibility
            shader.AppendLine($"// WebGPU Compatible Compute Shader: {shaderName}");
            shader.AppendLine($"// Generated for Unity 6.2 with WebGPU backend optimization");
            shader.AppendLine($"// Optimization Level: {optimizationLevel}");
            shader.AppendLine();
            
            // Pragma directives for WebGPU compatibility
            shader.AppendLine("#pragma kernel " + kernelName);
            shader.AppendLine("#pragma target 4.5");
            shader.AppendLine("#pragma require compute");
            
            if (optimizationLevel == "high")
            {
                shader.AppendLine("#pragma enable_d3d11_debug_symbols");
                shader.AppendLine("#pragma multi_compile _ UNITY_WEBGPU");
            }
            
            if (enableDebugging)
            {
                shader.AppendLine("#define WEBGPU_DEBUG 1");
            }
            
            shader.AppendLine();
            
            // Buffer declarations
            if (bufferBindings != null && bufferBindings.Count > 0)
            {
                for (int i = 0; i < bufferBindings.Count; i++)
                {
                    var binding = bufferBindings[i];
                    string bufferType = binding.GetValueOrDefault("type", "StructuredBuffer").ToString();
                    string dataType = binding.GetValueOrDefault("dataType", "float4").ToString();
                    string bufferName = binding.GetValueOrDefault("name", $"Buffer{i}").ToString();
                    
                    shader.AppendLine($"{bufferType}<{dataType}> {bufferName};");
                }
            }
            else
            {
                // Default buffers for basic compute operations
                shader.AppendLine("RWStructuredBuffer<float4> Result;");
                shader.AppendLine("StructuredBuffer<float4> Input;");
            }
            
            shader.AppendLine();
            
            // Thread group size
            shader.AppendLine($"[numthreads({threadGroups[0]},{threadGroups[1]},{threadGroups[2]})]");
            shader.AppendLine($"void {kernelName} (uint3 id : SV_DispatchThreadID)");
            shader.AppendLine("{");
            
            // Basic compute shader body optimized for WebGPU
            if (enableDebugging)
            {
                shader.AppendLine("    #ifdef WEBGPU_DEBUG");
                shader.AppendLine("    if (id.x == 0 && id.y == 0 && id.z == 0)");
                shader.AppendLine("    {");
                shader.AppendLine("        // Debug output for first thread");
                shader.AppendLine("    }");
                shader.AppendLine("    #endif");
                shader.AppendLine();
            }
            
            shader.AppendLine("    // WebGPU optimized compute operations");
            shader.AppendLine("    uint index = id.x + id.y * " + threadGroups[0] + " + id.z * " + (threadGroups[0] * threadGroups[1]) + ";");
            shader.AppendLine();
            
            if (bufferBindings != null && bufferBindings.Count > 0)
            {
                shader.AppendLine("    // Custom buffer operations based on bindings");
                foreach (var binding in bufferBindings)
                {
                    string bufferName = binding.Keys.FirstOrDefault() ?? "Buffer";
                    var bufferConfig = binding.Values.FirstOrDefault() as JObject;
                    string operation = bufferConfig?["operation"]?.ToString() ?? "copy";

                    switch (operation.ToLower())
                    {
                        case "multiply":
                            float multiplier = bufferConfig?["multiplier"]?.ToObject<float>() ?? 1.0f;
                            shader.AppendLine($"    if (index < {bufferName}.Length)");
                            shader.AppendLine("    {");
                            shader.AppendLine($"        Result[index] = {bufferName}[index] * {multiplier}f;");
                            shader.AppendLine("    }");
                            break;
                        case "add":
                            float addValue = bufferConfig?["value"]?.ToObject<float>() ?? 0.0f;
                            shader.AppendLine($"    if (index < {bufferName}.Length)");
                            shader.AppendLine("    {");
                            shader.AppendLine($"        Result[index] = {bufferName}[index] + {addValue}f;");
                            shader.AppendLine("    }");
                            break;
                        default:
                            shader.AppendLine($"    if (index < {bufferName}.Length)");
                            shader.AppendLine("    {");
                            shader.AppendLine($"        Result[index] = {bufferName}[index];");
                            shader.AppendLine("    }");
                            break;
                    }
                }
            }
            else
            {
                shader.AppendLine("    // Default operation: copy input to result with processing");
                shader.AppendLine("    if (index < Input.Length)");
                shader.AppendLine("    {");
                shader.AppendLine("        Result[index] = Input[index] * 2.0f; // Example operation");
                shader.AppendLine("    }");
            }
            
            shader.AppendLine("}");
            
            return shader.ToString();
        }

        private static object ModifyComputeShader(string shaderName, string computeKernel, int[] threadGroups, 
            List<Dictionary<string, object>> bufferBindings)
        {
            if (string.IsNullOrEmpty(shaderName))
            {
                return Response.Error("Shader name is required for modification.");
            }

            try
            {
                string shaderPath = $"Assets/Shaders/Compute/{shaderName}.compute";
                if (!File.Exists(shaderPath))
                {
                    return Response.Error($"Compute shader '{shaderName}' not found at {shaderPath}");
                }

                // Read existing shader content
                string existingContent = File.ReadAllText(shaderPath);
                
                // Modify shader based on new parameters
                // This is a simplified modification - in practice, you'd parse and modify specific sections
                string modifiedContent = existingContent;
                
                if (threadGroups != null && threadGroups.Length == 3)
                {
                    // Update thread group size
                    var threadGroupPattern = @"\[numthreads\(\d+,\d+,\d+\)\]";
                    string newThreadGroup = $"[numthreads({threadGroups[0]},{threadGroups[1]},{threadGroups[2]})]";
                    modifiedContent = System.Text.RegularExpressions.Regex.Replace(modifiedContent, threadGroupPattern, newThreadGroup);
                }

                // Write modified content
                File.WriteAllText(shaderPath, modifiedContent);
                AssetDatabase.ImportAsset(shaderPath);
                AssetDatabase.Refresh();

                return Response.Success($"Compute shader '{shaderName}' modified successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to modify compute shader: {e.Message}");
            }
        }

        private static object DeleteComputeShader(string shaderName)
        {
            if (string.IsNullOrEmpty(shaderName))
            {
                return Response.Error("Shader name is required for deletion.");
            }

            try
            {
                string shaderPath = $"Assets/Shaders/Compute/{shaderName}.compute";
                if (!File.Exists(shaderPath))
                {
                    return Response.Error($"Compute shader '{shaderName}' not found at {shaderPath}");
                }

                AssetDatabase.DeleteAsset(shaderPath);
                AssetDatabase.Refresh();

                return Response.Success($"Compute shader '{shaderName}' deleted successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to delete compute shader: {e.Message}");
            }
        }

        private static object ListComputeShaders()
        {
            try
            {
                string shaderDir = "Assets/Shaders/Compute";
                if (!AssetDatabase.IsValidFolder(shaderDir))
                {
                    return Response.Success("No compute shaders found.", new object[0]);
                }

                string[] shaderGuids = AssetDatabase.FindAssets("t:ComputeShader", new[] { shaderDir });
                var shaders = new List<object>();

                foreach (string guid in shaderGuids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    ComputeShader shader = AssetDatabase.LoadAssetAtPath<ComputeShader>(path);
                    
                    if (shader != null)
                    {
                        shaders.Add(new
                        {
                            name = shader.name,
                            path = path,
                            kernelCount = GetComputeShaderKernelCount(shader)
                        });
                    }
                }

                return Response.Success($"Found {shaders.Count} compute shaders.", shaders);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list compute shaders: {e.Message}");
            }
        }

        private static object GetComputeShaderInfo(string shaderName)
        {
            if (string.IsNullOrEmpty(shaderName))
            {
                return Response.Error("Shader name is required.");
            }

            try
            {
                string shaderPath = $"Assets/Shaders/Compute/{shaderName}.compute";
                ComputeShader shader = AssetDatabase.LoadAssetAtPath<ComputeShader>(shaderPath);
                
                if (shader == null)
                {
                    return Response.Error($"Compute shader '{shaderName}' not found.");
                }

                var info = new
                {
                    name = shader.name,
                    path = shaderPath,
                    kernelCount = GetComputeShaderKernelCount(shader),
                    webGPUCompatible = IsWebGPUCompatible(shader),
                    fileSize = new FileInfo(shaderPath).Length
                };

                return Response.Success($"Compute shader '{shaderName}' information retrieved.", info);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get compute shader info: {e.Message}");
            }
        }

        private static int GetComputeShaderKernelCount(ComputeShader shader)
        {
            // Unity doesn't provide a direct way to get kernel count
            // This is a workaround to estimate kernel count
            try
            {
                int count = 0;
                for (int i = 0; i < 32; i++) // Reasonable upper limit
                {
                    try
                    {
                        shader.FindKernel($"Kernel{i}");
                        count++;
                    }
                    catch
                    {
                        break;
                    }
                }
                return Math.Max(1, count); // At least 1 kernel should exist
            }
            catch
            {
                return 1;
            }
        }

        private static bool IsWebGPUCompatible(ComputeShader shader)
        {
            // Check if shader is compatible with WebGPU
            // This is a simplified check - in practice, you'd analyze shader features
            try
            {
                string shaderPath = AssetDatabase.GetAssetPath(shader);
                string content = File.ReadAllText(shaderPath);
                
                // Check for WebGPU compatibility markers
                return content.Contains("#pragma target 4.5") || 
                       content.Contains("UNITY_WEBGPU") ||
                       content.Contains("WebGPU Compatible");
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Handle WebGPU indirect rendering operations.
        /// </summary>
        private static object HandleIndirectRendering(JObject @params)
        {
            try
            {
                string renderType = @params["render_type"]?.ToString() ?? "instanced";
                int instanceCount = @params["instance_count"]?.ToObject<int>() ?? 1000;
                bool enableCulling = @params["enable_culling"]?.ToObject<bool>() ?? true;
                string bufferFormat = @params["buffer_format"]?.ToString() ?? "structured";

                // Validate WebGPU support
                if (!SystemInfo.supportsComputeShaders)
                {
                    return Response.Error("WebGPU indirect rendering requires compute shader support.");
                }

                // Create indirect rendering configuration
                var indirectConfig = new
                {
                    renderType = renderType,
                    instanceCount = instanceCount,
                    enableCulling = enableCulling,
                    bufferFormat = bufferFormat,
                    webgpuFeatures = new[]
                    {
                        "GPU-driven rendering",
                        "Indirect draw calls",
                        "Instance culling",
                        "Dynamic batching"
                    },
                    systemSupport = new
                    {
                        computeShaders = SystemInfo.supportsComputeShaders,
                        asyncCompute = SystemInfo.supportsAsyncCompute,
                        gpuInstancing = SystemInfo.supportsInstancing,
                        indirectDrawing = SystemInfo.supportsInstancing
                    }
                };

                return Response.Success("WebGPU indirect rendering configured successfully.", indirectConfig);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to configure WebGPU indirect rendering: {e.Message}");
            }
        }

        private static object HandleGPUSkinning(JObject @params)
        {
            // Implementation for GPU skinning will be in the next handler
            return Response.Error("GPU skinning handler not yet implemented. Please implement in separate handler.");
        }

        private static object HandleVFXSystem(JObject @params)
        {
            // Implementation for VFX system will be in the next handler
            return Response.Error("VFX system handler not yet implemented. Please implement in separate handler.");
        }

        private static object HandleCompatibilityDetection(JObject @params)
        {
            // Implementation for compatibility detection will be in the next handler
            return Response.Error("Compatibility detection handler not yet implemented. Please implement in separate handler.");
        }
    }
}