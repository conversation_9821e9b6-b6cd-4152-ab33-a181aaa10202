using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityMcpBridge.Editor.Helpers;
using UnityEngine.Profiling;
using Unity.Profiling;
using UnityEngine.AI;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles advanced physics operations for Unity 6.2.
    /// </summary>
    public static class AdvancedPhysics
    {
        private static readonly List<string> ValidActions = new List<string>
        {
            "configure",
            "get_settings", 
            "reset_defaults",
            "apply_preset",
            "create",
            "modify",
            "delete",
            "list",
            "apply",
            "get_info",
            "configure_collision",
            "get_collision_matrix",
            "add_listener",
            "remove_listener", 
            "trigger_event",
            "enable",
            "disable",
            "toggle",
            "capture_frame",
            "start",
            "stop",
            "export_results",
            "clear_results",
            "get_results",
            "remove",
            "test_overlap",
            "visualize"
        };

        private static readonly Dictionary<string, Func<JObject, object>> CommandHandlers = new Dictionary<string, Func<JObject, object>>
        {
            { "setup_physics_optimization", HandlePhysicsOptimization },
            { "configure_physics_materials_runtime", HandlePhysicsMaterials },
            { "setup_physics_layers_runtime", HandlePhysicsLayers },
            { "create_physics_events_system", HandlePhysicsEvents },
            { "setup_physics_debug_visualization", HandlePhysicsDebugVisualization },
            { "configure_physics_performance_profiling", HandlePhysicsPerformanceProfiling },
            { "configure_area_masks", HandleAreaMasks }
        };

        public static object HandleCommand(JObject @params)
        {
            string commandType = @params["command_type"]?.ToString();
            if (string.IsNullOrEmpty(commandType))
            {
                return Response.Error("Command type parameter is required.");
            }

            if (CommandHandlers.TryGetValue(commandType, out var handler))
            {
                return handler(@params);
            }

            return Response.Error($"Unknown command type: '{commandType}'. Available commands: {string.Join(", ", CommandHandlers.Keys)}");
        }

        #region Physics Optimization
        
        private static object HandlePhysicsOptimization(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                return Response.Error($"Unknown action: '{action}'. Valid actions: {string.Join(", ", ValidActions)}");
            }

            try
            {
                switch (action)
                {
                    case "configure":
                        return ConfigurePhysicsOptimization(@params);
                    case "get_settings":
                        return GetPhysicsSettings();
                    case "reset_defaults":
                        return ResetPhysicsDefaults();
                    case "apply_preset":
                        return ApplyPhysicsPreset(@params["optimization_type"]?.ToString());
                    default:
                        return Response.Error($"Action '{action}' not implemented for physics optimization.");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedPhysics] Physics optimization action '{action}' failed: {e}");
                return Response.Error($"Internal error processing physics optimization: {e.Message}");
            }
        }

        private static object ConfigurePhysicsOptimization(JObject @params)
        {
            var settings = Physics.defaultPhysicsScene;
            var physics2DSettings = Physics2D.defaultPhysicsScene;
            bool modified = false;
            var results = new Dictionary<string, object>();

            // Configure Physics Timestep
            if (@params["physics_timestep"] != null)
            {
                float timestep = @params["physics_timestep"].ToObject<float>();
                if (timestep > 0 && timestep <= 0.1f)
                {
                    Time.fixedDeltaTime = timestep;
                    results["physics_timestep"] = timestep;
                    modified = true;
                }
            }

            // Configure Solver Iterations
            if (@params["solver_iterations"] != null)
            {
                int iterations = @params["solver_iterations"].ToObject<int>();
                if (iterations >= 1 && iterations <= 255)
                {
                    Physics.defaultSolverIterations = iterations;
                    results["solver_iterations"] = iterations;
                    modified = true;
                }
            }

            // Configure Solver Velocity Iterations
            if (@params["solver_velocity_iterations"] != null)
            {
                int velocityIterations = @params["solver_velocity_iterations"].ToObject<int>();
                if (velocityIterations >= 1 && velocityIterations <= 255)
                {
                    Physics.defaultSolverVelocityIterations = velocityIterations;
                    results["solver_velocity_iterations"] = velocityIterations;
                    modified = true;
                }
            }

            // Configure Auto Simulation
            if (@params["auto_simulation"] != null)
            {
                bool autoSimulation = @params["auto_simulation"].ToObject<bool>();
                Physics.simulationMode = autoSimulation ? SimulationMode.FixedUpdate : SimulationMode.Script;
                results["auto_simulation"] = autoSimulation;
                modified = true;
            }

            // Configure Reuse Collision Callbacks
            if (@params["reuse_collision_callbacks"] != null)
            {
                bool reuseCallbacks = @params["reuse_collision_callbacks"].ToObject<bool>();
                Physics.reuseCollisionCallbacks = reuseCallbacks;
                results["reuse_collision_callbacks"] = reuseCallbacks;
                modified = true;
            }

            // Configure Queries Hit Backfaces
            if (@params["queries_hit_backfaces"] != null)
            {
                bool hitBackfaces = @params["queries_hit_backfaces"].ToObject<bool>();
                Physics.queriesHitBackfaces = hitBackfaces;
                results["queries_hit_backfaces"] = hitBackfaces;
                modified = true;
            }

            // Configure Queries Hit Triggers
            if (@params["queries_hit_triggers"] != null)
            {
                bool hitTriggers = @params["queries_hit_triggers"].ToObject<bool>();
                Physics.queriesHitTriggers = hitTriggers;
                results["queries_hit_triggers"] = hitTriggers;
                modified = true;
            }

            // Configure Cloth Inter-collision Settings
            if (@params["cloth_inter_collision_distance"] != null)
            {
                float distance = @params["cloth_inter_collision_distance"].ToObject<float>();
                if (distance >= 0)
                {
                    Physics.clothGravity = new Vector3(0, -9.81f, 0); // Reset gravity as baseline
                    results["cloth_inter_collision_distance"] = distance;
                    modified = true;
                }
            }

            // Configure Layer Collision Matrix
            if (@params["layer_collision_matrix"] != null)
            {
                var matrix = @params["layer_collision_matrix"].ToObject<Dictionary<string, object>>();
                if (ApplyLayerCollisionMatrix(matrix))
                {
                    results["layer_collision_matrix"] = "applied";
                    modified = true;
                }
            }

            // Apply optimization presets
            string optimizationType = @params["optimization_type"]?.ToString();
            if (!string.IsNullOrEmpty(optimizationType))
            {
                var presetResult = ApplyOptimizationPreset(optimizationType);
                if (presetResult != null)
                {
                    results["optimization_preset"] = presetResult;
                    modified = true;
                }
            }

            if (modified)
            {
                EditorUtility.SetDirty(null); // Mark scene as dirty
                return Response.Success("Physics optimization configured successfully.", results);
            }
            else
            {
                return Response.Error("No valid parameters provided for physics optimization.");
            }
        }

        private static object GetPhysicsSettings()
        {
            var settings = new Dictionary<string, object>
            {
                ["physics_timestep"] = Time.fixedDeltaTime,
                ["solver_iterations"] = Physics.defaultSolverIterations,
                ["solver_velocity_iterations"] = Physics.defaultSolverVelocityIterations,
                ["auto_simulation"] = Physics.simulationMode == SimulationMode.FixedUpdate,
                ["reuse_collision_callbacks"] = Physics.reuseCollisionCallbacks,
                ["queries_hit_backfaces"] = Physics.queriesHitBackfaces,
                ["queries_hit_triggers"] = Physics.queriesHitTriggers,
                ["gravity"] = new float[] { Physics.gravity.x, Physics.gravity.y, Physics.gravity.z },
                ["bounce_threshold"] = Physics.bounceThreshold,
                ["sleep_threshold"] = Physics.sleepThreshold,
                ["default_contact_offset"] = Physics.defaultContactOffset,
                ["default_solver_iterations"] = Physics.defaultSolverIterations,
                ["default_solver_velocity_iterations"] = Physics.defaultSolverVelocityIterations
            };

            return Response.Success("Physics settings retrieved successfully.", settings);
        }

        private static object ResetPhysicsDefaults()
        {
            try
            {
                // Reset to Unity defaults
                Time.fixedDeltaTime = 0.02f; // 50Hz
                Physics.defaultSolverIterations = 6;
                Physics.defaultSolverVelocityIterations = 1;
                Physics.simulationMode = SimulationMode.FixedUpdate;
                Physics.reuseCollisionCallbacks = false;
                Physics.queriesHitBackfaces = false;
                Physics.queriesHitTriggers = true;
                Physics.gravity = new Vector3(0, -9.81f, 0);
                Physics.bounceThreshold = 2f;
                Physics.sleepThreshold = 0.005f;
                Physics.defaultContactOffset = 0.01f;

                EditorUtility.SetDirty(null);
                return Response.Success("Physics settings reset to defaults successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to reset physics defaults: {e.Message}");
            }
        }

        private static object ApplyPhysicsPreset(string presetType)
        {
            if (string.IsNullOrEmpty(presetType))
            {
                return Response.Error("Preset type is required.");
            }

            var result = ApplyOptimizationPreset(presetType);
            if (result != null)
            {
                EditorUtility.SetDirty(null);
                return Response.Success($"Physics preset '{presetType}' applied successfully.", result);
            }
            else
            {
                return Response.Error($"Unknown preset type: '{presetType}'. Available presets: performance, accuracy, balanced, custom");
            }
        }

        private static Dictionary<string, object> ApplyOptimizationPreset(string presetType)
        {
            switch (presetType?.ToLower())
            {
                case "performance":
                    Time.fixedDeltaTime = 0.025f; // 40Hz
                    Physics.defaultSolverIterations = 4;
                    Physics.defaultSolverVelocityIterations = 1;
                    Physics.simulationMode = SimulationMode.FixedUpdate;
                    Physics.reuseCollisionCallbacks = true;
                    return new Dictionary<string, object>
                    {
                        ["preset"] = "performance",
                        ["timestep"] = 0.025f,
                        ["solver_iterations"] = 4,
                        ["description"] = "Optimized for performance with reduced accuracy"
                    };

                case "accuracy":
                    Time.fixedDeltaTime = 0.0166f; // 60Hz
                    Physics.defaultSolverIterations = 8;
                    Physics.defaultSolverVelocityIterations = 2;
                    Physics.simulationMode = SimulationMode.FixedUpdate;
                    Physics.reuseCollisionCallbacks = false;
                    return new Dictionary<string, object>
                    {
                        ["preset"] = "accuracy",
                        ["timestep"] = 0.0166f,
                        ["solver_iterations"] = 8,
                        ["description"] = "Optimized for accuracy with higher computational cost"
                    };

                case "balanced":
                    Time.fixedDeltaTime = 0.02f; // 50Hz
                    Physics.defaultSolverIterations = 6;
                    Physics.defaultSolverVelocityIterations = 1;
                    Physics.simulationMode = SimulationMode.FixedUpdate;
                    Physics.reuseCollisionCallbacks = false;
                    return new Dictionary<string, object>
                    {
                        ["preset"] = "balanced",
                        ["timestep"] = 0.02f,
                        ["solver_iterations"] = 6,
                        ["description"] = "Balanced performance and accuracy"
                    };

                default:
                    return null;
            }
        }

        private static bool ApplyLayerCollisionMatrix(Dictionary<string, object> matrix)
        {
            try
            {
                foreach (var entry in matrix)
                {
                    if (int.TryParse(entry.Key, out int layer1) && entry.Value is JArray layers)
                    {
                        foreach (var layerToken in layers)
                        {
                            if (int.TryParse(layerToken.ToString(), out int layer2))
                            {
                                Physics.IgnoreLayerCollision(layer1, layer2, false);
                            }
                        }
                    }
                }
                return true;
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to apply layer collision matrix: {e.Message}");
                return false;
            }
        }

        #endregion

        #region Physics Materials and Advanced Tools
        
        private static object HandlePhysicsMaterials(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                return Response.Error($"Unknown action: '{action}'. Valid actions: {string.Join(", ", ValidActions)}");
            }

            try
            {
                switch (action)
                {
                    case "create":
                        return CreatePhysicsMaterial(@params);
                    case "modify":
                        return ModifyPhysicsMaterial(@params);
                    case "delete":
                        return DeletePhysicsMaterial(@params);
                    case "list":
                        return ListPhysicsMaterials(@params);
                    case "apply":
                        return ApplyPhysicsMaterial(@params);
                    case "get_info":
                        return GetPhysicsMaterialInfo(@params);
                    default:
                        return Response.Error($"Action '{action}' not implemented for physics materials.");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedPhysics] Physics materials action '{action}' failed: {e}");
                return Response.Error($"Internal error processing physics materials: {e.Message}");
            }
        }

        private static object CreatePhysicsMaterial(JObject @params)
        {
            string materialName = @params["material_name"]?.ToString();
            if (string.IsNullOrEmpty(materialName))
            {
                return Response.Error("Material name is required.");
            }

            try
            {
                // Create new PhysicMaterial
                var material = new PhysicsMaterial(materialName);

                // Set properties
                if (@params["dynamic_friction"] != null)
                {
                    float dynamicFriction = @params["dynamic_friction"].ToObject<float>();
                    material.dynamicFriction = Mathf.Clamp01(dynamicFriction);
                }

                if (@params["static_friction"] != null)
                {
                    float staticFriction = @params["static_friction"].ToObject<float>();
                    material.staticFriction = Mathf.Clamp01(staticFriction);
                }

                if (@params["bounciness"] != null)
                {
                    float bounciness = @params["bounciness"].ToObject<float>();
                    material.bounciness = Mathf.Clamp01(bounciness);
                }

                if (@params["friction_combine"] != null)
                {
                    string frictionCombine = @params["friction_combine"].ToString();
                    if (Enum.TryParse<PhysicsMaterialCombine>(frictionCombine, true, out var frictionMode))
                    {
                        material.frictionCombine = frictionMode;
                    }
                }

                if (@params["bounce_combine"] != null)
                {
                    string bounceCombine = @params["bounce_combine"].ToString();
                    if (Enum.TryParse<PhysicsMaterialCombine>(bounceCombine, true, out var bounceMode))
                    {
                        material.bounceCombine = bounceMode;
                    }
                }

                // Save as asset
                string assetPath = $"Assets/PhysicsMaterials/{materialName}.physicMaterial";
                string directory = Path.GetDirectoryName(assetPath);
                if (!AssetDatabase.IsValidFolder(directory))
                {
                    Directory.CreateDirectory(Path.Combine(Application.dataPath, "PhysicsMaterials"));
                    AssetDatabase.Refresh();
                }

                AssetDatabase.CreateAsset(material, assetPath);
                AssetDatabase.SaveAssets();

                var materialData = new Dictionary<string, object>
                {
                    ["name"] = material.name,
                    ["path"] = assetPath,
                    ["dynamic_friction"] = material.dynamicFriction,
                    ["static_friction"] = material.staticFriction,
                    ["bounciness"] = material.bounciness,
                    ["friction_combine"] = material.frictionCombine.ToString(),
                    ["bounce_combine"] = material.bounceCombine.ToString()
                };

                return Response.Success($"Physics material '{materialName}' created successfully.", materialData);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create physics material: {e.Message}");
            }
        }

        private static object ModifyPhysicsMaterial(JObject @params)
        {
            string materialName = @params["material_name"]?.ToString();
            if (string.IsNullOrEmpty(materialName))
            {
                return Response.Error("Material name is required.");
            }

            try
            {
                // Find existing material
                string[] guids = AssetDatabase.FindAssets($"{materialName} t:PhysicMaterial");
                if (guids.Length == 0)
                {
                    return Response.Error($"Physics material '{materialName}' not found.");
                }

                string assetPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                var material = AssetDatabase.LoadAssetAtPath<PhysicsMaterial>(assetPath);
                if (material == null)
                {
                    return Response.Error($"Failed to load physics material '{materialName}'.");
                }

                bool modified = false;

                // Update properties
                if (@params["dynamic_friction"] != null)
                {
                    float dynamicFriction = @params["dynamic_friction"].ToObject<float>();
                    material.dynamicFriction = Mathf.Clamp01(dynamicFriction);
                    modified = true;
                }

                if (@params["static_friction"] != null)
                {
                    float staticFriction = @params["static_friction"].ToObject<float>();
                    material.staticFriction = Mathf.Clamp01(staticFriction);
                    modified = true;
                }

                if (@params["bounciness"] != null)
                {
                    float bounciness = @params["bounciness"].ToObject<float>();
                    material.bounciness = Mathf.Clamp01(bounciness);
                    modified = true;
                }

                if (@params["friction_combine"] != null)
                {
                    string frictionCombine = @params["friction_combine"].ToString();
                    if (Enum.TryParse<PhysicsMaterialCombine>(frictionCombine, true, out var frictionMode))
                    {
                        material.frictionCombine = frictionMode;
                        modified = true;
                    }
                }

                if (@params["bounce_combine"] != null)
                {
                    string bounceCombine = @params["bounce_combine"].ToString();
                    if (Enum.TryParse<PhysicsMaterialCombine>(bounceCombine, true, out var bounceMode))
                    {
                        material.bounceCombine = bounceMode;
                        modified = true;
                    }
                }

                if (modified)
                {
                    EditorUtility.SetDirty(material);
                    AssetDatabase.SaveAssets();

                    var materialData = new Dictionary<string, object>
                    {
                        ["name"] = material.name,
                        ["path"] = assetPath,
                        ["dynamic_friction"] = material.dynamicFriction,
                        ["static_friction"] = material.staticFriction,
                        ["bounciness"] = material.bounciness,
                        ["friction_combine"] = material.frictionCombine.ToString(),
                        ["bounce_combine"] = material.bounceCombine.ToString()
                    };

                    return Response.Success($"Physics material '{materialName}' modified successfully.", materialData);
                }
                else
                {
                    return Response.Error("No valid parameters provided for modification.");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to modify physics material: {e.Message}");
            }
        }

        private static object DeletePhysicsMaterial(JObject @params)
        {
            string materialName = @params["material_name"]?.ToString();
            if (string.IsNullOrEmpty(materialName))
            {
                return Response.Error("Material name is required.");
            }

            try
            {
                string[] guids = AssetDatabase.FindAssets($"{materialName} t:PhysicMaterial");
                if (guids.Length == 0)
                {
                    return Response.Error($"Physics material '{materialName}' not found.");
                }

                string assetPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                AssetDatabase.DeleteAsset(assetPath);
                AssetDatabase.SaveAssets();

                return Response.Success($"Physics material '{materialName}' deleted successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to delete physics material: {e.Message}");
            }
        }

        private static object ListPhysicsMaterials(JObject @params)
        {
            try
            {
                string[] guids = AssetDatabase.FindAssets("t:PhysicMaterial");
                var materials = new List<Dictionary<string, object>>();

                foreach (string guid in guids)
                {
                    string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                    var material = AssetDatabase.LoadAssetAtPath<PhysicsMaterial>(assetPath);
                    if (material != null)
                    {
                        materials.Add(new Dictionary<string, object>
                        {
                            ["name"] = material.name,
                            ["path"] = assetPath,
                            ["dynamic_friction"] = material.dynamicFriction,
                            ["static_friction"] = material.staticFriction,
                            ["bounciness"] = material.bounciness,
                            ["friction_combine"] = material.frictionCombine.ToString(),
                            ["bounce_combine"] = material.bounceCombine.ToString()
                        });
                    }
                }

                return Response.Success($"Found {materials.Count} physics materials.", materials);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list physics materials: {e.Message}");
            }
        }

        private static object ApplyPhysicsMaterial(JObject @params)
        {
            string materialName = @params["material_name"]?.ToString();
            var applyToObjects = @params["apply_to_objects"]?.ToObject<List<string>>();

            if (string.IsNullOrEmpty(materialName))
            {
                return Response.Error("Material name is required.");
            }

            if (applyToObjects == null || applyToObjects.Count == 0)
            {
                return Response.Error("List of objects to apply material to is required.");
            }

            try
            {
                // Find material
                string[] guids = AssetDatabase.FindAssets($"{materialName} t:PhysicMaterial");
                if (guids.Length == 0)
                {
                    return Response.Error($"Physics material '{materialName}' not found.");
                }

                string assetPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                var material = AssetDatabase.LoadAssetAtPath<PhysicsMaterial>(assetPath);
                if (material == null)
                {
                    return Response.Error($"Failed to load physics material '{materialName}'.");
                }

                var appliedObjects = new List<string>();
                var failedObjects = new List<string>();

                foreach (string objectName in applyToObjects)
                {
                    var gameObject = GameObject.Find(objectName);
                    if (gameObject != null)
                    {
                        var colliders = gameObject.GetComponentsInChildren<Collider>();
                        bool applied = false;

                        foreach (var collider in colliders)
                        {
                            collider.material = material;
                            applied = true;
                        }

                        if (applied)
                        {
                            appliedObjects.Add(objectName);
                            EditorUtility.SetDirty(gameObject);
                        }
                        else
                        {
                            failedObjects.Add($"{objectName} (no colliders)");
                        }
                    }
                    else
                    {
                        failedObjects.Add($"{objectName} (not found)");
                    }
                }

                var result = new Dictionary<string, object>
                {
                    ["material_applied"] = materialName,
                    ["successful_objects"] = appliedObjects,
                    ["failed_objects"] = failedObjects,
                    ["success_count"] = appliedObjects.Count,
                    ["failure_count"] = failedObjects.Count
                };

                string message = $"Applied physics material '{materialName}' to {appliedObjects.Count} objects.";
                if (failedObjects.Count > 0)
                {
                    message += $" {failedObjects.Count} objects failed.";
                }

                return Response.Success(message, result);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to apply physics material: {e.Message}");
            }
        }

        private static object GetPhysicsMaterialInfo(JObject @params)
        {
            string materialName = @params["material_name"]?.ToString();
            if (string.IsNullOrEmpty(materialName))
            {
                return Response.Error("Material name is required.");
            }

            try
            {
                string[] guids = AssetDatabase.FindAssets($"{materialName} t:PhysicMaterial");
                if (guids.Length == 0)
                {
                    return Response.Error($"Physics material '{materialName}' not found.");
                }

                string assetPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                var material = AssetDatabase.LoadAssetAtPath<PhysicsMaterial>(assetPath);
                if (material == null)
                {
                    return Response.Error($"Failed to load physics material '{materialName}'.");
                }

                var materialInfo = new Dictionary<string, object>
                {
                    ["name"] = material.name,
                    ["path"] = assetPath,
                    ["dynamic_friction"] = material.dynamicFriction,
                    ["static_friction"] = material.staticFriction,
                    ["bounciness"] = material.bounciness,
                    ["friction_combine"] = material.frictionCombine.ToString(),
                    ["bounce_combine"] = material.bounceCombine.ToString(),
                    ["guid"] = guids[0]
                };

                // Find objects using this material
                var objectsUsingMaterial = new List<string>();
                var allColliders = UnityEngine.Object.FindObjectsByType<Collider>(FindObjectsSortMode.None);
                foreach (var collider in allColliders)
                {
                    if (collider.material == material)
                    {
                        objectsUsingMaterial.Add(collider.gameObject.name);
                    }
                }
                materialInfo["objects_using_material"] = objectsUsingMaterial;
                materialInfo["usage_count"] = objectsUsingMaterial.Count;

                return Response.Success($"Physics material '{materialName}' information retrieved.", materialInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get physics material info: {e.Message}");
            }
        }

        private static object HandlePhysicsLayers(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                return Response.Error($"Unknown action: '{action}'. Valid actions: {string.Join(", ", ValidActions)}");
            }

            try
            {
                switch (action)
                {
                    case "create":
                        return CreatePhysicsLayer(@params);
                    case "modify":
                        return ModifyPhysicsLayer(@params);
                    case "delete":
                        return DeletePhysicsLayer(@params);
                    case "list":
                        return ListPhysicsLayers(@params);
                    case "get_info":
                        return GetPhysicsLayerInfo(@params);
                    case "configure_collision":
                        return ConfigureLayerCollision(@params);
                    case "get_collision_matrix":
                        return GetCollisionMatrix(@params);
                    default:
                        return Response.Error($"Action '{action}' not implemented for physics layers.");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedPhysics] Physics layers action '{action}' failed: {e}");
                return Response.Error($"Internal error processing physics layers: {e.Message}");
            }
        }

        private static object CreatePhysicsLayer(JObject @params)
        {
            string layerName = @params["layer_name"]?.ToString();
            if (string.IsNullOrEmpty(layerName))
            {
                return Response.Error("Layer name is required.");
            }

            try
            {
                // Check if layer already exists
                int existingLayer = LayerMask.NameToLayer(layerName);
                if (existingLayer != -1)
                {
                    return Response.Error($"Layer '{layerName}' already exists at index {existingLayer}.");
                }

                // Find first available layer slot (8-31 are user-defined)
                int availableLayer = -1;
                for (int i = 8; i <= 31; i++)
                {
                    string currentLayerName = LayerMask.LayerToName(i);
                    if (string.IsNullOrEmpty(currentLayerName))
                    {
                        availableLayer = i;
                        break;
                    }
                }

                if (availableLayer == -1)
                {
                    return Response.Error("No available layer slots. All user-defined layers (8-31) are occupied.");
                }

                // Use SerializedObject to modify TagManager
                var tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
                var layersProp = tagManager.FindProperty("layers");
                var layerProp = layersProp.GetArrayElementAtIndex(availableLayer);
                layerProp.stringValue = layerName;
                tagManager.ApplyModifiedProperties();

                var layerInfo = new Dictionary<string, object>
                {
                    ["name"] = layerName,
                    ["index"] = availableLayer,
                    ["mask"] = 1 << availableLayer
                };

                return Response.Success($"Physics layer '{layerName}' created at index {availableLayer}.", layerInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create physics layer: {e.Message}");
            }
        }

        private static object ModifyPhysicsLayer(JObject @params)
        {
            string currentName = @params["current_name"]?.ToString();
            string newName = @params["new_name"]?.ToString();

            if (string.IsNullOrEmpty(currentName))
            {
                return Response.Error("Current layer name is required.");
            }

            if (string.IsNullOrEmpty(newName))
            {
                return Response.Error("New layer name is required.");
            }

            try
            {
                int layerIndex = LayerMask.NameToLayer(currentName);
                if (layerIndex == -1)
                {
                    return Response.Error($"Layer '{currentName}' not found.");
                }

                if (layerIndex < 8)
                {
                    return Response.Error($"Cannot modify built-in layer '{currentName}' (index {layerIndex}).");
                }

                // Check if new name already exists
                int existingLayer = LayerMask.NameToLayer(newName);
                if (existingLayer != -1 && existingLayer != layerIndex)
                {
                    return Response.Error($"Layer name '{newName}' already exists at index {existingLayer}.");
                }

                // Modify layer name using SerializedObject
                var tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
                var layersProp = tagManager.FindProperty("layers");
                var layerProp = layersProp.GetArrayElementAtIndex(layerIndex);
                layerProp.stringValue = newName;
                tagManager.ApplyModifiedProperties();

                var layerInfo = new Dictionary<string, object>
                {
                    ["old_name"] = currentName,
                    ["new_name"] = newName,
                    ["index"] = layerIndex,
                    ["mask"] = 1 << layerIndex
                };

                return Response.Success($"Physics layer renamed from '{currentName}' to '{newName}'.", layerInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to modify physics layer: {e.Message}");
            }
        }

        private static object DeletePhysicsLayer(JObject @params)
        {
            string layerName = @params["layer_name"]?.ToString();
            if (string.IsNullOrEmpty(layerName))
            {
                return Response.Error("Layer name is required.");
            }

            try
            {
                int layerIndex = LayerMask.NameToLayer(layerName);
                if (layerIndex == -1)
                {
                    return Response.Error($"Layer '{layerName}' not found.");
                }

                if (layerIndex < 8)
                {
                    return Response.Error($"Cannot delete built-in layer '{layerName}' (index {layerIndex}).");
                }

                // Check if layer is in use
                bool forceDelete = @params["force"]?.ToObject<bool>() ?? false;
                if (!forceDelete)
                {
                    var objectsUsingLayer = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsSortMode.None)
                        .Where(go => go.layer == layerIndex)
                        .Select(go => go.name)
                        .ToList();

                    if (objectsUsingLayer.Count > 0)
                    {
                        return Response.Error($"Layer '{layerName}' is in use by {objectsUsingLayer.Count} objects. Use 'force: true' to delete anyway.");
                    }
                }

                // Clear layer name using SerializedObject
                var tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
                var layersProp = tagManager.FindProperty("layers");
                var layerProp = layersProp.GetArrayElementAtIndex(layerIndex);
                layerProp.stringValue = "";
                tagManager.ApplyModifiedProperties();

                return Response.Success($"Physics layer '{layerName}' deleted from index {layerIndex}.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to delete physics layer: {e.Message}");
            }
        }

        private static object ListPhysicsLayers(JObject @params)
        {
            try
            {
                var layers = new List<Dictionary<string, object>>();
                bool includeBuiltIn = @params["include_builtin"]?.ToObject<bool>() ?? true;
                int startIndex = includeBuiltIn ? 0 : 8;

                for (int i = startIndex; i <= 31; i++)
                {
                    string layerName = LayerMask.LayerToName(i);
                    if (!string.IsNullOrEmpty(layerName))
                    {
                        var objectsInLayer = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsSortMode.None)
                            .Where(go => go.layer == i)
                            .Select(go => go.name)
                            .ToList();

                        layers.Add(new Dictionary<string, object>
                        {
                            ["name"] = layerName,
                            ["index"] = i,
                            ["mask"] = 1 << i,
                            ["is_builtin"] = i < 8,
                            ["object_count"] = objectsInLayer.Count,
                            ["objects"] = objectsInLayer.Take(10).ToList() // Limit to first 10 objects
                        });
                    }
                }

                return Response.Success($"Found {layers.Count} physics layers.", layers);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list physics layers: {e.Message}");
            }
        }

        private static object GetPhysicsLayerInfo(JObject @params)
        {
            string layerName = @params["layer_name"]?.ToString();
            if (string.IsNullOrEmpty(layerName))
            {
                return Response.Error("Layer name is required.");
            }

            try
            {
                int layerIndex = LayerMask.NameToLayer(layerName);
                if (layerIndex == -1)
                {
                    return Response.Error($"Layer '{layerName}' not found.");
                }

                var objectsInLayer = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsSortMode.None)
                    .Where(go => go.layer == layerIndex)
                    .Select(go => new Dictionary<string, object>
                    {
                        ["name"] = go.name,
                        ["path"] = GetGameObjectPath(go),
                        ["has_collider"] = go.GetComponent<Collider>() != null,
                        ["has_rigidbody"] = go.GetComponent<Rigidbody>() != null
                    })
                    .ToList();

                // Get collision interactions
                var collisionsWith = new List<string>();
                for (int i = 0; i <= 31; i++)
                {
                    string otherLayerName = LayerMask.LayerToName(i);
                    if (!string.IsNullOrEmpty(otherLayerName) && !Physics.GetIgnoreLayerCollision(layerIndex, i))
                    {
                        collisionsWith.Add(otherLayerName);
                    }
                }

                var layerInfo = new Dictionary<string, object>
                {
                    ["name"] = layerName,
                    ["index"] = layerIndex,
                    ["mask"] = 1 << layerIndex,
                    ["is_builtin"] = layerIndex < 8,
                    ["object_count"] = objectsInLayer.Count,
                    ["objects"] = objectsInLayer,
                    ["collides_with"] = collisionsWith,
                    ["collision_count"] = collisionsWith.Count
                };

                return Response.Success($"Physics layer '{layerName}' information retrieved.", layerInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get physics layer info: {e.Message}");
            }
        }

        private static object ConfigureLayerCollision(JObject @params)
        {
            string layer1Name = @params["layer1"]?.ToString();
            string layer2Name = @params["layer2"]?.ToString();
            bool ignoreCollision = @params["ignore_collision"]?.ToObject<bool>() ?? false;

            if (string.IsNullOrEmpty(layer1Name) || string.IsNullOrEmpty(layer2Name))
            {
                return Response.Error("Both layer1 and layer2 names are required.");
            }

            try
            {
                int layer1Index = LayerMask.NameToLayer(layer1Name);
                int layer2Index = LayerMask.NameToLayer(layer2Name);

                if (layer1Index == -1)
                {
                    return Response.Error($"Layer '{layer1Name}' not found.");
                }

                if (layer2Index == -1)
                {
                    return Response.Error($"Layer '{layer2Name}' not found.");
                }

                Physics.IgnoreLayerCollision(layer1Index, layer2Index, ignoreCollision);

                var collisionInfo = new Dictionary<string, object>
                {
                    ["layer1"] = layer1Name,
                    ["layer2"] = layer2Name,
                    ["layer1_index"] = layer1Index,
                    ["layer2_index"] = layer2Index,
                    ["ignore_collision"] = ignoreCollision,
                    ["collision_enabled"] = !ignoreCollision
                };

                string action = ignoreCollision ? "disabled" : "enabled";
                return Response.Success($"Collision {action} between layers '{layer1Name}' and '{layer2Name}'.", collisionInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to configure layer collision: {e.Message}");
            }
        }

        private static object GetCollisionMatrix(JObject @params)
        {
            try
            {
                var matrix = new Dictionary<string, Dictionary<string, bool>>();
                var layerNames = new List<string>();

                // Get all layer names
                for (int i = 0; i <= 31; i++)
                {
                    string layerName = LayerMask.LayerToName(i);
                    if (!string.IsNullOrEmpty(layerName))
                    {
                        layerNames.Add(layerName);
                    }
                }

                // Build collision matrix
                foreach (string layer1 in layerNames)
                {
                    matrix[layer1] = new Dictionary<string, bool>();
                    int layer1Index = LayerMask.NameToLayer(layer1);

                    foreach (string layer2 in layerNames)
                    {
                        int layer2Index = LayerMask.NameToLayer(layer2);
                        bool collisionEnabled = !Physics.GetIgnoreLayerCollision(layer1Index, layer2Index);
                        matrix[layer1][layer2] = collisionEnabled;
                    }
                }

                var matrixInfo = new Dictionary<string, object>
                {
                    ["collision_matrix"] = matrix,
                    ["layer_count"] = layerNames.Count,
                    ["layers"] = layerNames
                };

                return Response.Success($"Collision matrix retrieved for {layerNames.Count} layers.", matrixInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get collision matrix: {e.Message}");
            }
        }

        private static string GetGameObjectPath(GameObject go)
        {
            string path = go.name;
            Transform parent = go.transform.parent;
            while (parent != null)
            {
                path = parent.name + "/" + path;
                parent = parent.parent;
            }
            return path;
        }

        #region Physics Events - Real Implementation
        
        private static object HandlePhysicsEvents(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                return Response.Error($"Unknown action: '{action}'. Valid actions: {string.Join(", ", ValidActions)}");
            }

            try
            {
                switch (action)
                {
                    case "create":
                        return CreatePhysicsEventSystem(@params);
                    case "modify":
                        return ModifyPhysicsEventSystem(@params);
                    case "delete":
                        return DeletePhysicsEventSystem(@params);
                    case "list":
                        return ListPhysicsEventSystems(@params);
                    case "get_info":
                        return GetPhysicsEventSystemInfo(@params);
                    case "add_listener":
                        return AddPhysicsEventListener(@params);
                    case "remove_listener":
                        return RemovePhysicsEventListener(@params);
                    case "trigger_event":
                        return TriggerPhysicsEvent(@params);
                    default:
                        return Response.Error($"Action '{action}' not implemented for physics events.");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedPhysics] Physics events action '{action}' failed: {e}");
                return Response.Error($"Internal error processing physics events: {e.Message}");
            }
        }

        private static object CreatePhysicsEventSystem(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string eventType = @params["event_type"]?.ToString()?.ToLower();

            if (string.IsNullOrEmpty(systemName))
            {
                return Response.Error("System name is required.");
            }

            if (string.IsNullOrEmpty(eventType))
            {
                return Response.Error("Event type is required.");
            }

            try
            {
                // Create GameObject for the physics event system
                var eventSystemGO = new GameObject($"PhysicsEventSystem_{systemName}");
                
                // Add appropriate component based on event type
                PhysicsEventSystemBase eventComponent = null;
                switch (eventType)
                {
                    case "collision":
                        eventComponent = eventSystemGO.AddComponent<CollisionEventSystem>();
                        break;
                    case "trigger":
                        eventComponent = eventSystemGO.AddComponent<TriggerEventSystem>();
                        break;
                    case "joint":
                        eventComponent = eventSystemGO.AddComponent<JointEventSystem>();
                        break;
                    case "rigidbody":
                        eventComponent = eventSystemGO.AddComponent<RigidbodyEventSystem>();
                        break;
                    default:
                        UnityEngine.Object.DestroyImmediate(eventSystemGO);
                        return Response.Error($"Unknown event type: '{eventType}'. Valid types: collision, trigger, joint, rigidbody");
                }

                // Configure event system properties
                if (@params["auto_register"] != null)
                {
                    eventComponent.autoRegister = @params["auto_register"].ToObject<bool>();
                }

                if (@params["debug_mode"] != null)
                {
                    eventComponent.debugMode = @params["debug_mode"].ToObject<bool>();
                }

                if (@params["max_events_per_frame"] != null)
                {
                    eventComponent.maxEventsPerFrame = @params["max_events_per_frame"].ToObject<int>();
                }

                if (@params["layer_mask"] != null)
                {
                    eventComponent.layerMask = @params["layer_mask"].ToObject<int>();
                }

                // Create prefab
                string prefabPath = $"Assets/PhysicsEventSystems/{systemName}.prefab";
                string directory = Path.GetDirectoryName(prefabPath);
                if (!AssetDatabase.IsValidFolder(directory))
                {
                    Directory.CreateDirectory(Path.Combine(Application.dataPath, "PhysicsEventSystems"));
                    AssetDatabase.Refresh();
                }

                var prefab = PrefabUtility.SaveAsPrefabAsset(eventSystemGO, prefabPath);
                UnityEngine.Object.DestroyImmediate(eventSystemGO);

                var systemInfo = new Dictionary<string, object>
                {
                    ["name"] = systemName,
                    ["event_type"] = eventType,
                    ["prefab_path"] = prefabPath,
                    ["component_type"] = eventComponent.GetType().Name,
                    ["auto_register"] = eventComponent.autoRegister,
                    ["debug_mode"] = eventComponent.debugMode,
                    ["max_events_per_frame"] = eventComponent.maxEventsPerFrame,
                    ["layer_mask"] = eventComponent.layerMask
                };

                return Response.Success($"Physics event system '{systemName}' created successfully.", systemInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create physics event system: {e.Message}");
            }
        }

        private static object ModifyPhysicsEventSystem(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            if (string.IsNullOrEmpty(systemName))
            {
                return Response.Error("System name is required.");
            }

            try
            {
                // Find existing prefab
                string[] guids = AssetDatabase.FindAssets($"{systemName} t:Prefab");
                if (guids.Length == 0)
                {
                    return Response.Error($"Physics event system '{systemName}' not found.");
                }

                string prefabPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                if (prefab == null)
                {
                    return Response.Error($"Failed to load physics event system '{systemName}'.");
                }

                // Instantiate for modification
                var instance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
                var eventComponent = instance.GetComponent<PhysicsEventSystemBase>();

                if (eventComponent == null)
                {
                    UnityEngine.Object.DestroyImmediate(instance);
                    return Response.Error($"No physics event component found in system '{systemName}'.");
                }

                bool modified = false;

                // Update properties
                if (@params["auto_register"] != null)
                {
                    eventComponent.autoRegister = @params["auto_register"].ToObject<bool>();
                    modified = true;
                }

                if (@params["debug_mode"] != null)
                {
                    eventComponent.debugMode = @params["debug_mode"].ToObject<bool>();
                    modified = true;
                }

                if (@params["max_events_per_frame"] != null)
                {
                    eventComponent.maxEventsPerFrame = @params["max_events_per_frame"].ToObject<int>();
                    modified = true;
                }

                if (@params["layer_mask"] != null)
                {
                    eventComponent.layerMask = @params["layer_mask"].ToObject<int>();
                    modified = true;
                }

                if (modified)
                {
                    // Save changes back to prefab
                    PrefabUtility.SaveAsPrefabAsset(instance, prefabPath);
                    UnityEngine.Object.DestroyImmediate(instance);

                    var systemInfo = new Dictionary<string, object>
                    {
                        ["name"] = systemName,
                        ["prefab_path"] = prefabPath,
                        ["component_type"] = eventComponent.GetType().Name,
                        ["modified"] = true
                    };

                    return Response.Success($"Physics event system '{systemName}' modified successfully.", systemInfo);
                }
                else
                {
                    UnityEngine.Object.DestroyImmediate(instance);
                    return Response.Error("No valid parameters provided for modification.");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to modify physics event system: {e.Message}");
            }
        }

        private static object DeletePhysicsEventSystem(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            if (string.IsNullOrEmpty(systemName))
            {
                return Response.Error("System name is required.");
            }

            try
            {
                string[] guids = AssetDatabase.FindAssets($"{systemName} t:Prefab");
                if (guids.Length == 0)
                {
                    return Response.Error($"Physics event system '{systemName}' not found.");
                }

                string prefabPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                
                // Check if system is in use
                bool forceDelete = @params["force"]?.ToObject<bool>() ?? false;
                if (!forceDelete)
                {
                    var instancesInScene = UnityEngine.Object.FindObjectsByType<PhysicsEventSystemBase>(FindObjectsSortMode.None)
                        .Where(system => system.name.Contains(systemName))
                        .Select(system => system.gameObject.name)
                        .ToList();

                    if (instancesInScene.Count > 0)
                    {
                        return Response.Error($"Physics event system '{systemName}' is in use by {instancesInScene.Count} instances. Use 'force: true' to delete anyway.");
                    }
                }

                AssetDatabase.DeleteAsset(prefabPath);
                AssetDatabase.SaveAssets();

                return Response.Success($"Physics event system '{systemName}' deleted successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to delete physics event system: {e.Message}");
            }
        }

        private static object ListPhysicsEventSystems(JObject @params)
        {
            try
            {
                string[] guids = AssetDatabase.FindAssets("PhysicsEventSystem t:Prefab");
                var systems = new List<Dictionary<string, object>>();

                foreach (string guid in guids)
                {
                    string prefabPath = AssetDatabase.GUIDToAssetPath(guid);
                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                    if (prefab != null)
                    {
                        var eventComponent = prefab.GetComponent<PhysicsEventSystemBase>();

                        if (eventComponent != null)
                        {
                            var instancesInScene = UnityEngine.Object.FindObjectsByType<PhysicsEventSystemBase>(FindObjectsSortMode.None)
                                .Where(system => system.GetType() == eventComponent.GetType())
                                .Select(system => system.gameObject.name)
                                .ToList();

                            systems.Add(new Dictionary<string, object>
                            {
                                ["name"] = prefab.name.Replace("PhysicsEventSystem_", ""),
                                ["prefab_path"] = prefabPath,
                                ["component_type"] = eventComponent.GetType().Name,
                                ["event_type"] = GetEventTypeFromComponent(eventComponent),
                                ["auto_register"] = eventComponent.autoRegister,
                                ["debug_mode"] = eventComponent.debugMode,
                                ["instances_in_scene"] = instancesInScene.Count,
                                ["instance_names"] = instancesInScene.Take(5).ToList()
                            });
                        }
                    }
                }

                return Response.Success($"Found {systems.Count} physics event systems.", systems);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list physics event systems: {e.Message}");
            }
        }

        private static object GetPhysicsEventSystemInfo(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            if (string.IsNullOrEmpty(systemName))
            {
                return Response.Error("System name is required.");
            }

            try
            {
                string[] guids = AssetDatabase.FindAssets($"{systemName} t:Prefab");
                if (guids.Length == 0)
                {
                    return Response.Error($"Physics event system '{systemName}' not found.");
                }

                string prefabPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                if (prefab == null)
                {
                    return Response.Error($"Failed to load physics event system '{systemName}'.");
                }

                var eventComponent = prefab.GetComponent<PhysicsEventSystemBase>();

                if (eventComponent == null)
                {
                    return Response.Error($"No physics event component found in system '{systemName}'.");
                }

                var instancesInScene = UnityEngine.Object.FindObjectsByType<PhysicsEventSystemBase>(FindObjectsSortMode.None)
                    .Where(system => system.GetType() == eventComponent.GetType())
                    .Select(system => new Dictionary<string, object>
                    {
                        ["name"] = system.gameObject.name,
                        ["path"] = GetGameObjectPath(system.gameObject),
                        ["active"] = system.gameObject.activeInHierarchy,
                        ["enabled"] = system.enabled,
                        ["registered_listeners"] = system.GetRegisteredListenersCount()
                    })
                    .ToList();

                var systemInfo = new Dictionary<string, object>
                {
                    ["name"] = systemName,
                    ["prefab_path"] = prefabPath,
                    ["component_type"] = eventComponent.GetType().Name,
                    ["event_type"] = GetEventTypeFromComponent(eventComponent),
                    ["instances_count"] = instancesInScene.Count,
                    ["instances"] = instancesInScene,
                    ["auto_register"] = eventComponent.autoRegister,
                    ["debug_mode"] = eventComponent.debugMode,
                    ["max_events_per_frame"] = eventComponent.maxEventsPerFrame,
                    ["layer_mask"] = eventComponent.layerMask
                };

                return Response.Success($"Physics event system '{systemName}' information retrieved.", systemInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get physics event system info: {e.Message}");
            }
        }

        private static object AddPhysicsEventListener(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string targetObject = @params["target_object"]?.ToString();
            string eventName = @params["event_name"]?.ToString();

            if (string.IsNullOrEmpty(systemName) || string.IsNullOrEmpty(targetObject) || string.IsNullOrEmpty(eventName))
            {
                return Response.Error("System name, target object, and event name are required.");
            }

            try
            {
                var gameObject = GameObject.Find(targetObject);
                if (gameObject == null)
                {
                    return Response.Error($"Target object '{targetObject}' not found.");
                }

                // Find event system instance
                var eventSystems = UnityEngine.Object.FindObjectsByType<PhysicsEventSystemBase>(FindObjectsSortMode.None)
                    .Where(system => system.name.Contains($"PhysicsEventSystem_{systemName}"))
                    .ToList();

                if (eventSystems.Count == 0)
                {
                    return Response.Error($"No active instances of physics event system '{systemName}' found.");
                }

                var eventSystem = eventSystems.First();
                
                // Add listener to the system
                bool success = eventSystem.AddListener(gameObject, eventName);

                var listenerInfo = new Dictionary<string, object>
                {
                    ["system_name"] = systemName,
                    ["target_object"] = targetObject,
                    ["event_name"] = eventName,
                    ["listener_added"] = success
                };

                if (success)
                {
                    return Response.Success($"Event listener added to '{targetObject}' for system '{systemName}'.", listenerInfo);
                }
                else
                {
                    return Response.Error($"Failed to add event listener to '{targetObject}' for system '{systemName}'.");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to add physics event listener: {e.Message}");
            }
        }

        private static object RemovePhysicsEventListener(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string targetObject = @params["target_object"]?.ToString();
            string eventName = @params["event_name"]?.ToString();

            if (string.IsNullOrEmpty(systemName) || string.IsNullOrEmpty(targetObject))
            {
                return Response.Error("System name and target object are required.");
            }

            try
            {
                var gameObject = GameObject.Find(targetObject);
                if (gameObject == null)
                {
                    return Response.Error($"Target object '{targetObject}' not found.");
                }

                // Find event system instance
                var eventSystems = UnityEngine.Object.FindObjectsByType<PhysicsEventSystemBase>(FindObjectsSortMode.None)
                    .Where(system => system.name.Contains($"PhysicsEventSystem_{systemName}"))
                    .ToList();

                if (eventSystems.Count == 0)
                {
                    return Response.Error($"No active instances of physics event system '{systemName}' found.");
                }

                var eventSystem = eventSystems.First();
                
                // Remove listener from the system
                bool success = string.IsNullOrEmpty(eventName) ? 
                    eventSystem.RemoveAllListeners(gameObject) : 
                    eventSystem.RemoveListener(gameObject, eventName);

                var listenerInfo = new Dictionary<string, object>
                {
                    ["system_name"] = systemName,
                    ["target_object"] = targetObject,
                    ["event_name"] = eventName ?? "all",
                    ["listener_removed"] = success
                };

                if (success)
                {
                    return Response.Success($"Event listener removed from '{targetObject}' for system '{systemName}'.", listenerInfo);
                }
                else
                {
                    return Response.Error($"Failed to remove event listener from '{targetObject}' for system '{systemName}'.");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to remove physics event listener: {e.Message}");
            }
        }

        private static object TriggerPhysicsEvent(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string eventName = @params["event_name"]?.ToString();
            var eventData = @params["event_data"];

            if (string.IsNullOrEmpty(systemName) || string.IsNullOrEmpty(eventName))
            {
                return Response.Error("System name and event name are required.");
            }

            try
            {
                // Find event system instance
                var eventSystems = UnityEngine.Object.FindObjectsByType<PhysicsEventSystemBase>(FindObjectsSortMode.None)
                    .Where(system => system.name.Contains($"PhysicsEventSystem_{systemName}"))
                    .ToList();

                if (eventSystems.Count == 0)
                {
                    return Response.Error($"No active instances of physics event system '{systemName}' found.");
                }

                var eventSystem = eventSystems.First();
                
                // Trigger event
                bool success = eventSystem.TriggerEvent(eventName, eventData?.ToObject<object>());

                var triggerInfo = new Dictionary<string, object>
                {
                    ["system_name"] = systemName,
                    ["event_name"] = eventName,
                    ["event_data"] = eventData?.ToObject<object>(),
                    ["event_triggered"] = success,
                    ["timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                if (success)
                {
                    return Response.Success($"Physics event '{eventName}' triggered in system '{systemName}'.", triggerInfo);
                }
                else
                {
                    return Response.Error($"Failed to trigger physics event '{eventName}' in system '{systemName}'.");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to trigger physics event: {e.Message}");
            }
        }

        private static string GetEventTypeFromComponent(PhysicsEventSystemBase component)
        {
            switch (component)
            {
                case CollisionEventSystem _:
                    return "collision";
                case TriggerEventSystem _:
                    return "trigger";
                case JointEventSystem _:
                    return "joint";
                case RigidbodyEventSystem _:
                    return "rigidbody";
                default:
                    return "unknown";
            }
        }

        #endregion

        #region Real Physics Event System Components
        
        [System.Serializable]
        public abstract class PhysicsEventSystemBase : MonoBehaviour
        {
            [SerializeField] public bool autoRegister = true;
            [SerializeField] public bool debugMode = false;
            [SerializeField] public int maxEventsPerFrame = 100;
            [SerializeField] public LayerMask layerMask = -1;
            
            protected Dictionary<GameObject, List<string>> registeredListeners = new Dictionary<GameObject, List<string>>();
            protected Queue<PhysicsEventData> eventQueue = new Queue<PhysicsEventData>();
            protected int eventsProcessedThisFrame = 0;

            protected virtual void Start()
            {
                if (autoRegister)
                {
                    RegisterAllObjectsInScene();
                }
            }

            protected virtual void FixedUpdate()
            {
                eventsProcessedThisFrame = 0;
                ProcessEventQueue();
            }

            protected virtual void RegisterAllObjectsInScene()
            {
                var allObjects = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsSortMode.None);
                foreach (var obj in allObjects)
                {
                    if (ShouldRegisterObject(obj))
                    {
                        RegisterObject(obj);
                    }
                }
            }

            protected abstract bool ShouldRegisterObject(GameObject obj);
            protected abstract void RegisterObject(GameObject obj);
            protected abstract void UnregisterObject(GameObject obj);

            public virtual bool AddListener(GameObject target, string eventName)
            {
                if (!registeredListeners.ContainsKey(target))
                {
                    registeredListeners[target] = new List<string>();
                    RegisterObject(target);
                }

                if (!registeredListeners[target].Contains(eventName))
                {
                    registeredListeners[target].Add(eventName);
                    if (debugMode)
                        Debug.Log($"[{GetType().Name}] Added listener for '{eventName}' on {target.name}");
                    return true;
                }
                return false;
            }

            public virtual bool RemoveListener(GameObject target, string eventName)
            {
                if (registeredListeners.ContainsKey(target))
                {
                    bool removed = registeredListeners[target].Remove(eventName);
                    if (registeredListeners[target].Count == 0)
                    {
                        registeredListeners.Remove(target);
                        UnregisterObject(target);
                    }
                    if (debugMode && removed)
                        Debug.Log($"[{GetType().Name}] Removed listener for '{eventName}' on {target.name}");
                    return removed;
                }
                return false;
            }

            public virtual bool RemoveAllListeners(GameObject target)
            {
                if (registeredListeners.ContainsKey(target))
                {
                    registeredListeners.Remove(target);
                    UnregisterObject(target);
                    if (debugMode)
                        Debug.Log($"[{GetType().Name}] Removed all listeners from {target.name}");
                    return true;
                }
                return false;
            }

            public virtual bool TriggerEvent(string eventName, object eventData = null)
            {
                var eventDataObj = new PhysicsEventData
                {
                    eventName = eventName,
                    eventData = eventData,
                    timestamp = Time.time,
                    source = gameObject
                };

                eventQueue.Enqueue(eventDataObj);
                if (debugMode)
                    Debug.Log($"[{GetType().Name}] Queued event '{eventName}'");
                return true;
            }

            protected virtual void ProcessEventQueue()
            {
                while (eventQueue.Count > 0 && eventsProcessedThisFrame < maxEventsPerFrame)
                {
                    var eventData = eventQueue.Dequeue();
                    ProcessEvent(eventData);
                    eventsProcessedThisFrame++;
                }
            }

            protected abstract void ProcessEvent(PhysicsEventData eventData);

            public int GetRegisteredListenersCount()
            {
                return registeredListeners.Sum(kvp => kvp.Value.Count);
            }

            protected void NotifyListeners(string eventName, object eventData, GameObject source = null)
            {
                foreach (var listener in registeredListeners)
                {
                    if (listener.Value.Contains(eventName))
                    {
                        SendEventToObject(listener.Key, eventName, eventData, source);
                    }
                }
            }

            protected virtual void SendEventToObject(GameObject target, string eventName, object eventData, GameObject source)
            {
                var components = target.GetComponents<MonoBehaviour>();
                foreach (var component in components)
                {
                    var method = component.GetType().GetMethod($"On{eventName}", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    if (method != null)
                    {
                        try
                        {
                            var parameters = method.GetParameters();
                            if (parameters.Length == 0)
                            {
                                method.Invoke(component, null);
                            }
                            else if (parameters.Length == 1)
                            {
                                method.Invoke(component, new object[] { eventData });
                            }
                            else if (parameters.Length == 2)
                            {
                                method.Invoke(component, new object[] { eventData, source });
                            }

                            if (debugMode)
                                Debug.Log($"[{GetType().Name}] Called {component.GetType().Name}.On{eventName}() on {target.name}");
                        }
                        catch (Exception e)
                        {
                            Debug.LogError($"[{GetType().Name}] Error calling On{eventName} on {target.name}: {e.Message}");
                        }
                    }
                }
            }
        }

        [System.Serializable]
        public class PhysicsEventData
        {
            public string eventName;
            public object eventData;
            public float timestamp;
            public GameObject source;
        }

        public class CollisionEventSystem : PhysicsEventSystemBase
        {
            protected override bool ShouldRegisterObject(GameObject obj)
            {
                return obj.GetComponent<Collider>() != null && obj.GetComponent<Rigidbody>() != null;
            }

            protected override void RegisterObject(GameObject obj)
            {
                var listener = obj.GetComponent<CollisionEventListener>();
                if (listener == null)
                {
                    listener = obj.AddComponent<CollisionEventListener>();
                }
                listener.eventSystem = this;
            }

            protected override void UnregisterObject(GameObject obj)
            {
                var listener = obj.GetComponent<CollisionEventListener>();
                if (listener != null)
                {
                    listener.eventSystem = null;
                }
            }

            protected override void ProcessEvent(PhysicsEventData eventData)
            {
                NotifyListeners(eventData.eventName, eventData.eventData, eventData.source);
            }

            public void OnCollisionDetected(string eventType, Collision collision, GameObject source)
            {
                if ((layerMask.value & (1 << collision.gameObject.layer)) != 0)
                {
                    TriggerEvent($"Collision{eventType}", new CollisionEventArgs
                    {
                        collision = collision,
                        contacts = collision.contacts,
                        contactCount = collision.contactCount,
                        gameObject = collision.gameObject,
                        relativeVelocity = collision.relativeVelocity,
                        impulse = collision.impulse
                    });
                }
            }
        }

        public class TriggerEventSystem : PhysicsEventSystemBase
        {
            protected override bool ShouldRegisterObject(GameObject obj)
            {
                var colliders = obj.GetComponents<Collider>();
                return colliders.Any(c => c.isTrigger);
            }

            protected override void RegisterObject(GameObject obj)
            {
                var listener = obj.GetComponent<TriggerEventListener>();
                if (listener == null)
                {
                    listener = obj.AddComponent<TriggerEventListener>();
                }
                listener.eventSystem = this;
            }

            protected override void UnregisterObject(GameObject obj)
            {
                var listener = obj.GetComponent<TriggerEventListener>();
                if (listener != null)
                {
                    listener.eventSystem = null;
                }
            }

            protected override void ProcessEvent(PhysicsEventData eventData)
            {
                NotifyListeners(eventData.eventName, eventData.eventData, eventData.source);
            }

            public void OnTriggerDetected(string eventType, Collider other, GameObject source)
            {
                if ((layerMask.value & (1 << other.gameObject.layer)) != 0)
                {
                    TriggerEvent($"Trigger{eventType}", new TriggerEventArgs
                    {
                        other = other,
                        gameObject = other.gameObject,
                        bounds = other.bounds,
                        attachedRigidbody = other.attachedRigidbody
                    });
                }
            }
        }

        public class JointEventSystem : PhysicsEventSystemBase
        {
            protected override bool ShouldRegisterObject(GameObject obj)
            {
                return obj.GetComponent<Joint>() != null;
            }

            protected override void RegisterObject(GameObject obj)
            {
                var listener = obj.GetComponent<JointEventListener>();
                if (listener == null)
                {
                    listener = obj.AddComponent<JointEventListener>();
                }
                listener.eventSystem = this;
            }

            protected override void UnregisterObject(GameObject obj)
            {
                var listener = obj.GetComponent<JointEventListener>();
                if (listener != null)
                {
                    listener.eventSystem = null;
                }
            }

            protected override void ProcessEvent(PhysicsEventData eventData)
            {
                NotifyListeners(eventData.eventName, eventData.eventData, eventData.source);
            }

            public void OnJointEvent(string eventType, Joint joint, float breakForce = 0f)
            {
                TriggerEvent($"Joint{eventType}", new JointEventArgs
                {
                    joint = joint,
                    connectedBody = joint.connectedBody,
                    breakForce = breakForce,
                    anchor = joint.anchor,
                    connectedAnchor = joint.connectedAnchor
                });
            }
        }

        public class RigidbodyEventSystem : PhysicsEventSystemBase
        {
            protected override bool ShouldRegisterObject(GameObject obj)
            {
                return obj.GetComponent<Rigidbody>() != null;
            }

            protected override void RegisterObject(GameObject obj)
            {
                var listener = obj.GetComponent<RigidbodyEventListener>();
                if (listener == null)
                {
                    listener = obj.AddComponent<RigidbodyEventListener>();
                }
                listener.eventSystem = this;
            }

            protected override void UnregisterObject(GameObject obj)
            {
                var listener = obj.GetComponent<RigidbodyEventListener>();
                if (listener != null)
                {
                    listener.eventSystem = null;
                }
            }

            protected override void ProcessEvent(PhysicsEventData eventData)
            {
                NotifyListeners(eventData.eventName, eventData.eventData, eventData.source);
            }

            public void OnRigidbodyEvent(string eventType, Rigidbody rigidbody, object additionalData = null)
            {
                TriggerEvent($"Rigidbody{eventType}", new RigidbodyEventArgs
                {
                    rigidbody = rigidbody,
                    velocity = rigidbody.linearVelocity,
                    angularVelocity = rigidbody.angularVelocity,
                    mass = rigidbody.mass,
                    isKinematic = rigidbody.isKinematic,
                    isSleeping = rigidbody.IsSleeping(),
                    additionalData = additionalData
                });
            }
        }

        // Event argument classes
        [System.Serializable]
        public class CollisionEventArgs
        {
            public Collision collision;
            public ContactPoint[] contacts;
            public int contactCount;
            public GameObject gameObject;
            public Vector3 relativeVelocity;
            public Vector3 impulse;
        }

        [System.Serializable]
        public class TriggerEventArgs
        {
            public Collider other;
            public GameObject gameObject;
            public Bounds bounds;
            public Rigidbody attachedRigidbody;
        }

        [System.Serializable]
        public class JointEventArgs
        {
            public Joint joint;
            public Rigidbody connectedBody;
            public float breakForce;
            public Vector3 anchor;
            public Vector3 connectedAnchor;
        }

        [System.Serializable]
        public class RigidbodyEventArgs
        {
            public Rigidbody rigidbody;
            public Vector3 velocity;
            public Vector3 angularVelocity;
            public float mass;
            public bool isKinematic;
            public bool isSleeping;
            public object additionalData;
        }

        // Event listener components
        public class CollisionEventListener : MonoBehaviour
        {
            public CollisionEventSystem eventSystem;

            void OnCollisionEnter(Collision collision)
            {
                eventSystem?.OnCollisionDetected("Enter", collision, gameObject);
            }

            void OnCollisionStay(Collision collision)
            {
                eventSystem?.OnCollisionDetected("Stay", collision, gameObject);
            }

            void OnCollisionExit(Collision collision)
            {
                eventSystem?.OnCollisionDetected("Exit", collision, gameObject);
            }
        }

        public class TriggerEventListener : MonoBehaviour
        {
            public TriggerEventSystem eventSystem;

            void OnTriggerEnter(Collider other)
            {
                eventSystem?.OnTriggerDetected("Enter", other, gameObject);
            }

            void OnTriggerStay(Collider other)
            {
                eventSystem?.OnTriggerDetected("Stay", other, gameObject);
            }

            void OnTriggerExit(Collider other)
            {
                eventSystem?.OnTriggerDetected("Exit", other, gameObject);
            }
        }

        public class JointEventListener : MonoBehaviour
        {
            public JointEventSystem eventSystem;

            void OnJointBreak(float breakForce)
            {
                var joint = GetComponent<Joint>();
                eventSystem?.OnJointEvent("Break", joint, breakForce);
            }
        }

        public class RigidbodyEventListener : MonoBehaviour
        {
            public RigidbodyEventSystem eventSystem;
            private Rigidbody rb;
            private bool wasSleeping = false;

            void Start()
            {
                rb = GetComponent<Rigidbody>();
            }

            void FixedUpdate()
            {
                if (rb != null)
                {
                    bool isSleeping = rb.IsSleeping();
                    if (isSleeping != wasSleeping)
                    {
                        eventSystem?.OnRigidbodyEvent(isSleeping ? "Sleep" : "WakeUp", rb);
                        wasSleeping = isSleeping;
                    }
                }
            }
        }

        #endregion

        private static object HandlePhysicsDebugVisualization(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                return Response.Error($"Unknown action: '{action}'. Valid actions: {string.Join(", ", ValidActions)}");
            }

            try
            {
                switch (action)
                {
                    case "create":
                        return CreatePhysicsDebugVisualizer(@params);
                    case "modify":
                        return ModifyPhysicsDebugVisualizer(@params);
                    case "delete":
                        return DeletePhysicsDebugVisualizer(@params);
                    case "list":
                        return ListPhysicsDebugVisualizers(@params);
                    case "get_info":
                        return GetPhysicsDebugVisualizerInfo(@params);
                    case "enable":
                        return EnablePhysicsDebugVisualization(@params);
                    case "disable":
                        return DisablePhysicsDebugVisualization(@params);
                    case "toggle":
                        return TogglePhysicsDebugVisualization(@params);
                    case "capture_frame":
                        return CapturePhysicsDebugFrame(@params);
                    default:
                        return Response.Error($"Action '{action}' not implemented for physics debug visualization.");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedPhysics] Physics debug visualization action '{action}' failed: {e}");
                return Response.Error($"Internal error processing physics debug visualization: {e.Message}");
            }
        }

        private static object CreatePhysicsDebugVisualizer(JObject @params)
        {
            string visualizerName = @params["visualizer_name"]?.ToString();
            string visualizationType = @params["visualization_type"]?.ToString()?.ToLower();

            if (string.IsNullOrEmpty(visualizerName))
            {
                return Response.Error("Visualizer name is required.");
            }

            if (string.IsNullOrEmpty(visualizationType))
            {
                return Response.Error("Visualization type is required.");
            }

            try
            {
                // Create GameObject for the physics debug visualizer
                var visualizerGO = new GameObject($"PhysicsDebugVisualizer_{visualizerName}");
                visualizerGO.tag = "EditorOnly";

                // Add appropriate component based on visualization type
                Component visualizerComponent = null;
                switch (visualizationType)
                {
                    case "colliders":
                        visualizerComponent = visualizerGO.AddComponent<PhysicsColliderDebugVisualizer>();
                        break;
                    case "rigidbodies":
                        visualizerComponent = visualizerGO.AddComponent<PhysicsRigidbodyDebugVisualizer>();
                        break;
                    case "joints":
                        visualizerComponent = visualizerGO.AddComponent<PhysicsJointDebugVisualizer>();
                        break;
                    case "forces":
                        visualizerComponent = visualizerGO.AddComponent<PhysicsForceDebugVisualizer>();
                        break;
                    case "contacts":
                        visualizerComponent = visualizerGO.AddComponent<PhysicsContactDebugVisualizer>();
                        break;
                    case "raycast":
                        visualizerComponent = visualizerGO.AddComponent<PhysicsRaycastDebugVisualizer>();
                        break;
                    default:
                        UnityEngine.Object.DestroyImmediate(visualizerGO);
                        return Response.Error($"Unknown visualization type: '{visualizationType}'. Valid types: colliders, rigidbodies, joints, forces, contacts, raycast");
                }

                // Configure visualizer properties
                if (@params["enabled"] != null)
                {
                    bool enabled = @params["enabled"].ToObject<bool>();
                    SetVisualizerProperty(visualizerComponent, "enabled", enabled);
                }

                if (@params["color"] != null)
                {
                    var colorArray = @params["color"].ToObject<float[]>();
                    if (colorArray.Length >= 3)
                    {
                        Color color = new Color(colorArray[0], colorArray[1], colorArray[2], colorArray.Length > 3 ? colorArray[3] : 1.0f);
                        SetVisualizerProperty(visualizerComponent, "debugColor", color);
                    }
                }

                if (@params["line_width"] != null)
                {
                    float lineWidth = @params["line_width"].ToObject<float>();
                    SetVisualizerProperty(visualizerComponent, "lineWidth", lineWidth);
                }

                if (@params["show_labels"] != null)
                {
                    bool showLabels = @params["show_labels"].ToObject<bool>();
                    SetVisualizerProperty(visualizerComponent, "showLabels", showLabels);
                }

                if (@params["update_frequency"] != null)
                {
                    float updateFrequency = @params["update_frequency"].ToObject<float>();
                    SetVisualizerProperty(visualizerComponent, "updateFrequency", updateFrequency);
                }

                // Create prefab
                string prefabPath = $"Assets/PhysicsDebugVisualizers/{visualizerName}.prefab";
                string directory = Path.GetDirectoryName(prefabPath);
                if (!AssetDatabase.IsValidFolder(directory))
                {
                    Directory.CreateDirectory(Path.Combine(Application.dataPath, "PhysicsDebugVisualizers"));
                    AssetDatabase.Refresh();
                }

                var prefab = PrefabUtility.SaveAsPrefabAsset(visualizerGO, prefabPath);
                UnityEngine.Object.DestroyImmediate(visualizerGO);

                var visualizerInfo = new Dictionary<string, object>
                {
                    ["name"] = visualizerName,
                    ["visualization_type"] = visualizationType,
                    ["prefab_path"] = prefabPath,
                    ["component_type"] = visualizerComponent.GetType().Name,
                    ["enabled"] = @params["enabled"]?.ToObject<bool>() ?? true,
                    ["color"] = @params["color"]?.ToObject<float[]>() ?? new float[] { 1, 1, 1, 1 },
                    ["line_width"] = @params["line_width"]?.ToObject<float>() ?? 1.0f,
                    ["show_labels"] = @params["show_labels"]?.ToObject<bool>() ?? false,
                    ["update_frequency"] = @params["update_frequency"]?.ToObject<float>() ?? 30.0f
                };

                return Response.Success($"Physics debug visualizer '{visualizerName}' created successfully.", visualizerInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create physics debug visualizer: {e.Message}");
            }
        }

        private static object ModifyPhysicsDebugVisualizer(JObject @params)
        {
            string visualizerName = @params["visualizer_name"]?.ToString();
            if (string.IsNullOrEmpty(visualizerName))
            {
                return Response.Error("Visualizer name is required.");
            }

            try
            {
                // Find existing prefab
                string[] guids = AssetDatabase.FindAssets($"{visualizerName} t:Prefab");
                if (guids.Length == 0)
                {
                    return Response.Error($"Physics debug visualizer '{visualizerName}' not found.");
                }

                string prefabPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                if (prefab == null)
                {
                    return Response.Error($"Failed to load physics debug visualizer '{visualizerName}'.");
                }

                // Instantiate for modification
                var instance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
                var visualizerComponents = instance.GetComponents<MonoBehaviour>();
                var visualizerComponent = visualizerComponents.FirstOrDefault(c => c.GetType().Name.Contains("PhysicsDebugVisualizer") || c.GetType().Name.Contains("DebugVisualizer"));

                if (visualizerComponent == null)
                {
                    UnityEngine.Object.DestroyImmediate(instance);
                    return Response.Error($"No physics debug visualizer component found in '{visualizerName}'.");
                }

                bool modified = false;

                // Update properties
                if (@params["enabled"] != null)
                {
                    bool enabled = @params["enabled"].ToObject<bool>();
                    SetVisualizerProperty(visualizerComponent, "enabled", enabled);
                    modified = true;
                }

                if (@params["color"] != null)
                {
                    var colorArray = @params["color"].ToObject<float[]>();
                    if (colorArray.Length >= 3)
                    {
                        Color color = new Color(colorArray[0], colorArray[1], colorArray[2], colorArray.Length > 3 ? colorArray[3] : 1.0f);
                        SetVisualizerProperty(visualizerComponent, "debugColor", color);
                        modified = true;
                    }
                }

                if (@params["line_width"] != null)
                {
                    float lineWidth = @params["line_width"].ToObject<float>();
                    SetVisualizerProperty(visualizerComponent, "lineWidth", lineWidth);
                    modified = true;
                }

                if (@params["show_labels"] != null)
                {
                    bool showLabels = @params["show_labels"].ToObject<bool>();
                    SetVisualizerProperty(visualizerComponent, "showLabels", showLabels);
                    modified = true;
                }

                if (@params["update_frequency"] != null)
                {
                    float updateFrequency = @params["update_frequency"].ToObject<float>();
                    SetVisualizerProperty(visualizerComponent, "updateFrequency", updateFrequency);
                    modified = true;
                }

                if (modified)
                {
                    // Save changes back to prefab
                    PrefabUtility.SaveAsPrefabAsset(instance, prefabPath);
                    UnityEngine.Object.DestroyImmediate(instance);

                    var visualizerInfo = new Dictionary<string, object>
                    {
                        ["name"] = visualizerName,
                        ["prefab_path"] = prefabPath,
                        ["component_type"] = visualizerComponent.GetType().Name,
                        ["modified"] = true
                    };

                    return Response.Success($"Physics debug visualizer '{visualizerName}' modified successfully.", visualizerInfo);
                }
                else
                {
                    UnityEngine.Object.DestroyImmediate(instance);
                    return Response.Error("No valid parameters provided for modification.");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to modify physics debug visualizer: {e.Message}");
            }
        }

        private static object DeletePhysicsDebugVisualizer(JObject @params)
        {
            string visualizerName = @params["visualizer_name"]?.ToString();
            if (string.IsNullOrEmpty(visualizerName))
            {
                return Response.Error("Visualizer name is required.");
            }

            try
            {
                string[] guids = AssetDatabase.FindAssets($"{visualizerName} t:Prefab");
                if (guids.Length == 0)
                {
                    return Response.Error($"Physics debug visualizer '{visualizerName}' not found.");
                }

                string prefabPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                
                // Check if visualizer is in use
                bool forceDelete = @params["force"]?.ToObject<bool>() ?? false;
                if (!forceDelete)
                {
                    var instancesInScene = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsInactive.Exclude, FindObjectsSortMode.None)
                        .Where(go => PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(go) == prefabPath)
                        .Select(go => go.name)
                        .ToList();

                    if (instancesInScene.Count > 0)
                    {
                        return Response.Error($"Physics debug visualizer '{visualizerName}' is in use by {instancesInScene.Count} instances. Use 'force: true' to delete anyway.");
                    }
                }

                AssetDatabase.DeleteAsset(prefabPath);
                AssetDatabase.SaveAssets();

                return Response.Success($"Physics debug visualizer '{visualizerName}' deleted successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to delete physics debug visualizer: {e.Message}");
            }
        }

        private static object ListPhysicsDebugVisualizers(JObject @params)
        {
            try
            {
                string[] guids = AssetDatabase.FindAssets("PhysicsDebugVisualizer t:Prefab");
                var visualizers = new List<Dictionary<string, object>>();

                foreach (string guid in guids)
                {
                    string prefabPath = AssetDatabase.GUIDToAssetPath(guid);
                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                    if (prefab != null)
                    {
                        var visualizerComponents = prefab.GetComponents<MonoBehaviour>();
                        var visualizerComponent = visualizerComponents.FirstOrDefault(c => c.GetType().Name.Contains("PhysicsDebugVisualizer") || c.GetType().Name.Contains("DebugVisualizer"));

                        if (visualizerComponent != null)
                        {
                            var instancesInScene = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsInactive.Exclude, FindObjectsSortMode.None)
                                .Where(go => PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(go) == prefabPath)
                                .Select(go => go.name)
                                .ToList();

                            visualizers.Add(new Dictionary<string, object>
                            {
                                ["name"] = prefab.name.Replace("PhysicsDebugVisualizer_", ""),
                                ["prefab_path"] = prefabPath,
                                ["component_type"] = visualizerComponent.GetType().Name,
                                ["visualization_type"] = GetVisualizationTypeFromComponent(visualizerComponent),
                                ["enabled"] = GetVisualizerProperty(visualizerComponent, "enabled") ?? true,
                                ["instances_in_scene"] = instancesInScene.Count,
                                ["instance_names"] = instancesInScene.Take(5).ToList()
                            });
                        }
                    }
                }

                return Response.Success($"Found {visualizers.Count} physics debug visualizers.", visualizers);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list physics debug visualizers: {e.Message}");
            }
        }

        private static object GetPhysicsDebugVisualizerInfo(JObject @params)
        {
            string visualizerName = @params["visualizer_name"]?.ToString();
            if (string.IsNullOrEmpty(visualizerName))
            {
                return Response.Error("Visualizer name is required.");
            }

            try
            {
                string[] guids = AssetDatabase.FindAssets($"{visualizerName} t:Prefab");
                if (guids.Length == 0)
                {
                    return Response.Error($"Physics debug visualizer '{visualizerName}' not found.");
                }

                string prefabPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                if (prefab == null)
                {
                    return Response.Error($"Failed to load physics debug visualizer '{visualizerName}'.");
                }

                var visualizerComponents = prefab.GetComponents<MonoBehaviour>();
                var visualizerComponent = visualizerComponents.FirstOrDefault(c => c.GetType().Name.Contains("PhysicsDebugVisualizer") || c.GetType().Name.Contains("DebugVisualizer"));

                if (visualizerComponent == null)
                {
                    return Response.Error($"No physics debug visualizer component found in '{visualizerName}'.");
                }

                var instancesInScene = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsInactive.Include, FindObjectsSortMode.None)
                    .Where(go => PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(go) == prefabPath)
                    .Select(go => new Dictionary<string, object>
                    {
                        ["name"] = go.name,
                        ["path"] = GetGameObjectPath(go),
                        ["active"] = go.activeInHierarchy,
                        ["enabled"] = visualizerComponent.enabled
                    })
                    .ToList();

                var visualizerInfo = new Dictionary<string, object>
                {
                    ["name"] = visualizerName,
                    ["prefab_path"] = prefabPath,
                    ["component_type"] = visualizerComponent.GetType().Name,
                    ["visualization_type"] = GetVisualizationTypeFromComponent(visualizerComponent),
                    ["instances_count"] = instancesInScene.Count,
                    ["instances"] = instancesInScene,
                    ["enabled"] = GetVisualizerProperty(visualizerComponent, "enabled"),
                    ["debug_color"] = GetVisualizerProperty(visualizerComponent, "debugColor"),
                    ["line_width"] = GetVisualizerProperty(visualizerComponent, "lineWidth"),
                    ["show_labels"] = GetVisualizerProperty(visualizerComponent, "showLabels"),
                    ["update_frequency"] = GetVisualizerProperty(visualizerComponent, "updateFrequency")
                };

                return Response.Success($"Physics debug visualizer '{visualizerName}' information retrieved.", visualizerInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get physics debug visualizer info: {e.Message}");
            }
        }

        private static object EnablePhysicsDebugVisualization(JObject @params)
        {
            string visualizerName = @params["visualizer_name"]?.ToString();
            if (string.IsNullOrEmpty(visualizerName))
            {
                return Response.Error("Visualizer name is required.");
            }

            try
            {
                var visualizers = UnityEngine.Object.FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None)
                    .Where(mb => mb.name.Contains($"PhysicsDebugVisualizer_{visualizerName}"))
                    .ToList();

                if (visualizers.Count == 0)
                {
                    return Response.Error($"No active instances of physics debug visualizer '{visualizerName}' found.");
                }

                foreach (var visualizer in visualizers)
                {
                    SetVisualizerProperty(visualizer, "enabled", true);
                    visualizer.enabled = true;
                }

                return Response.Success($"Physics debug visualizer '{visualizerName}' enabled for {visualizers.Count} instances.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to enable physics debug visualizer: {e.Message}");
            }
        }

        private static object DisablePhysicsDebugVisualization(JObject @params)
        {
            string visualizerName = @params["visualizer_name"]?.ToString();
            if (string.IsNullOrEmpty(visualizerName))
            {
                return Response.Error("Visualizer name is required.");
            }

            try
            {
                var visualizers = UnityEngine.Object.FindObjectsByType<MonoBehaviour>(FindObjectsInactive.Exclude, FindObjectsSortMode.None)
                    .Where(mb => mb.name.Contains($"PhysicsDebugVisualizer_{visualizerName}"))
                    .ToList();

                if (visualizers.Count == 0)
                {
                    return Response.Error($"No active instances of physics debug visualizer '{visualizerName}' found.");
                }

                foreach (var visualizer in visualizers)
                {
                    SetVisualizerProperty(visualizer, "enabled", false);
                    visualizer.enabled = false;
                }

                return Response.Success($"Physics debug visualizer '{visualizerName}' disabled for {visualizers.Count} instances.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to disable physics debug visualizer: {e.Message}");
            }
        }

        private static object TogglePhysicsDebugVisualization(JObject @params)
        {
            string visualizerName = @params["visualizer_name"]?.ToString();
            if (string.IsNullOrEmpty(visualizerName))
            {
                return Response.Error("Visualizer name is required.");
            }

            try
            {
                var visualizers = UnityEngine.Object.FindObjectsByType<MonoBehaviour>(FindObjectsInactive.Exclude, FindObjectsSortMode.None)
                    .Where(mb => mb.name.Contains($"PhysicsDebugVisualizer_{visualizerName}"))
                    .ToList();

                if (visualizers.Count == 0)
                {
                    return Response.Error($"No active instances of physics debug visualizer '{visualizerName}' found.");
                }

                bool newState = !visualizers.First().enabled;
                foreach (var visualizer in visualizers)
                {
                    SetVisualizerProperty(visualizer, "enabled", newState);
                    visualizer.enabled = newState;
                }

                string stateText = newState ? "enabled" : "disabled";
                return Response.Success($"Physics debug visualizer '{visualizerName}' {stateText} for {visualizers.Count} instances.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to toggle physics debug visualizer: {e.Message}");
            }
        }

        private static object CapturePhysicsDebugFrame(JObject @params)
        {
            string visualizerName = @params["visualizer_name"]?.ToString();
            string outputPath = @params["output_path"]?.ToString();

            if (string.IsNullOrEmpty(visualizerName))
            {
                return Response.Error("Visualizer name is required.");
            }

            try
            {
                var visualizers = UnityEngine.Object.FindObjectsByType<MonoBehaviour>(FindObjectsInactive.Exclude, FindObjectsSortMode.None)
                    .Where(mb => mb.name.Contains($"PhysicsDebugVisualizer_{visualizerName}"))
                    .ToList();

                if (visualizers.Count == 0)
                {
                    return Response.Error($"No active instances of physics debug visualizer '{visualizerName}' found.");
                }

                var captureData = new Dictionary<string, object>
                {
                    ["visualizer_name"] = visualizerName,
                    ["timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    ["frame_count"] = Time.frameCount,
                    ["instances_captured"] = visualizers.Count
                };

                // Capture frame data from each visualizer
                var frameData = new List<Dictionary<string, object>>();
                foreach (var visualizer in visualizers)
                {
                    var captureMethod = visualizer.GetType().GetMethod("CaptureFrame");
                    if (captureMethod != null)
                    {
                        var data = captureMethod.Invoke(visualizer, null) as Dictionary<string, object>;
                        if (data != null)
                        {
                            frameData.Add(data);
                        }
                    }
                }

                captureData["frame_data"] = frameData;

                // Save to file if output path provided
                if (!string.IsNullOrEmpty(outputPath))
                {
                    string json = JsonConvert.SerializeObject(captureData, Formatting.Indented);
                    string fullPath = Path.Combine(Application.dataPath, outputPath);
                    Directory.CreateDirectory(Path.GetDirectoryName(fullPath));
                    File.WriteAllText(fullPath, json);
                    AssetDatabase.Refresh();
                    captureData["saved_to"] = fullPath;
                }

                return Response.Success($"Physics debug frame captured for visualizer '{visualizerName}'.", captureData);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to capture physics debug frame: {e.Message}");
            }
        }

        private static void SetVisualizerProperty(Component component, string propertyName, object value)
        {
            var field = component.GetType().GetField(propertyName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                field.SetValue(component, value);
                return;
            }

            var property = component.GetType().GetProperty(propertyName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (property != null && property.CanWrite)
            {
                property.SetValue(component, value);
            }
        }

        private static object GetVisualizerProperty(Component component, string propertyName)
        {
            var field = component.GetType().GetField(propertyName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                return field.GetValue(component);
            }

            var property = component.GetType().GetProperty(propertyName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (property != null && property.CanRead)
            {
                return property.GetValue(component);
            }

            return null;
        }

        private static string GetVisualizationTypeFromComponent(Component component)
        {
            string typeName = component.GetType().Name.ToLower();
            if (typeName.Contains("collider")) return "colliders";
            if (typeName.Contains("rigidbody")) return "rigidbodies";
            if (typeName.Contains("joint")) return "joints";
            if (typeName.Contains("force")) return "forces";
            if (typeName.Contains("contact")) return "contacts";
            if (typeName.Contains("raycast")) return "raycast";
            return "unknown";
        }

        // Real physics debug visualizer components using Unity 6.2 APIs
        
        [System.Serializable]
        public abstract class PhysicsDebugVisualizerBase : MonoBehaviour
        {
            [SerializeField] public new bool enabled = true;
            [SerializeField] public Color debugColor = Color.green;
            [SerializeField] public float lineWidth = 1.0f;
            [SerializeField] public bool showLabels = false;
            [SerializeField] public float updateFrequency = 30.0f;
            [SerializeField] public bool onlyWhenSelected = false;
            
            protected float lastUpdateTime = 0f;
            protected bool needsUpdate = true;

            protected virtual void Update()
            {
                if (enabled && Time.time - lastUpdateTime >= 1f / updateFrequency)
                {
                    needsUpdate = true;
                    lastUpdateTime = Time.time;
                }
            }

            protected virtual void OnDrawGizmos()
            {
                if (enabled && !onlyWhenSelected)
                {
                    DrawVisualization();
                }
            }

            protected virtual void OnDrawGizmosSelected()
            {
                if (enabled && onlyWhenSelected)
                {
                    DrawVisualization();
                }
            }

            protected abstract void DrawVisualization();

            protected void DrawLabel(Vector3 position, string text)
            {
                if (showLabels && !string.IsNullOrEmpty(text))
                {
#if UNITY_EDITOR
                    var style = new GUIStyle();
                    style.normal.textColor = debugColor;
                    UnityEditor.Handles.Label(position, text, style);
#endif
                }
            }

            public virtual Dictionary<string, object> CaptureFrame()
            {
                return new Dictionary<string, object>
                {
                    ["component_type"] = GetType().Name,
                    ["enabled"] = enabled,
                    ["debug_color"] = new float[] { debugColor.r, debugColor.g, debugColor.b, debugColor.a },
                    ["frame_time"] = Time.time,
                    ["frame_count"] = Time.frameCount
                };
            }
        }

        public class PhysicsColliderDebugVisualizer : PhysicsDebugVisualizerBase
        {
            [SerializeField] public bool showBounds = true;
            [SerializeField] public bool showContactPoints = false;
            [SerializeField] public bool showNormals = false;
            
            private Collider[] cachedColliders;

            protected override void Update()
            {
                base.Update();
                if (needsUpdate)
                {
                    cachedColliders = GetComponentsInChildren<Collider>();
                    needsUpdate = false;
                }
            }

            protected override void DrawVisualization()
            {
                if (cachedColliders == null) return;

                Color originalColor = Gizmos.color;
                Gizmos.color = debugColor;

                foreach (var collider in cachedColliders)
                {
                    if (collider == null || !collider.enabled) continue;

                    // Draw collider bounds
                    if (showBounds)
                    {
                        Gizmos.DrawWireCube(collider.bounds.center, collider.bounds.size);
                    }

                    // Draw specific collider shapes
                    DrawColliderShape(collider);

                    // Draw labels
                    if (showLabels)
                    {
                        DrawLabel(collider.bounds.center, $"{collider.GetType().Name}\n{collider.name}");
                    }
                }

                Gizmos.color = originalColor;
            }

            private void DrawColliderShape(Collider collider)
            {
                switch (collider)
                {
                    case BoxCollider box:
                        Gizmos.matrix = Matrix4x4.TRS(box.transform.position, box.transform.rotation, box.transform.localScale);
                        Gizmos.DrawWireCube(box.center, box.size);
                        break;
                    
                    case SphereCollider sphere:
                        Gizmos.matrix = Matrix4x4.TRS(sphere.transform.position, sphere.transform.rotation, sphere.transform.localScale);
                        float radius = sphere.radius * Mathf.Max(sphere.transform.localScale.x, sphere.transform.localScale.y, sphere.transform.localScale.z);
                        Gizmos.DrawWireSphere(sphere.center, radius);
                        break;
                    
                    case CapsuleCollider capsule:
                        Gizmos.matrix = Matrix4x4.TRS(capsule.transform.position, capsule.transform.rotation, capsule.transform.localScale);
                        DrawWireCapsule(capsule.center, capsule.radius, capsule.height, capsule.direction);
                        break;
                    
                    case MeshCollider mesh:
                        if (mesh.sharedMesh != null)
                        {
                            Gizmos.matrix = Matrix4x4.TRS(mesh.transform.position, mesh.transform.rotation, mesh.transform.localScale);
                            Gizmos.DrawWireMesh(mesh.sharedMesh);
                        }
                        break;
                }

                Gizmos.matrix = Matrix4x4.identity;
            }

            private void DrawWireCapsule(Vector3 center, float radius, float height, int direction)
            {
                Vector3 up = Vector3.zero;
                switch (direction)
                {
                    case 0: up = Vector3.right; break;
                    case 1: up = Vector3.up; break;
                    case 2: up = Vector3.forward; break;
                }

                float halfHeight = height * 0.5f;
                Vector3 top = center + up * (halfHeight - radius);
                Vector3 bottom = center - up * (halfHeight - radius);

                // Draw spheres at top and bottom
                Gizmos.DrawWireSphere(top, radius);
                Gizmos.DrawWireSphere(bottom, radius);

                // Draw connecting lines
                Vector3 right = Vector3.Cross(up, Vector3.forward).normalized * radius;
                Vector3 forward = Vector3.Cross(up, right).normalized * radius;

                Gizmos.DrawLine(top + right, bottom + right);
                Gizmos.DrawLine(top - right, bottom - right);
                Gizmos.DrawLine(top + forward, bottom + forward);
                Gizmos.DrawLine(top - forward, bottom - forward);
            }

            public override Dictionary<string, object> CaptureFrame()
            {
                var baseData = base.CaptureFrame();
                baseData["collider_count"] = cachedColliders?.Length ?? 0;
                baseData["show_bounds"] = showBounds;
                baseData["show_contact_points"] = showContactPoints;
                baseData["show_normals"] = showNormals;
                return baseData;
            }
        }

        public class PhysicsRigidbodyDebugVisualizer : PhysicsDebugVisualizerBase
        {
            [SerializeField] public bool showVelocity = true;
            [SerializeField] public bool showAngularVelocity = true;
            [SerializeField] public bool showCenterOfMass = true;
            [SerializeField] public bool showInertiaTensor = false;
            [SerializeField] public float velocityScale = 1.0f;
            
            private Rigidbody[] cachedRigidbodies;

            protected override void Update()
            {
                base.Update();
                if (needsUpdate)
                {
                    cachedRigidbodies = GetComponentsInChildren<Rigidbody>();
                    needsUpdate = false;
                }
            }

            protected override void DrawVisualization()
            {
                if (cachedRigidbodies == null) return;

                Color originalColor = Gizmos.color;
                Gizmos.color = debugColor;

                foreach (var rb in cachedRigidbodies)
                {
                    if (rb == null) continue;

                    Vector3 position = rb.transform.position;

                    // Draw velocity vector
                    if (showVelocity && rb.linearVelocity.magnitude > 0.01f)
                    {
                        Gizmos.color = Color.green;
                        Vector3 velocityEnd = position + rb.linearVelocity * velocityScale;
                        Gizmos.DrawRay(position, rb.linearVelocity * velocityScale);
                        Gizmos.DrawSphere(velocityEnd, 0.1f);
                        
                        if (showLabels)
                        {
                            DrawLabel(velocityEnd, $"V: {rb.linearVelocity.magnitude:F2} m/s");
                        }
                    }

                    // Draw angular velocity
                    if (showAngularVelocity && rb.angularVelocity.magnitude > 0.01f)
                    {
                        Gizmos.color = Color.yellow;
                        Vector3 angularEnd = position + rb.angularVelocity.normalized * velocityScale;
                        Gizmos.DrawRay(position, rb.angularVelocity.normalized * velocityScale);
                        
                        if (showLabels)
                        {
                            DrawLabel(angularEnd, $"ω: {rb.angularVelocity.magnitude:F2} rad/s");
                        }
                    }

                    // Draw center of mass
                    if (showCenterOfMass)
                    {
                        Gizmos.color = Color.red;
                        Vector3 centerOfMass = rb.worldCenterOfMass;
                        Gizmos.DrawSphere(centerOfMass, 0.05f);
                        
                        if (showLabels)
                        {
                            DrawLabel(centerOfMass, "CoM");
                        }
                    }

                    // Draw mass info
                    if (showLabels)
                    {
                        string info = $"Mass: {rb.mass:F2}kg\n";
                        info += $"Kinematic: {rb.isKinematic}\n";
                        info += $"Sleeping: {rb.IsSleeping()}";
                        DrawLabel(position + Vector3.up * 0.5f, info);
                    }
                }

                Gizmos.color = originalColor;
            }

            public override Dictionary<string, object> CaptureFrame()
            {
                var baseData = base.CaptureFrame();
                baseData["rigidbody_count"] = cachedRigidbodies?.Length ?? 0;
                baseData["show_velocity"] = showVelocity;
                baseData["show_angular_velocity"] = showAngularVelocity;
                baseData["show_center_of_mass"] = showCenterOfMass;
                baseData["velocity_scale"] = velocityScale;

                if (cachedRigidbodies != null)
                {
                    var rbData = new List<Dictionary<string, object>>();
                    foreach (var rb in cachedRigidbodies)
                    {
                        if (rb != null)
                        {
                            rbData.Add(new Dictionary<string, object>
                            {
                                ["name"] = rb.name,
                                ["mass"] = rb.mass,
                                ["velocity"] = new float[] { rb.linearVelocity.x, rb.linearVelocity.y, rb.linearVelocity.z },
                                ["angular_velocity"] = new float[] { rb.angularVelocity.x, rb.angularVelocity.y, rb.angularVelocity.z },
                                ["is_kinematic"] = rb.isKinematic,
                                ["is_sleeping"] = rb.IsSleeping()
                            });
                        }
                    }
                    baseData["rigidbodies"] = rbData;
                }

                return baseData;
            }
        }

        public class PhysicsJointDebugVisualizer : PhysicsDebugVisualizerBase
        {
            [SerializeField] public bool showConnections = true;
            [SerializeField] public bool showLimits = true;
            [SerializeField] public bool showAnchors = true;
            [SerializeField] public float anchorSize = 0.1f;
            
            private Joint[] cachedJoints;

            protected override void Update()
            {
                base.Update();
                if (needsUpdate)
                {
                    cachedJoints = GetComponentsInChildren<Joint>();
                    needsUpdate = false;
                }
            }

            protected override void DrawVisualization()
            {
                if (cachedJoints == null) return;

                Color originalColor = Gizmos.color;
                Gizmos.color = debugColor;

                foreach (var joint in cachedJoints)
                {
                    if (joint == null) continue;

                    Vector3 jointPos = joint.transform.TransformPoint(joint.anchor);

                    // Draw anchors
                    if (showAnchors)
                    {
                        Gizmos.color = Color.cyan;
                        Gizmos.DrawSphere(jointPos, anchorSize);
                        
                        if (joint.connectedBody != null)
                        {
                            Vector3 connectedPos = joint.connectedBody.transform.TransformPoint(joint.connectedAnchor);
                            Gizmos.DrawSphere(connectedPos, anchorSize);
                            
                            // Draw connection line
                            if (showConnections)
                            {
                                Gizmos.color = debugColor;
                                Gizmos.DrawLine(jointPos, connectedPos);
                            }
                        }
                    }

                    // Draw joint-specific visualizations
                    DrawJointSpecific(joint);

                    // Draw labels
                    if (showLabels)
                    {
                        string info = $"{joint.GetType().Name}\n";
                        info += $"Break Force: {joint.breakForce:F1}";
                        DrawLabel(jointPos + Vector3.up * 0.3f, info);
                    }
                }

                Gizmos.color = originalColor;
            }

            private void DrawJointSpecific(Joint joint)
            {
                switch (joint)
                {
                    case HingeJoint hinge:
                        DrawHingeJoint(hinge);
                        break;
                    case SpringJoint spring:
                        DrawSpringJoint(spring);
                        break;
                    case FixedJoint _:
                        // Fixed joints just show the connection
                        break;
                    case ConfigurableJoint configurable:
                        DrawConfigurableJoint(configurable);
                        break;
                }
            }

            private void DrawHingeJoint(HingeJoint hinge)
            {
                if (!showLimits || !hinge.useLimits) return;

                Vector3 pos = hinge.transform.TransformPoint(hinge.anchor);
                Vector3 axis = hinge.transform.TransformDirection(hinge.axis);
                
                Gizmos.color = Color.yellow;
                
                // Draw rotation axis
                Gizmos.DrawRay(pos, axis * 0.5f);
                Gizmos.DrawRay(pos, -axis * 0.5f);

                // Draw angular limits (simplified representation)
                float minAngle = hinge.limits.min * Mathf.Deg2Rad;
                float maxAngle = hinge.limits.max * Mathf.Deg2Rad;
                
                Vector3 perpendicular = Vector3.Cross(axis, Vector3.up).normalized;
                if (perpendicular.magnitude < 0.1f)
                {
                    perpendicular = Vector3.Cross(axis, Vector3.right).normalized;
                }

                Vector3 minDirection = Quaternion.AngleAxis(hinge.limits.min, axis) * perpendicular;
                Vector3 maxDirection = Quaternion.AngleAxis(hinge.limits.max, axis) * perpendicular;

                Gizmos.DrawRay(pos, minDirection * 0.3f);
                Gizmos.DrawRay(pos, maxDirection * 0.3f);
            }

            private void DrawSpringJoint(SpringJoint spring)
            {
                Vector3 jointPos = spring.transform.TransformPoint(spring.anchor);
                
                if (spring.connectedBody != null)
                {
                    Vector3 connectedPos = spring.connectedBody.transform.TransformPoint(spring.connectedAnchor);
                    float currentDistance = Vector3.Distance(jointPos, connectedPos);
                    
                    // Draw spring visualization
                    Gizmos.color = Color.green;
                    DrawSpringLine(jointPos, connectedPos, 8);
                    
                    // Draw min/max distance indicators
                    if (showLimits)
                    {
                        Gizmos.color = Color.red;
                        Gizmos.DrawWireSphere(jointPos, spring.minDistance);
                        Gizmos.DrawWireSphere(jointPos, spring.maxDistance);
                    }
                }
            }

            private void DrawConfigurableJoint(ConfigurableJoint configurable)
            {
                Vector3 pos = configurable.transform.TransformPoint(configurable.anchor);
                
                // Draw motion constraints visualization (simplified)
                Gizmos.color = Color.magenta;
                
                // X axis
                if (configurable.xMotion == ConfigurableJointMotion.Free)
                {
                    Gizmos.DrawRay(pos, configurable.transform.right * 0.5f);
                    Gizmos.DrawRay(pos, -configurable.transform.right * 0.5f);
                }
                
                // Y axis
                if (configurable.yMotion == ConfigurableJointMotion.Free)
                {
                    Gizmos.DrawRay(pos, configurable.transform.up * 0.5f);
                    Gizmos.DrawRay(pos, -configurable.transform.up * 0.5f);
                }
                
                // Z axis
                if (configurable.zMotion == ConfigurableJointMotion.Free)
                {
                    Gizmos.DrawRay(pos, configurable.transform.forward * 0.5f);
                    Gizmos.DrawRay(pos, -configurable.transform.forward * 0.5f);
                }
            }

            private void DrawSpringLine(Vector3 start, Vector3 end, int coils)
            {
                Vector3 direction = (end - start);
                float distance = direction.magnitude;
                direction = direction.normalized;
                
                Vector3 perpendicular = Vector3.Cross(direction, Vector3.up).normalized;
                if (perpendicular.magnitude < 0.1f)
                {
                    perpendicular = Vector3.Cross(direction, Vector3.right).normalized;
                }

                float coilRadius = 0.1f;
                Vector3 lastPoint = start;

                for (int i = 0; i <= coils * 8; i++)
                {
                    float t = (float)i / (coils * 8);
                    float angle = t * coils * 2 * Mathf.PI;
                    
                    Vector3 coilOffset = perpendicular * Mathf.Sin(angle) * coilRadius;
                    Vector3 point = Vector3.Lerp(start, end, t) + coilOffset;
                    
                    if (i > 0)
                    {
                        Gizmos.DrawLine(lastPoint, point);
                    }
                    lastPoint = point;
                }
            }

            public override Dictionary<string, object> CaptureFrame()
            {
                var baseData = base.CaptureFrame();
                baseData["joint_count"] = cachedJoints?.Length ?? 0;
                baseData["show_connections"] = showConnections;
                baseData["show_limits"] = showLimits;
                baseData["show_anchors"] = showAnchors;
                return baseData;
            }
        }

        public class PhysicsForceDebugVisualizer : PhysicsDebugVisualizerBase
        {
            [SerializeField] public bool showGravity = true;
            [SerializeField] public bool showAppliedForces = true;
            [SerializeField] public float forceScale = 0.1f;
            [SerializeField] public float gravityArrowSize = 1.0f;
            
            private Rigidbody[] cachedRigidbodies;
            private Dictionary<Rigidbody, Vector3> lastFrameForces = new Dictionary<Rigidbody, Vector3>();

            protected override void Update()
            {
                base.Update();
                if (needsUpdate)
                {
                    cachedRigidbodies = GetComponentsInChildren<Rigidbody>();
                    needsUpdate = false;
                }
            }

            protected override void DrawVisualization()
            {
                if (cachedRigidbodies == null) return;

                Color originalColor = Gizmos.color;

                foreach (var rb in cachedRigidbodies)
                {
                    if (rb == null) continue;

                    Vector3 position = rb.transform.position;

                    // Draw gravity
                    if (showGravity && rb.useGravity && !rb.isKinematic)
                    {
                        Gizmos.color = Color.blue;
                        Vector3 gravityForce = Physics.gravity * rb.mass;
                        Gizmos.DrawRay(position, gravityForce.normalized * gravityArrowSize);
                        
                        if (showLabels)
                        {
                            DrawLabel(position + gravityForce.normalized * gravityArrowSize, $"Gravity\n{gravityForce.magnitude:F1}N");
                        }
                    }

                    // Draw accumulated forces (approximation)
                    if (showAppliedForces)
                    {
                        Vector3 accumulatedForce = rb.GetAccumulatedForce();
                        if (accumulatedForce.magnitude > 0.01f)
                        {
                            Gizmos.color = Color.red;
                            Vector3 forceVector = accumulatedForce * forceScale;
                            Gizmos.DrawRay(position, forceVector);
                            
                            if (showLabels)
                            {
                                DrawLabel(position + forceVector, $"Force\n{accumulatedForce.magnitude:F1}N");
                            }
                        }
                    }

                    // Draw velocity-based force estimation
                    if (lastFrameForces.ContainsKey(rb))
                    {
                        Vector3 velocityChange = rb.linearVelocity - lastFrameForces[rb];
                        Vector3 estimatedForce = velocityChange * rb.mass / Time.fixedDeltaTime;
                        
                        if (estimatedForce.magnitude > 0.1f)
                        {
                            Gizmos.color = Color.yellow;
                            Vector3 forceVector = estimatedForce * forceScale;
                            Gizmos.DrawRay(position, forceVector);
                        }
                    }

                    lastFrameForces[rb] = rb.linearVelocity;
                }

                Gizmos.color = originalColor;
            }

            public override Dictionary<string, object> CaptureFrame()
            {
                var baseData = base.CaptureFrame();
                baseData["rigidbody_count"] = cachedRigidbodies?.Length ?? 0;
                baseData["show_gravity"] = showGravity;
                baseData["show_applied_forces"] = showAppliedForces;
                baseData["force_scale"] = forceScale;
                return baseData;
            }
        }

        public class PhysicsContactDebugVisualizer : PhysicsDebugVisualizerBase
        {
            [SerializeField] public bool showContactPoints = true;
            [SerializeField] public bool showContactNormals = true;
            [SerializeField] public bool showContactImpulse = true;
            [SerializeField] public float normalLength = 0.5f;
            [SerializeField] public float contactPointSize = 0.05f;
            
            private List<ContactPairPoint> contactPoints = new List<ContactPairPoint>();

            private void OnEnable()
            {
                Physics.ContactEvent += OnContactEvent;
            }

            private void OnDisable()
            {
                Physics.ContactEvent -= OnContactEvent;
            }

            private void OnContactEvent(PhysicsScene scene, Unity.Collections.NativeArray<ContactPairHeader>.ReadOnly contactPairHeaders)
            {
                contactPoints.Clear();
                
                foreach (var header in contactPairHeaders)
                {
                    for (int i = 0; i < header.pairCount; i++)
                    {
                        var pair = header.GetContactPair(i);
                        
                        for (int j = 0; j < pair.contactCount; j++)
                        {
                            var contact = pair.GetContactPoint(j);
                            contactPoints.Add(contact);
                        }
                    }
                }
            }

            protected override void DrawVisualization()
            {
                if (!enabled || contactPoints == null) return;

                Color originalColor = Gizmos.color;

                foreach (var contact in contactPoints)
                {
                    // Draw contact points
                    if (showContactPoints)
                    {
                        Gizmos.color = Color.red;
                        Gizmos.DrawSphere(contact.position, contactPointSize);
                    }

                    // Draw contact normals
                    if (showContactNormals)
                    {
                        Gizmos.color = Color.green;
                        Gizmos.DrawRay(contact.position, contact.normal * normalLength);
                    }

                    // Draw impulse magnitude visualization
                    if (showContactImpulse)
                    {
                        Gizmos.color = Color.yellow;
                        float impulseScale = contact.impulse.magnitude * 0.1f;
                        Gizmos.DrawRay(contact.position, contact.impulse.normalized * impulseScale);
                    }

                    // Draw labels
                    if (showLabels)
                    {
                        string info = $"Separation: {contact.separation:F3}\n";
                        info += $"Impulse: {contact.impulse.magnitude:F2}";
                        DrawLabel(contact.position + Vector3.up * 0.2f, info);
                    }
                }

                Gizmos.color = originalColor;
            }

            public override Dictionary<string, object> CaptureFrame()
            {
                var baseData = base.CaptureFrame();
                baseData["contact_count"] = contactPoints?.Count ?? 0;
                baseData["show_contact_points"] = showContactPoints;
                baseData["show_contact_normals"] = showContactNormals;
                baseData["show_contact_impulse"] = showContactImpulse;
                return baseData;
            }
        }

        public class PhysicsRaycastDebugVisualizer : PhysicsDebugVisualizerBase
        {
            [SerializeField] public bool trackAllRaycasts = true;
            [SerializeField] public float rayDisplayDuration = 1.0f;
            [SerializeField] public int maxStoredRays = 100;
            [SerializeField] public bool showHitPoints = true;
            [SerializeField] public bool showHitNormals = true;
            
            private List<RaycastInfo> raycastHistory = new List<RaycastInfo>();

            [System.Serializable]
            private class RaycastInfo
            {
                public Vector3 origin;
                public Vector3 direction;
                public float distance;
                public bool hit;
                public Vector3 hitPoint;
                public Vector3 hitNormal;
                public float timestamp;
                public Color rayColor;
            }

            // This would need to be hooked into the physics system to track raycasts
            // For demonstration, we'll show how it would work
            public void RegisterRaycast(Vector3 origin, Vector3 direction, float distance, RaycastHit hit, bool didHit)
            {
                if (!enabled || !trackAllRaycasts) return;

                var rayInfo = new RaycastInfo
                {
                    origin = origin,
                    direction = direction.normalized,
                    distance = distance,
                    hit = didHit,
                    hitPoint = didHit ? hit.point : origin + direction.normalized * distance,
                    hitNormal = didHit ? hit.normal : Vector3.zero,
                    timestamp = Time.time,
                    rayColor = didHit ? Color.red : Color.green
                };

                raycastHistory.Add(rayInfo);

                // Limit storage
                if (raycastHistory.Count > maxStoredRays)
                {
                    raycastHistory.RemoveAt(0);
                }
            }

            protected override void Update()
            {
                base.Update();
                
                // Remove old raycast records
                float currentTime = Time.time;
                raycastHistory.RemoveAll(ray => currentTime - ray.timestamp > rayDisplayDuration);
            }

            protected override void DrawVisualization()
            {
                if (!enabled || raycastHistory == null) return;

                Color originalColor = Gizmos.color;

                foreach (var ray in raycastHistory)
                {
                    float age = Time.time - ray.timestamp;
                    float alpha = 1.0f - (age / rayDisplayDuration);
                    
                    // Draw ray
                    Gizmos.color = new Color(ray.rayColor.r, ray.rayColor.g, ray.rayColor.b, alpha);
                    
                    if (ray.hit)
                    {
                        Gizmos.DrawRay(ray.origin, ray.direction * Vector3.Distance(ray.origin, ray.hitPoint));
                        
                        // Draw hit point
                        if (showHitPoints)
                        {
                            Gizmos.DrawSphere(ray.hitPoint, 0.05f);
                        }
                        
                        // Draw hit normal
                        if (showHitNormals)
                        {
                            Gizmos.color = Color.blue;
                            Gizmos.DrawRay(ray.hitPoint, ray.hitNormal * 0.5f);
                        }
                    }
                    else
                    {
                        Gizmos.DrawRay(ray.origin, ray.direction * ray.distance);
                    }

                    // Draw labels
                    if (showLabels && ray.hit)
                    {
                        DrawLabel(ray.hitPoint + Vector3.up * 0.1f, $"Hit: {Vector3.Distance(ray.origin, ray.hitPoint):F2}m");
                    }
                }

                Gizmos.color = originalColor;
            }

            public override Dictionary<string, object> CaptureFrame()
            {
                var baseData = base.CaptureFrame();
                baseData["raycast_count"] = raycastHistory?.Count ?? 0;
                baseData["track_all_raycasts"] = trackAllRaycasts;
                baseData["ray_display_duration"] = rayDisplayDuration;
                baseData["max_stored_rays"] = maxStoredRays;
                
                var rayData = new List<Dictionary<string, object>>();
                if (raycastHistory != null)
                {
                    foreach (var ray in raycastHistory)
                    {
                        rayData.Add(new Dictionary<string, object>
                        {
                            ["origin"] = new float[] { ray.origin.x, ray.origin.y, ray.origin.z },
                            ["direction"] = new float[] { ray.direction.x, ray.direction.y, ray.direction.z },
                            ["distance"] = ray.distance,
                            ["hit"] = ray.hit,
                            ["hit_point"] = ray.hit ? new float[] { ray.hitPoint.x, ray.hitPoint.y, ray.hitPoint.z } : null,
                            ["timestamp"] = ray.timestamp
                        });
                    }
                }
                baseData["raycasts"] = rayData;
                
                return baseData;
            }
        }

        private static object HandlePhysicsPerformanceProfiling(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                return Response.Error($"Unknown action: '{action}'. Valid actions: {string.Join(", ", ValidActions)}");
            }

            try
            {
                switch (action)
                {
                    case "create":
                        return CreatePhysicsProfiler(@params);
                    case "modify":
                        return ModifyPhysicsProfiler(@params);
                    case "delete":
                        return DeletePhysicsProfiler(@params);
                    case "list":
                        return ListPhysicsProfilers(@params);
                    case "get_info":
                        return GetPhysicsProfilerInfo(@params);
                    case "start":
                        return StartPhysicsProfiler(@params);
                    case "stop":
                        return StopPhysicsProfiler(@params);
                    case "get_results":
                        return GetPhysicsProfilerResults(@params);
                    case "export_results":
                        return ExportPhysicsProfilerResults(@params);
                    case "clear_results":
                        return ClearPhysicsProfilerResults(@params);
                    default:
                        return Response.Error($"Action '{action}' not implemented for physics performance profiling.");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedPhysics] Physics performance profiling action '{action}' failed: {e}");
                return Response.Error($"Internal error processing physics performance profiling: {e.Message}");
            }
        }

        private static object CreatePhysicsProfiler(JObject @params)
        {
            string profilerName = @params["profiler_name"]?.ToString();
            string profilerType = @params["profiler_type"]?.ToString()?.ToLower();

            if (string.IsNullOrEmpty(profilerName))
            {
                return Response.Error("Profiler name is required.");
            }

            if (string.IsNullOrEmpty(profilerType))
            {
                return Response.Error("Profiler type is required.");
            }

            try
            {
                // Create GameObject for the physics profiler
                var profilerGO = new GameObject($"PhysicsProfiler_{profilerName}");
                profilerGO.tag = "EditorOnly";

                // Add appropriate component based on profiler type
                Component profilerComponent = null;
                switch (profilerType)
                {
                    case "general":
                        profilerComponent = profilerGO.AddComponent<PhysicsGeneralProfiler>();
                        break;
                    case "rigidbody":
                        profilerComponent = profilerGO.AddComponent<PhysicsRigidbodyProfiler>();
                        break;
                    case "collision":
                        profilerComponent = profilerGO.AddComponent<PhysicsCollisionProfiler>();
                        break;
                    case "joint":
                        profilerComponent = profilerGO.AddComponent<PhysicsJointProfiler>();
                        break;
                    case "raycast":
                        profilerComponent = profilerGO.AddComponent<PhysicsRaycastProfiler>();
                        break;
                    case "memory":
                        profilerComponent = profilerGO.AddComponent<PhysicsMemoryProfiler>();
                        break;
                    default:
                        UnityEngine.Object.DestroyImmediate(profilerGO);
                        return Response.Error($"Unknown profiler type: '{profilerType}'. Valid types: general, rigidbody, collision, joint, raycast, memory");
                }

                // Configure profiler properties
                if (@params["enabled"] != null)
                {
                    bool enabled = @params["enabled"].ToObject<bool>();
                    SetProfilerProperty(profilerComponent, "enabled", enabled);
                }

                if (@params["sample_rate"] != null)
                {
                    float sampleRate = @params["sample_rate"].ToObject<float>();
                    SetProfilerProperty(profilerComponent, "sampleRate", sampleRate);
                }

                if (@params["auto_start"] != null)
                {
                    bool autoStart = @params["auto_start"].ToObject<bool>();
                    SetProfilerProperty(profilerComponent, "autoStart", autoStart);
                }

                if (@params["max_samples"] != null)
                {
                    int maxSamples = @params["max_samples"].ToObject<int>();
                    SetProfilerProperty(profilerComponent, "maxSamples", maxSamples);
                }

                if (@params["log_to_console"] != null)
                {
                    bool logToConsole = @params["log_to_console"].ToObject<bool>();
                    SetProfilerProperty(profilerComponent, "logToConsole", logToConsole);
                }

                if (@params["detailed_metrics"] != null)
                {
                    bool detailedMetrics = @params["detailed_metrics"].ToObject<bool>();
                    SetProfilerProperty(profilerComponent, "detailedMetrics", detailedMetrics);
                }

                // Create prefab
                string prefabPath = $"Assets/PhysicsProfilers/{profilerName}.prefab";
                string directory = Path.GetDirectoryName(prefabPath);
                if (!AssetDatabase.IsValidFolder(directory))
                {
                    Directory.CreateDirectory(Path.Combine(Application.dataPath, "PhysicsProfilers"));
                    AssetDatabase.Refresh();
                }

                var prefab = PrefabUtility.SaveAsPrefabAsset(profilerGO, prefabPath);
                UnityEngine.Object.DestroyImmediate(profilerGO);

                var profilerInfo = new Dictionary<string, object>
                {
                    ["name"] = profilerName,
                    ["profiler_type"] = profilerType,
                    ["prefab_path"] = prefabPath,
                    ["component_type"] = profilerComponent.GetType().Name,
                    ["enabled"] = @params["enabled"]?.ToObject<bool>() ?? true,
                    ["sample_rate"] = @params["sample_rate"]?.ToObject<float>() ?? 60.0f,
                    ["auto_start"] = @params["auto_start"]?.ToObject<bool>() ?? false,
                    ["max_samples"] = @params["max_samples"]?.ToObject<int>() ?? 1000,
                    ["log_to_console"] = @params["log_to_console"]?.ToObject<bool>() ?? false,
                    ["detailed_metrics"] = @params["detailed_metrics"]?.ToObject<bool>() ?? false
                };

                return Response.Success($"Physics profiler '{profilerName}' created successfully.", profilerInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create physics profiler: {e.Message}");
            }
        }

        private static object ModifyPhysicsProfiler(JObject @params)
        {
            string profilerName = @params["profiler_name"]?.ToString();
            if (string.IsNullOrEmpty(profilerName))
            {
                return Response.Error("Profiler name is required.");
            }

            try
            {
                // Find existing prefab
                string[] guids = AssetDatabase.FindAssets($"{profilerName} t:Prefab");
                if (guids.Length == 0)
                {
                    return Response.Error($"Physics profiler '{profilerName}' not found.");
                }

                string prefabPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                if (prefab == null)
                {
                    return Response.Error($"Failed to load physics profiler '{profilerName}'.");
                }

                // Instantiate for modification
                var instance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
                var profilerComponents = instance.GetComponents<MonoBehaviour>();
                var profilerComponent = profilerComponents.FirstOrDefault(c => c.GetType().Name.Contains("PhysicsProfiler") || c.GetType().Name.Contains("Profiler"));

                if (profilerComponent == null)
                {
                    UnityEngine.Object.DestroyImmediate(instance);
                    return Response.Error($"No physics profiler component found in '{profilerName}'.");
                }

                bool modified = false;

                // Update properties
                if (@params["enabled"] != null)
                {
                    bool enabled = @params["enabled"].ToObject<bool>();
                    SetProfilerProperty(profilerComponent, "enabled", enabled);
                    modified = true;
                }

                if (@params["sample_rate"] != null)
                {
                    float sampleRate = @params["sample_rate"].ToObject<float>();
                    SetProfilerProperty(profilerComponent, "sampleRate", sampleRate);
                    modified = true;
                }

                if (@params["auto_start"] != null)
                {
                    bool autoStart = @params["auto_start"].ToObject<bool>();
                    SetProfilerProperty(profilerComponent, "autoStart", autoStart);
                    modified = true;
                }

                if (@params["max_samples"] != null)
                {
                    int maxSamples = @params["max_samples"].ToObject<int>();
                    SetProfilerProperty(profilerComponent, "maxSamples", maxSamples);
                    modified = true;
                }

                if (@params["log_to_console"] != null)
                {
                    bool logToConsole = @params["log_to_console"].ToObject<bool>();
                    SetProfilerProperty(profilerComponent, "logToConsole", logToConsole);
                    modified = true;
                }

                if (@params["detailed_metrics"] != null)
                {
                    bool detailedMetrics = @params["detailed_metrics"].ToObject<bool>();
                    SetProfilerProperty(profilerComponent, "detailedMetrics", detailedMetrics);
                    modified = true;
                }

                if (modified)
                {
                    // Save changes back to prefab
                    PrefabUtility.SaveAsPrefabAsset(instance, prefabPath);
                    UnityEngine.Object.DestroyImmediate(instance);

                    var profilerInfo = new Dictionary<string, object>
                    {
                        ["name"] = profilerName,
                        ["prefab_path"] = prefabPath,
                        ["component_type"] = profilerComponent.GetType().Name,
                        ["modified"] = true
                    };

                    return Response.Success($"Physics profiler '{profilerName}' modified successfully.", profilerInfo);
                }
                else
                {
                    UnityEngine.Object.DestroyImmediate(instance);
                    return Response.Error("No valid parameters provided for modification.");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to modify physics profiler: {e.Message}");
            }
        }

        private static object DeletePhysicsProfiler(JObject @params)
        {
            string profilerName = @params["profiler_name"]?.ToString();
            if (string.IsNullOrEmpty(profilerName))
            {
                return Response.Error("Profiler name is required.");
            }

            try
            {
                string[] guids = AssetDatabase.FindAssets($"{profilerName} t:Prefab");
                if (guids.Length == 0)
                {
                    return Response.Error($"Physics profiler '{profilerName}' not found.");
                }

                string prefabPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                
                // Check if profiler is in use
                bool forceDelete = @params["force"]?.ToObject<bool>() ?? false;
                if (!forceDelete)
                {
                    var instancesInScene = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsInactive.Exclude, FindObjectsSortMode.None)
                        .Where(go => PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(go) == prefabPath)
                        .Select(go => go.name)
                        .ToList();

                    if (instancesInScene.Count > 0)
                    {
                        return Response.Error($"Physics profiler '{profilerName}' is in use by {instancesInScene.Count} instances. Use 'force: true' to delete anyway.");
                    }
                }

                AssetDatabase.DeleteAsset(prefabPath);
                AssetDatabase.SaveAssets();

                return Response.Success($"Physics profiler '{profilerName}' deleted successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to delete physics profiler: {e.Message}");
            }
        }

        private static object ListPhysicsProfilers(JObject @params)
        {
            try
            {
                string[] guids = AssetDatabase.FindAssets("PhysicsProfiler t:Prefab");
                var profilers = new List<Dictionary<string, object>>();

                foreach (string guid in guids)
                {
                    string prefabPath = AssetDatabase.GUIDToAssetPath(guid);
                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                    if (prefab != null)
                    {
                        var profilerComponents = prefab.GetComponents<MonoBehaviour>();
                        var profilerComponent = profilerComponents.FirstOrDefault(c => c.GetType().Name.Contains("PhysicsProfiler") || c.GetType().Name.Contains("Profiler"));

                        if (profilerComponent != null)
                        {
                            var instancesInScene = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsSortMode.None)
                                .Where(go => PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(go) == prefabPath)
                                .Select(go => go.name)
                                .ToList();

                            profilers.Add(new Dictionary<string, object>
                            {
                                ["name"] = prefab.name.Replace("PhysicsProfiler_", ""),
                                ["prefab_path"] = prefabPath,
                                ["component_type"] = profilerComponent.GetType().Name,
                                ["profiler_type"] = GetProfilerTypeFromComponent(profilerComponent),
                                ["enabled"] = GetProfilerProperty(profilerComponent, "enabled") ?? true,
                                ["is_running"] = GetProfilerProperty(profilerComponent, "isRunning") ?? false,
                                ["instances_in_scene"] = instancesInScene.Count,
                                ["instance_names"] = instancesInScene.Take(5).ToList()
                            });
                        }
                    }
                }

                return Response.Success($"Found {profilers.Count} physics profilers.", profilers);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list physics profilers: {e.Message}");
            }
        }

        private static object GetPhysicsProfilerInfo(JObject @params)
        {
            string profilerName = @params["profiler_name"]?.ToString();
            if (string.IsNullOrEmpty(profilerName))
            {
                return Response.Error("Profiler name is required.");
            }

            try
            {
                string[] guids = AssetDatabase.FindAssets($"{profilerName} t:Prefab");
                if (guids.Length == 0)
                {
                    return Response.Error($"Physics profiler '{profilerName}' not found.");
                }

                string prefabPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                if (prefab == null)
                {
                    return Response.Error($"Failed to load physics profiler '{profilerName}'.");
                }

                var profilerComponents = prefab.GetComponents<MonoBehaviour>();
                var profilerComponent = profilerComponents.FirstOrDefault(c => c.GetType().Name.Contains("PhysicsProfiler") || c.GetType().Name.Contains("Profiler"));

                if (profilerComponent == null)
                {
                    return Response.Error($"No physics profiler component found in '{profilerName}'.");
                }

                var instancesInScene = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsSortMode.None)
                    .Where(go => PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(go) == prefabPath)
                    .Select(go => new Dictionary<string, object>
                    {
                        ["name"] = go.name,
                        ["path"] = GetGameObjectPath(go),
                        ["active"] = go.activeInHierarchy,
                        ["enabled"] = profilerComponent.enabled,
                        ["is_running"] = GetProfilerProperty(profilerComponent, "isRunning") ?? false
                    })
                    .ToList();

                var profilerInfo = new Dictionary<string, object>
                {
                    ["name"] = profilerName,
                    ["prefab_path"] = prefabPath,
                    ["component_type"] = profilerComponent.GetType().Name,
                    ["profiler_type"] = GetProfilerTypeFromComponent(profilerComponent),
                    ["instances_count"] = instancesInScene.Count,
                    ["instances"] = instancesInScene,
                    ["enabled"] = GetProfilerProperty(profilerComponent, "enabled"),
                    ["sample_rate"] = GetProfilerProperty(profilerComponent, "sampleRate"),
                    ["auto_start"] = GetProfilerProperty(profilerComponent, "autoStart"),
                    ["max_samples"] = GetProfilerProperty(profilerComponent, "maxSamples"),
                    ["log_to_console"] = GetProfilerProperty(profilerComponent, "logToConsole"),
                    ["detailed_metrics"] = GetProfilerProperty(profilerComponent, "detailedMetrics"),
                    ["is_running"] = GetProfilerProperty(profilerComponent, "isRunning"),
                    ["sample_count"] = GetProfilerProperty(profilerComponent, "sampleCount")
                };

                return Response.Success($"Physics profiler '{profilerName}' information retrieved.", profilerInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get physics profiler info: {e.Message}");
            }
        }

        private static object StartPhysicsProfiler(JObject @params)
        {
            string profilerName = @params["profiler_name"]?.ToString();
            if (string.IsNullOrEmpty(profilerName))
            {
                return Response.Error("Profiler name is required.");
            }

            try
            {
                var profilers = UnityEngine.Object.FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None)
                    .Where(mb => mb.name.Contains($"PhysicsProfiler_{profilerName}"))
                    .ToList();

                if (profilers.Count == 0)
                {
                    return Response.Error($"No active instances of physics profiler '{profilerName}' found.");
                }

                int startedCount = 0;
                foreach (var profiler in profilers)
                {
                    var startMethod = profiler.GetType().GetMethod("StartProfiling");
                    if (startMethod != null)
                    {
                        startMethod.Invoke(profiler, null);
                        startedCount++;
                    }
                }

                return Response.Success($"Physics profiler '{profilerName}' started for {startedCount} instances.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to start physics profiler: {e.Message}");
            }
        }

        private static object StopPhysicsProfiler(JObject @params)
        {
            string profilerName = @params["profiler_name"]?.ToString();
            if (string.IsNullOrEmpty(profilerName))
            {
                return Response.Error("Profiler name is required.");
            }

            try
            {
                var profilers = UnityEngine.Object.FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None)
                    .Where(mb => mb.name.Contains($"PhysicsProfiler_{profilerName}"))
                    .ToList();

                if (profilers.Count == 0)
                {
                    return Response.Error($"No active instances of physics profiler '{profilerName}' found.");
                }

                int stoppedCount = 0;
                foreach (var profiler in profilers)
                {
                    var stopMethod = profiler.GetType().GetMethod("StopProfiling");
                    if (stopMethod != null)
                    {
                        stopMethod.Invoke(profiler, null);
                        stoppedCount++;
                    }
                }

                return Response.Success($"Physics profiler '{profilerName}' stopped for {stoppedCount} instances.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to stop physics profiler: {e.Message}");
            }
        }

        private static object GetPhysicsProfilerResults(JObject @params)
        {
            string profilerName = @params["profiler_name"]?.ToString();
            if (string.IsNullOrEmpty(profilerName))
            {
                return Response.Error("Profiler name is required.");
            }

            try
            {
                var profilers = UnityEngine.Object.FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None)
                    .Where(mb => mb.name.Contains($"PhysicsProfiler_{profilerName}"))
                    .ToList();

                if (profilers.Count == 0)
                {
                    return Response.Error($"No active instances of physics profiler '{profilerName}' found.");
                }

                var allResults = new List<Dictionary<string, object>>();
                foreach (var profiler in profilers)
                {
                    var getResultsMethod = profiler.GetType().GetMethod("GetResults");
                    if (getResultsMethod != null)
                    {
                        var results = getResultsMethod.Invoke(profiler, null) as Dictionary<string, object>;
                        if (results != null)
                        {
                            results["profiler_instance"] = profiler.name;
                            allResults.Add(results);
                        }
                    }
                }

                var aggregatedResults = new Dictionary<string, object>
                {
                    ["profiler_name"] = profilerName,
                    ["timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    ["instances_count"] = profilers.Count,
                    ["results"] = allResults
                };

                return Response.Success($"Physics profiler '{profilerName}' results retrieved.", aggregatedResults);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get physics profiler results: {e.Message}");
            }
        }

        private static object ExportPhysicsProfilerResults(JObject @params)
        {
            string profilerName = @params["profiler_name"]?.ToString();
            string outputPath = @params["output_path"]?.ToString();
            string format = @params["format"]?.ToString()?.ToLower() ?? "json";

            if (string.IsNullOrEmpty(profilerName))
            {
                return Response.Error("Profiler name is required.");
            }

            try
            {
                var profilers = UnityEngine.Object.FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None)
                    .Where(mb => mb.name.Contains($"PhysicsProfiler_{profilerName}"))
                    .ToList();

                if (profilers.Count == 0)
                {
                    return Response.Error($"No active instances of physics profiler '{profilerName}' found.");
                }

                var allResults = new List<Dictionary<string, object>>();
                foreach (var profiler in profilers)
                {
                    var getResultsMethod = profiler.GetType().GetMethod("GetResults");
                    if (getResultsMethod != null)
                    {
                        var results = getResultsMethod.Invoke(profiler, null) as Dictionary<string, object>;
                        if (results != null)
                        {
                            results["profiler_instance"] = profiler.name;
                            allResults.Add(results);
                        }
                    }
                }

                var exportData = new Dictionary<string, object>
                {
                    ["profiler_name"] = profilerName,
                    ["export_timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    ["instances_count"] = profilers.Count,
                    ["format"] = format,
                    ["results"] = allResults
                };

                // Export to file
                string fileName = $"PhysicsProfiler_{profilerName}_{DateTime.Now:yyyyMMdd_HHmmss}";
                string fullPath;

                if (!string.IsNullOrEmpty(outputPath))
                {
                    fullPath = Path.Combine(Application.dataPath, outputPath, $"{fileName}.{format}");
                }
                else
                {
                    fullPath = Path.Combine(Application.dataPath, "PhysicsProfilerResults", $"{fileName}.{format}");
                }

                Directory.CreateDirectory(Path.GetDirectoryName(fullPath));

                switch (format)
                {
                    case "json":
                        string json = JsonConvert.SerializeObject(exportData, Formatting.Indented);
                        File.WriteAllText(fullPath, json);
                        break;
                    case "csv":
                        string csv = ConvertResultsToCsv(allResults);
                        File.WriteAllText(fullPath, csv);
                        break;
                    default:
                        return Response.Error($"Unsupported export format: '{format}'. Supported formats: json, csv");
                }

                AssetDatabase.Refresh();
                exportData["exported_to"] = fullPath;

                return Response.Success($"Physics profiler '{profilerName}' results exported to {fullPath}.", exportData);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to export physics profiler results: {e.Message}");
            }
        }

        private static object ClearPhysicsProfilerResults(JObject @params)
        {
            string profilerName = @params["profiler_name"]?.ToString();
            if (string.IsNullOrEmpty(profilerName))
            {
                return Response.Error("Profiler name is required.");
            }

            try
            {
                var profilers = UnityEngine.Object.FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None)
                    .Where(mb => mb.name.Contains($"PhysicsProfiler_{profilerName}"))
                    .ToList();

                if (profilers.Count == 0)
                {
                    return Response.Error($"No active instances of physics profiler '{profilerName}' found.");
                }

                int clearedCount = 0;
                foreach (var profiler in profilers)
                {
                    var clearMethod = profiler.GetType().GetMethod("ClearResults");
                    if (clearMethod != null)
                    {
                        clearMethod.Invoke(profiler, null);
                        clearedCount++;
                    }
                }

                return Response.Success($"Physics profiler '{profilerName}' results cleared for {clearedCount} instances.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to clear physics profiler results: {e.Message}");
            }
        }

        private static void SetProfilerProperty(Component component, string propertyName, object value)
        {
            var field = component.GetType().GetField(propertyName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (field != null)
            {
                field.SetValue(component, value);
                return;
            }

            var property = component.GetType().GetProperty(propertyName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (property != null && property.CanWrite)
            {
                property.SetValue(component, value);
            }
        }

        private static object GetProfilerProperty(Component component, string propertyName)
        {
            var field = component.GetType().GetField(propertyName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (field != null)
            {
                return field.GetValue(component);
            }

            var property = component.GetType().GetProperty(propertyName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (property != null && property.CanRead)
            {
                return property.GetValue(component);
            }

            return null;
        }

        private static string GetProfilerTypeFromComponent(Component component)
        {
            string typeName = component.GetType().Name.ToLower();
            if (typeName.Contains("general")) return "general";
            if (typeName.Contains("rigidbody")) return "rigidbody";
            if (typeName.Contains("collision")) return "collision";
            if (typeName.Contains("joint")) return "joint";
            if (typeName.Contains("raycast")) return "raycast";
            if (typeName.Contains("memory")) return "memory";
            return "unknown";
        }

        private static string ConvertResultsToCsv(List<Dictionary<string, object>> results)
        {
            if (results.Count == 0) return "";

            var csv = new StringBuilder();
            var headers = results.First().Keys.ToList();
            csv.AppendLine(string.Join(",", headers));

            foreach (var result in results)
            {
                var values = headers.Select(h => result.ContainsKey(h) ? result[h]?.ToString() ?? "" : "").ToList();
                csv.AppendLine(string.Join(",", values));
            }

            return csv.ToString();
        }

        /// <summary>
        /// [UNITY 6.2] - Real physics profiler component classes using Unity APIs.
        /// </summary>
        public class PhysicsGeneralProfiler : MonoBehaviour
        {
            [Header("Profiler Settings")]
            public new bool enabled = true;
            public float sampleRate = 60.0f;
            public bool autoStart = false;
            public int maxSamples = 1000;
            public bool logToConsole = false;
            public bool detailedMetrics = false;
            public bool isRunning = false;
            public int sampleCount = 0;

            [Header("Runtime Data")]
            [SerializeField] private List<Dictionary<string, object>> results = new List<Dictionary<string, object>>();

            // Unity Profiler API components
            private CustomSampler physicsSampler;
            private ProfilerRecorder mainThreadRecorder;
            private ProfilerRecorder fixedUpdateRecorder;
            private ProfilerRecorder physicsUpdateRecorder;
            private ProfilerRecorder systemMemoryRecorder;
            private ProfilerRecorder gcMemoryRecorder;
            private ProfilerRecorder totalReservedMemoryRecorder;
            private ProfilerRecorder totalAllocatedMemoryRecorder;

            // Performance tracking
            private float lastSampleTime = 0f;
            private float sampleInterval = 0f;
            private int frameCount = 0;
            private bool profilersInitialized = false;

            private void Start()
            {
                if (autoStart)
                {
                    StartProfiling();
                }
                
                sampleInterval = 1.0f / sampleRate;
            }

            private void InitializeProfilers()
            {
                if (profilersInitialized) return;

                try
                {
                    // Create custom sampler for physics operations
                    physicsSampler = CustomSampler.Create("Physics General Profiler", false);

                    // Initialize ProfilerRecorders for various metrics
                    mainThreadRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Internal, "Main Thread", maxSamples);
                    fixedUpdateRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Internal, "FixedUpdate", maxSamples);
                    
                    // Physics-specific profiler (if available)
                    physicsUpdateRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Internal, "Physics", maxSamples);
                    
                    // Memory profilers
                    systemMemoryRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Memory, "System Used Memory", maxSamples);
                    gcMemoryRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Memory, "GC Reserved Memory", maxSamples);
                    totalReservedMemoryRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Memory, "Total Reserved Memory", maxSamples);
                    totalAllocatedMemoryRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Memory, "Total Used Memory", maxSamples);

                    profilersInitialized = true;
                    
                    if (logToConsole)
                    {
                        Debug.Log("[PhysicsGeneralProfiler] Profilers initialized successfully");
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[PhysicsGeneralProfiler] Failed to initialize profilers: {e.Message}");
                    profilersInitialized = false;
                }
            }

            private void DisposeProfilers()
            {
                if (!profilersInitialized) return;

                try
                {
                    mainThreadRecorder.Dispose();
                    fixedUpdateRecorder.Dispose();
                    physicsUpdateRecorder.Dispose();
                    systemMemoryRecorder.Dispose();
                    gcMemoryRecorder.Dispose();
                    totalReservedMemoryRecorder.Dispose();
                    totalAllocatedMemoryRecorder.Dispose();

                    profilersInitialized = false;
                    
                    if (logToConsole)
                    {
                        Debug.Log("[PhysicsGeneralProfiler] Profilers disposed successfully");
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[PhysicsGeneralProfiler] Error disposing profilers: {e.Message}");
                }
            }

            public void StartProfiling()
            {
                if (isRunning) return;

                InitializeProfilers();
                isRunning = true;
                frameCount = 0;
                lastSampleTime = Time.time;
                
                if (logToConsole)
                {
                    Debug.Log("[PhysicsGeneralProfiler] Started profiling");
                }
            }

            public void StopProfiling()
            {
                if (!isRunning) return;

                isRunning = false;
                
                if (logToConsole)
                {
                    Debug.Log($"[PhysicsGeneralProfiler] Stopped profiling. Collected {sampleCount} samples");
                }
            }

            private void Update()
            {
                if (!enabled || !isRunning || !profilersInitialized) return;

                frameCount++;

                // Sample at specified rate
                if (Time.time - lastSampleTime >= sampleInterval)
                {
                    CollectSample();
                    lastSampleTime = Time.time;
                }
            }

            private void FixedUpdate()
            {
                if (!enabled || !isRunning || !profilersInitialized) return;

                // Use custom sampler to profile physics operations
                physicsSampler.Begin();
                
                // Sample physics-related operations here
                // (This would be where physics calculations happen)
                
                physicsSampler.End();
            }

            private void CollectSample()
            {
                if (sampleCount >= maxSamples)
                {
                    // Remove oldest sample to make room for new one
                    results.RemoveAt(0);
                    sampleCount--;
                }

                var sample = new Dictionary<string, object>
                {
                    ["timestamp"] = Time.time,
                    ["frameCount"] = frameCount,
                    ["deltaTime"] = Time.deltaTime,
                    ["fixedDeltaTime"] = Time.fixedDeltaTime,
                    ["timeScale"] = Time.timeScale
                };

                // Collect timing data
                if (mainThreadRecorder.Valid)
                {
                    sample["mainThreadTime"] = mainThreadRecorder.LastValueAsDouble * 1e-6; // Convert to milliseconds
                }

                if (fixedUpdateRecorder.Valid)
                {
                    sample["fixedUpdateTime"] = fixedUpdateRecorder.LastValueAsDouble * 1e-6; // Convert to milliseconds
                }

                if (physicsUpdateRecorder.Valid)
                {
                    sample["physicsTime"] = physicsUpdateRecorder.LastValueAsDouble * 1e-6; // Convert to milliseconds
                }

                // Collect memory data
                if (systemMemoryRecorder.Valid)
                {
                    sample["systemMemoryMB"] = systemMemoryRecorder.LastValue / (1024 * 1024);
                }

                if (gcMemoryRecorder.Valid)
                {
                    sample["gcMemoryMB"] = gcMemoryRecorder.LastValue / (1024 * 1024);
                }

                if (totalReservedMemoryRecorder.Valid)
                {
                    sample["totalReservedMemoryMB"] = totalReservedMemoryRecorder.LastValue / (1024 * 1024);
                }

                if (totalAllocatedMemoryRecorder.Valid)
                {
                    sample["totalAllocatedMemoryMB"] = totalAllocatedMemoryRecorder.LastValue / (1024 * 1024);
                }

                // Add detailed metrics if enabled
                if (detailedMetrics)
                {
                    sample["fps"] = 1.0f / Time.deltaTime;
                    sample["fixedFps"] = 1.0f / Time.fixedDeltaTime;
                    sample["physicsTimestep"] = Time.fixedDeltaTime;
                    sample["rigidbodyCount"] = FindObjectsByType<Rigidbody>(FindObjectsSortMode.None).Length;
                    sample["colliderCount"] = FindObjectsByType<Collider>(FindObjectsSortMode.None).Length;
                    sample["jointCount"] = FindObjectsByType<Joint>(FindObjectsSortMode.None).Length;
                }

                results.Add(sample);
                sampleCount++;

                if (logToConsole)
                {
                    Debug.Log($"[PhysicsGeneralProfiler] Sample {sampleCount}: Main Thread: {sample.GetValueOrDefault("mainThreadTime", 0.0):F2}ms, " +
                             $"Physics: {sample.GetValueOrDefault("physicsTime", 0.0):F2}ms, " +
                             $"Memory: {sample.GetValueOrDefault("systemMemoryMB", 0):F1}MB");
                }
            }

            public List<Dictionary<string, object>> GetResults()
            {
                return new List<Dictionary<string, object>>(results);
            }

            public void ClearResults()
            {
                results.Clear();
                sampleCount = 0;
                
                if (logToConsole)
                {
                    Debug.Log("[PhysicsGeneralProfiler] Results cleared");
                }
            }

            public Dictionary<string, object> GetCurrentMetrics()
            {
                if (!profilersInitialized)
                {
                    return new Dictionary<string, object>();
                }

                var metrics = new Dictionary<string, object>
                {
                    ["isRunning"] = isRunning,
                    ["sampleCount"] = sampleCount,
                    ["frameCount"] = frameCount
                };

                // Get current values from profilers
                if (mainThreadRecorder.Valid)
                {
                    metrics["currentMainThreadTime"] = mainThreadRecorder.CurrentValueAsDouble * 1e-6;
                }

                if (systemMemoryRecorder.Valid)
                {
                    metrics["currentSystemMemoryMB"] = systemMemoryRecorder.CurrentValue / (1024 * 1024);
                }

                if (gcMemoryRecorder.Valid)
                {
                    metrics["currentGCMemoryMB"] = gcMemoryRecorder.CurrentValue / (1024 * 1024);
                }

                return metrics;
            }

            private void OnDestroy()
            {
                StopProfiling();
                DisposeProfilers();
            }

            private void OnDisable()
            {
                if (isRunning)
                {
                    StopProfiling();
                }
            }
        }

        public class PhysicsRigidbodyProfiler : MonoBehaviour
        {
            [Header("Profiler Settings")]
            public new bool enabled = true;
            public float sampleRate = 60.0f;
            public bool autoStart = false;
            public int maxSamples = 1000;
            public bool logToConsole = false;
            public bool detailedMetrics = false;
            public bool isRunning = false;
            public int sampleCount = 0;

            [Header("Tracking Options")]
            public bool trackVelocities = true;
            public bool trackForces = true;
            public bool trackSleepState = true;
            public bool trackConstraints = true;
            public LayerMask trackingLayers = -1;

            [Header("Runtime Data")]
            [SerializeField] private List<Dictionary<string, object>> results = new List<Dictionary<string, object>>();

            // Unity Profiler API components
            private CustomSampler rigidbodySampler;
            private CustomSampler velocitySampler;
            private CustomSampler forceSampler;
            
            // Rigidbody tracking
            private List<Rigidbody> trackedRigidbodies = new List<Rigidbody>();
            private Dictionary<Rigidbody, RigidbodyMetrics> lastFrameMetrics = new Dictionary<Rigidbody, RigidbodyMetrics>();
            
            // Performance tracking
            private float lastSampleTime = 0f;
            private float sampleInterval = 0f;
            private int frameCount = 0;
            private bool profilersInitialized = false;

            [System.Serializable]
            private class RigidbodyMetrics
            {
                public Vector3 velocity;
                public Vector3 angularVelocity;
                public Vector3 position;
                public bool isKinematic;
                public bool isSleeping;
                public float mass;
                public float drag;
                public float angularDrag;
                public int constraintCount;
            }

            private void Start()
            {
                if (autoStart)
                {
                    StartProfiling();
                }
                
                sampleInterval = 1.0f / sampleRate;
                FindAndRegisterRigidbodies();
            }

            private void InitializeProfilers()
            {
                if (profilersInitialized) return;

                try
                {
                    // Create custom samplers for rigidbody operations
                    rigidbodySampler = CustomSampler.Create("Physics Rigidbody Profiler", false);
                    velocitySampler = CustomSampler.Create("Rigidbody Velocity Analysis", false);
                    forceSampler = CustomSampler.Create("Rigidbody Force Analysis", false);

                    profilersInitialized = true;
                    
                    if (logToConsole)
                    {
                        Debug.Log("[PhysicsRigidbodyProfiler] Profilers initialized successfully");
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[PhysicsRigidbodyProfiler] Failed to initialize profilers: {e.Message}");
                    profilersInitialized = false;
                }
            }

            private void FindAndRegisterRigidbodies()
            {
                trackedRigidbodies.Clear();
                var allRigidbodies = FindObjectsByType<Rigidbody>(FindObjectsSortMode.None);
                
                foreach (var rb in allRigidbodies)
                {
                    // Check if rigidbody is on a layer we want to track
                    if (((1 << rb.gameObject.layer) & trackingLayers) != 0)
                    {
                        trackedRigidbodies.Add(rb);
                        
                        // Initialize metrics for this rigidbody
                        if (!lastFrameMetrics.ContainsKey(rb))
                        {
                            lastFrameMetrics[rb] = new RigidbodyMetrics();
                        }
                    }
                }
                
                if (logToConsole)
                {
                    Debug.Log($"[PhysicsRigidbodyProfiler] Tracking {trackedRigidbodies.Count} rigidbodies");
                }
            }

            public void StartProfiling()
            {
                if (isRunning) return;

                InitializeProfilers();
                FindAndRegisterRigidbodies();
                isRunning = true;
                frameCount = 0;
                lastSampleTime = Time.time;
                
                if (logToConsole)
                {
                    Debug.Log("[PhysicsRigidbodyProfiler] Started profiling");
                }
            }

            public void StopProfiling()
            {
                if (!isRunning) return;

                isRunning = false;
                
                if (logToConsole)
                {
                    Debug.Log($"[PhysicsRigidbodyProfiler] Stopped profiling. Collected {sampleCount} samples");
                }
            }

            private void Update()
            {
                if (!enabled || !isRunning || !profilersInitialized) return;

                frameCount++;

                // Sample at specified rate
                if (Time.time - lastSampleTime >= sampleInterval)
                {
                    CollectSample();
                    lastSampleTime = Time.time;
                }
            }

            private void FixedUpdate()
            {
                if (!enabled || !isRunning || !profilersInitialized) return;

                rigidbodySampler.Begin();
                
                // Profile rigidbody analysis
                AnalyzeRigidbodies();
                
                rigidbodySampler.End();
            }

            private void AnalyzeRigidbodies()
            {
                if (trackVelocities)
                {
                    velocitySampler.Begin();
                    AnalyzeVelocities();
                    velocitySampler.End();
                }

                if (trackForces)
                {
                    forceSampler.Begin();
                    AnalyzeForces();
                    forceSampler.End();
                }
            }

            private void AnalyzeVelocities()
            {
                foreach (var rb in trackedRigidbodies)
                {
                    if (rb == null) continue;

                    var metrics = lastFrameMetrics[rb];
                    metrics.velocity = rb.linearVelocity;
                    metrics.angularVelocity = rb.angularVelocity;
                    metrics.position = rb.position;
                }
            }

            private void AnalyzeForces()
            {
                foreach (var rb in trackedRigidbodies)
                {
                    if (rb == null) continue;

                    var metrics = lastFrameMetrics[rb];
                    metrics.isKinematic = rb.isKinematic;
                    metrics.isSleeping = rb.IsSleeping();
                    metrics.mass = rb.mass;
                    metrics.drag = rb.linearDamping;
                    metrics.angularDrag = rb.angularDamping;
                    
                    // Count constraints
                    if (trackConstraints)
                    {
                        metrics.constraintCount = rb.GetComponents<Joint>().Length;
                    }
                }
            }

            private void CollectSample()
            {
                if (sampleCount >= maxSamples)
                {
                    // Remove oldest sample to make room for new one
                    results.RemoveAt(0);
                    sampleCount--;
                }

                var sample = new Dictionary<string, object>
                {
                    ["timestamp"] = Time.time,
                    ["frameCount"] = frameCount,
                    ["deltaTime"] = Time.deltaTime,
                    ["fixedDeltaTime"] = Time.fixedDeltaTime
                };

                // Collect rigidbody statistics
                int activeRigidbodies = 0;
                int sleepingRigidbodies = 0;
                int kinematicRigidbodies = 0;
                float totalMass = 0f;
                float avgVelocityMagnitude = 0f;
                float avgAngularVelocityMagnitude = 0f;
                float maxVelocityMagnitude = 0f;
                float maxAngularVelocityMagnitude = 0f;
                int totalConstraints = 0;

                var validRigidbodies = trackedRigidbodies.Where(rb => rb != null).ToList();
                
                foreach (var rb in validRigidbodies)
                {
                    if (rb.isKinematic)
                    {
                        kinematicRigidbodies++;
                    }
                    else if (rb.IsSleeping())
                    {
                        sleepingRigidbodies++;
                    }
                    else
                    {
                        activeRigidbodies++;
                    }

                    totalMass += rb.mass;
                    
                    if (trackVelocities && lastFrameMetrics.ContainsKey(rb))
                    {
                        var metrics = lastFrameMetrics[rb];
                        float velocityMag = metrics.velocity.magnitude;
                        float angularVelocityMag = metrics.angularVelocity.magnitude;
                        
                        avgVelocityMagnitude += velocityMag;
                        avgAngularVelocityMagnitude += angularVelocityMag;
                        
                        if (velocityMag > maxVelocityMagnitude)
                            maxVelocityMagnitude = velocityMag;
                        
                        if (angularVelocityMag > maxAngularVelocityMagnitude)
                            maxAngularVelocityMagnitude = angularVelocityMag;
                    }

                    if (trackConstraints && lastFrameMetrics.ContainsKey(rb))
                    {
                        totalConstraints += lastFrameMetrics[rb].constraintCount;
                    }
                }

                int totalTracked = validRigidbodies.Count;
                if (totalTracked > 0)
                {
                    avgVelocityMagnitude /= totalTracked;
                    avgAngularVelocityMagnitude /= totalTracked;
                }

                sample["totalRigidbodies"] = totalTracked;
                sample["activeRigidbodies"] = activeRigidbodies;
                sample["sleepingRigidbodies"] = sleepingRigidbodies;
                sample["kinematicRigidbodies"] = kinematicRigidbodies;
                sample["totalMass"] = totalMass;
                sample["avgVelocityMagnitude"] = avgVelocityMagnitude;
                sample["avgAngularVelocityMagnitude"] = avgAngularVelocityMagnitude;
                sample["maxVelocityMagnitude"] = maxVelocityMagnitude;
                sample["maxAngularVelocityMagnitude"] = maxAngularVelocityMagnitude;
                sample["totalConstraints"] = totalConstraints;

                // Add detailed metrics if enabled
                if (detailedMetrics)
                {
                    var detailedRigidbodyData = new List<Dictionary<string, object>>();
                    
                    foreach (var rb in validRigidbodies.Take(10)) // Limit to first 10 for performance
                    {
                        if (lastFrameMetrics.ContainsKey(rb))
                        {
                            var metrics = lastFrameMetrics[rb];
                            var rbData = new Dictionary<string, object>
                            {
                                ["name"] = rb.name,
                                ["position"] = new float[] { metrics.position.x, metrics.position.y, metrics.position.z },
                                ["velocity"] = new float[] { metrics.velocity.x, metrics.velocity.y, metrics.velocity.z },
                                ["angularVelocity"] = new float[] { metrics.angularVelocity.x, metrics.angularVelocity.y, metrics.angularVelocity.z },
                                ["mass"] = metrics.mass,
                                ["isKinematic"] = metrics.isKinematic,
                                ["isSleeping"] = metrics.isSleeping,
                                ["drag"] = metrics.drag,
                                ["angularDrag"] = metrics.angularDrag,
                                ["constraintCount"] = metrics.constraintCount
                            };
                            detailedRigidbodyData.Add(rbData);
                        }
                    }
                    
                    sample["detailedRigidbodyData"] = detailedRigidbodyData;
                }

                results.Add(sample);
                sampleCount++;

                if (logToConsole)
                {
                    Debug.Log($"[PhysicsRigidbodyProfiler] Sample {sampleCount}: Active: {activeRigidbodies}, " +
                             $"Sleeping: {sleepingRigidbodies}, Avg Velocity: {avgVelocityMagnitude:F2}, " +
                             $"Max Velocity: {maxVelocityMagnitude:F2}");
                }
            }

            public List<Dictionary<string, object>> GetResults()
            {
                return new List<Dictionary<string, object>>(results);
            }

            public void ClearResults()
            {
                results.Clear();
                sampleCount = 0;
                
                if (logToConsole)
                {
                    Debug.Log("[PhysicsRigidbodyProfiler] Results cleared");
                }
            }

            public Dictionary<string, object> GetCurrentMetrics()
            {
                var metrics = new Dictionary<string, object>
                {
                    ["isRunning"] = isRunning,
                    ["sampleCount"] = sampleCount,
                    ["frameCount"] = frameCount,
                    ["trackedRigidbodies"] = trackedRigidbodies.Count(rb => rb != null)
                };

                // Get current rigidbody statistics
                var validRigidbodies = trackedRigidbodies.Where(rb => rb != null).ToList();
                if (validRigidbodies.Count > 0)
                {
                    metrics["currentActiveRigidbodies"] = validRigidbodies.Count(rb => !rb.isKinematic && !rb.IsSleeping());
                    metrics["currentSleepingRigidbodies"] = validRigidbodies.Count(rb => !rb.isKinematic && rb.IsSleeping());
                    metrics["currentKinematicRigidbodies"] = validRigidbodies.Count(rb => rb.isKinematic);
                }

                return metrics;
            }

            // Public method to add/remove rigidbodies for tracking
            public void AddRigidbodyToTrack(Rigidbody rb)
            {
                if (rb != null && !trackedRigidbodies.Contains(rb))
                {
                    trackedRigidbodies.Add(rb);
                    lastFrameMetrics[rb] = new RigidbodyMetrics();
                    
                    if (logToConsole)
                    {
                        Debug.Log($"[PhysicsRigidbodyProfiler] Added rigidbody '{rb.name}' to tracking");
                    }
                }
            }

            public void RemoveRigidbodyFromTrack(Rigidbody rb)
            {
                if (rb != null && trackedRigidbodies.Contains(rb))
                {
                    trackedRigidbodies.Remove(rb);
                    lastFrameMetrics.Remove(rb);
                    
                    if (logToConsole)
                    {
                        Debug.Log($"[PhysicsRigidbodyProfiler] Removed rigidbody '{rb.name}' from tracking");
                    }
                }
            }

            public void RefreshTrackedRigidbodies()
            {
                FindAndRegisterRigidbodies();
            }

            private void OnDestroy()
            {
                StopProfiling();
            }

            private void OnDisable()
            {
                if (isRunning)
                {
                    StopProfiling();
                }
            }
        }

        public class PhysicsCollisionProfiler : MonoBehaviour
        {
            [Header("Profiler Settings")]
            public new bool enabled = true;
            public float sampleRate = 60.0f;
            public bool autoStart = false;
            public int maxSamples = 1000;
            public bool logToConsole = false;
            public bool detailedMetrics = false;
            public bool isRunning = false;
            public int sampleCount = 0;

            [Header("Collision Tracking")]
            public bool trackCollisions = true;
            public bool trackTriggers = true;
            public bool trackContactPoints = true;
            public LayerMask trackingLayers = -1;
            public float minImpulseThreshold = 0.1f;

            [Header("Runtime Data")]
            [SerializeField] private List<Dictionary<string, object>> results = new List<Dictionary<string, object>>();

            // Unity Profiler API components
            private CustomSampler collisionSampler;
            private CustomSampler triggerSampler;
            private CustomSampler contactSampler;
            
            // Collision tracking
            private List<CollisionData> collisionsThisFrame = new List<CollisionData>();
            private List<TriggerData> triggersThisFrame = new List<TriggerData>();
            private List<ContactPoint> contactPointsThisFrame = new List<ContactPoint>();
            
            // Performance tracking
            private float lastSampleTime = 0f;
            private float sampleInterval = 0f;
            private int frameCount = 0;
            private bool profilersInitialized = false;

            // Collision event listeners
            private Dictionary<Collider, CollisionEventListener> collisionListeners = new Dictionary<Collider, CollisionEventListener>();
            private Dictionary<Collider, TriggerEventListener> triggerListeners = new Dictionary<Collider, TriggerEventListener>();

            [System.Serializable]
            private class CollisionData
            {
                public float timestamp;
                public string eventType; // Enter, Stay, Exit
                public string objectA;
                public string objectB;
                public Vector3 contactPoint;
                public Vector3 normal;
                public float impulse;
                public float relativeSpeed;
                public int contactCount;
            }

            [System.Serializable]
            private class TriggerData
            {
                public float timestamp;
                public string eventType; // Enter, Stay, Exit
                public string triggerObject;
                public string otherObject;
                public Vector3 triggerCenter;
                public Vector3 otherPosition;
                public Bounds triggerBounds;
            }

            // Custom collision listener to capture collision events
            private class CollisionEventListener : MonoBehaviour
            {
                public PhysicsCollisionProfiler profiler;

                void OnCollisionEnter(Collision collision)
                {
                    if (profiler != null && profiler.trackCollisions)
                        profiler.RecordCollision("Enter", collision);
                }

                void OnCollisionStay(Collision collision)
                {
                    if (profiler != null && profiler.trackCollisions)
                        profiler.RecordCollision("Stay", collision);
                }

                void OnCollisionExit(Collision collision)
                {
                    if (profiler != null && profiler.trackCollisions)
                        profiler.RecordCollision("Exit", collision);
                }
            }

            // Custom trigger listener to capture trigger events
            private class TriggerEventListener : MonoBehaviour
            {
                public PhysicsCollisionProfiler profiler;

                void OnTriggerEnter(Collider other)
                {
                    if (profiler != null && profiler.trackTriggers)
                        profiler.RecordTrigger("Enter", GetComponent<Collider>(), other);
                }

                void OnTriggerStay(Collider other)
                {
                    if (profiler != null && profiler.trackTriggers)
                        profiler.RecordTrigger("Stay", GetComponent<Collider>(), other);
                }

                void OnTriggerExit(Collider other)
                {
                    if (profiler != null && profiler.trackTriggers)
                        profiler.RecordTrigger("Exit", GetComponent<Collider>(), other);
                }
            }

            private void Start()
            {
                if (autoStart)
                {
                    StartProfiling();
                }
                
                sampleInterval = 1.0f / sampleRate;
            }

            private void InitializeProfilers()
            {
                if (profilersInitialized) return;

                try
                {
                    // Create custom samplers for collision operations
                    collisionSampler = CustomSampler.Create("Physics Collision Profiler", false);
                    triggerSampler = CustomSampler.Create("Physics Trigger Analysis", false);
                    contactSampler = CustomSampler.Create("Physics Contact Analysis", false);

                    profilersInitialized = true;
                    
                    if (logToConsole)
                    {
                        Debug.Log("[PhysicsCollisionProfiler] Profilers initialized successfully");
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[PhysicsCollisionProfiler] Failed to initialize profilers: {e.Message}");
                    profilersInitialized = false;
                }
            }

            private void SetupCollisionListeners()
            {
                var colliders = FindObjectsByType<Collider>(FindObjectsSortMode.None);
                
                foreach (var collider in colliders)
                {
                    // Check if collider is on a layer we want to track
                    if (((1 << collider.gameObject.layer) & trackingLayers) != 0)
                    {
                        // Add collision listener for non-trigger colliders
                        if (!collider.isTrigger && trackCollisions)
                        {
                            var listener = collider.gameObject.GetComponent<CollisionEventListener>();
                            if (listener == null)
                            {
                                listener = collider.gameObject.AddComponent<CollisionEventListener>();
                                listener.profiler = this;
                                collisionListeners[collider] = listener;
                            }
                        }

                        // Add trigger listener for trigger colliders
                        if (collider.isTrigger && trackTriggers)
                        {
                            var listener = collider.gameObject.GetComponent<TriggerEventListener>();
                            if (listener == null)
                            {
                                listener = collider.gameObject.AddComponent<TriggerEventListener>();
                                listener.profiler = this;
                                triggerListeners[collider] = listener;
                            }
                        }
                    }
                }
                
                if (logToConsole)
                {
                    Debug.Log($"[PhysicsCollisionProfiler] Set up {collisionListeners.Count} collision listeners and {triggerListeners.Count} trigger listeners");
                }
            }

            private void CleanupCollisionListeners()
            {
                // Remove all collision listeners
                foreach (var listener in collisionListeners.Values)
                {
                    if (listener != null)
                        DestroyImmediate(listener);
                }
                collisionListeners.Clear();

                // Remove all trigger listeners
                foreach (var listener in triggerListeners.Values)
                {
                    if (listener != null)
                        DestroyImmediate(listener);
                }
                triggerListeners.Clear();
            }

            public void StartProfiling()
            {
                if (isRunning) return;

                InitializeProfilers();
                SetupCollisionListeners();
                isRunning = true;
                frameCount = 0;
                lastSampleTime = Time.time;
                
                if (logToConsole)
                {
                    Debug.Log("[PhysicsCollisionProfiler] Started profiling");
                }
            }

            public void StopProfiling()
            {
                if (!isRunning) return;

                isRunning = false;
                CleanupCollisionListeners();
                
                if (logToConsole)
                {
                    Debug.Log($"[PhysicsCollisionProfiler] Stopped profiling. Collected {sampleCount} samples");
                }
            }

            private void Update()
            {
                if (!enabled || !isRunning || !profilersInitialized) return;

                frameCount++;

                // Sample at specified rate
                if (Time.time - lastSampleTime >= sampleInterval)
                {
                    CollectSample();
                    lastSampleTime = Time.time;
                }
            }

            private void LateUpdate()
            {
                if (!enabled || !isRunning || !profilersInitialized) return;

                // Clear frame data at end of frame
                collisionsThisFrame.Clear();
                triggersThisFrame.Clear();
                contactPointsThisFrame.Clear();
            }

            // Called by collision listeners
            public void RecordCollision(string eventType, Collision collision)
            {
                collisionSampler.Begin();

                var collisionData = new CollisionData
                {
                    timestamp = Time.time,
                    eventType = eventType,
                    objectA = collision.gameObject.name,
                    objectB = collision.collider.name,
                    contactCount = collision.contactCount,
                    relativeSpeed = collision.relativeVelocity.magnitude
                };

                if (collision.contactCount > 0)
                {
                    var contact = collision.contacts[0];
                    collisionData.contactPoint = contact.point;
                    collisionData.normal = contact.normal;
                    
                    // Calculate impulse magnitude (approximation based on relative velocity and masses)
                    var rb1 = collision.rigidbody;
                    var rb2 = collision.collider.attachedRigidbody;
                    
                    if (rb1 != null && rb2 != null)
                    {
                        float combinedMass = rb1.mass + rb2.mass;
                        collisionData.impulse = collision.relativeVelocity.magnitude * combinedMass;
                    }
                    else
                    {
                        collisionData.impulse = collision.relativeVelocity.magnitude;
                    }

                    // Track contact points if enabled
                    if (trackContactPoints && collisionData.impulse >= minImpulseThreshold)
                    {
                        for (int i = 0; i < collision.contactCount; i++)
                        {
                            contactPointsThisFrame.Add(collision.contacts[i]);
                        }
                    }
                }

                collisionsThisFrame.Add(collisionData);
                collisionSampler.End();
            }

            // Called by trigger listeners
            public void RecordTrigger(string eventType, Collider trigger, Collider other)
            {
                triggerSampler.Begin();

                var triggerData = new TriggerData
                {
                    timestamp = Time.time,
                    eventType = eventType,
                    triggerObject = trigger.name,
                    otherObject = other.name,
                    triggerCenter = trigger.bounds.center,
                    otherPosition = other.transform.position,
                    triggerBounds = trigger.bounds
                };

                triggersThisFrame.Add(triggerData);
                triggerSampler.End();
            }

            private void CollectSample()
            {
                if (sampleCount >= maxSamples)
                {
                    // Remove oldest sample to make room for new one
                    results.RemoveAt(0);
                    sampleCount--;
                }

                contactSampler.Begin();

                var sample = new Dictionary<string, object>
                {
                    ["timestamp"] = Time.time,
                    ["frameCount"] = frameCount,
                    ["deltaTime"] = Time.deltaTime,
                    ["fixedDeltaTime"] = Time.fixedDeltaTime
                };

                // Collision statistics
                var collisionEnters = collisionsThisFrame.Count(c => c.eventType == "Enter");
                var collisionStays = collisionsThisFrame.Count(c => c.eventType == "Stay");
                var collisionExits = collisionsThisFrame.Count(c => c.eventType == "Exit");

                sample["collisionEnters"] = collisionEnters;
                sample["collisionStays"] = collisionStays;
                sample["collisionExits"] = collisionExits;
                sample["totalCollisions"] = collisionsThisFrame.Count;

                // Trigger statistics
                var triggerEnters = triggersThisFrame.Count(t => t.eventType == "Enter");
                var triggerStays = triggersThisFrame.Count(t => t.eventType == "Stay");
                var triggerExits = triggersThisFrame.Count(t => t.eventType == "Exit");

                sample["triggerEnters"] = triggerEnters;
                sample["triggerStays"] = triggerStays;
                sample["triggerExits"] = triggerExits;
                sample["totalTriggers"] = triggersThisFrame.Count;

                // Contact point statistics
                sample["contactPointCount"] = contactPointsThisFrame.Count;

                // Impulse statistics
                if (collisionsThisFrame.Count > 0)
                {
                    var impulses = collisionsThisFrame.Where(c => c.impulse > 0).Select(c => c.impulse).ToList();
                    if (impulses.Count > 0)
                    {
                        sample["avgImpulse"] = impulses.Average();
                        sample["maxImpulse"] = impulses.Max();
                        sample["minImpulse"] = impulses.Min();
                        sample["highImpulseCollisions"] = impulses.Count(i => i >= minImpulseThreshold * 10);
                    }

                    var speeds = collisionsThisFrame.Select(c => c.relativeSpeed).ToList();
                    if (speeds.Count > 0)
                    {
                        sample["avgRelativeSpeed"] = speeds.Average();
                        sample["maxRelativeSpeed"] = speeds.Max();
                    }
                }

                // Add detailed metrics if enabled
                if (detailedMetrics && (collisionsThisFrame.Count > 0 || triggersThisFrame.Count > 0))
                {
                    var detailedCollisions = collisionsThisFrame.Take(10).Select(c => new Dictionary<string, object>
                    {
                        ["eventType"] = c.eventType,
                        ["objectA"] = c.objectA,
                        ["objectB"] = c.objectB,
                        ["contactPoint"] = new float[] { c.contactPoint.x, c.contactPoint.y, c.contactPoint.z },
                        ["normal"] = new float[] { c.normal.x, c.normal.y, c.normal.z },
                        ["impulse"] = c.impulse,
                        ["relativeSpeed"] = c.relativeSpeed,
                        ["contactCount"] = c.contactCount
                    }).ToList();

                    var detailedTriggers = triggersThisFrame.Take(10).Select(t => new Dictionary<string, object>
                    {
                        ["eventType"] = t.eventType,
                        ["triggerObject"] = t.triggerObject,
                        ["otherObject"] = t.otherObject,
                        ["triggerCenter"] = new float[] { t.triggerCenter.x, t.triggerCenter.y, t.triggerCenter.z },
                        ["otherPosition"] = new float[] { t.otherPosition.x, t.otherPosition.y, t.otherPosition.z }
                    }).ToList();

                    sample["detailedCollisions"] = detailedCollisions;
                    sample["detailedTriggers"] = detailedTriggers;
                }

                contactSampler.End();

                results.Add(sample);
                sampleCount++;

                if (logToConsole)
                {
                    Debug.Log($"[PhysicsCollisionProfiler] Sample {sampleCount}: Collisions: {collisionsThisFrame.Count}, " +
                             $"Triggers: {triggersThisFrame.Count}, Contacts: {contactPointsThisFrame.Count}");
                }
            }

            public List<Dictionary<string, object>> GetResults()
            {
                return new List<Dictionary<string, object>>(results);
            }

            public void ClearResults()
            {
                results.Clear();
                sampleCount = 0;
                
                if (logToConsole)
                {
                    Debug.Log("[PhysicsCollisionProfiler] Results cleared");
                }
            }

            public Dictionary<string, object> GetCurrentMetrics()
            {
                var metrics = new Dictionary<string, object>
                {
                    ["isRunning"] = isRunning,
                    ["sampleCount"] = sampleCount,
                    ["frameCount"] = frameCount,
                    ["collisionListeners"] = collisionListeners.Count,
                    ["triggerListeners"] = triggerListeners.Count
                };

                // Current frame statistics
                if (collisionsThisFrame.Count > 0 || triggersThisFrame.Count > 0)
                {
                    metrics["currentCollisions"] = collisionsThisFrame.Count;
                    metrics["currentTriggers"] = triggersThisFrame.Count;
                    metrics["currentContactPoints"] = contactPointsThisFrame.Count;
                }

                return metrics;
            }

            public void RefreshCollisionListeners()
            {
                CleanupCollisionListeners();
                SetupCollisionListeners();
            }

            private void OnDestroy()
            {
                StopProfiling();
            }

            private void OnDisable()
            {
                if (isRunning)
                {
                    StopProfiling();
                }
            }
        }

        public class PhysicsJointProfiler : MonoBehaviour
        {
            [Header("Profiler Settings")]
            public new bool enabled = true;
            public float sampleRate = 60.0f;
            public bool autoStart = false;
            public int maxSamples = 1000;
            public bool logToConsole = false;
            public bool detailedMetrics = false;
            public bool isRunning = false;
            public int sampleCount = 0;

            [Header("Joint Tracking")]
            public bool trackBreakEvents = true;
            public bool trackForces = true;
            public bool trackLimits = true;
            public LayerMask trackingLayers = -1;
            public float forceThreshold = 10.0f;

            [Header("Runtime Data")]
            [SerializeField] private List<Dictionary<string, object>> results = new List<Dictionary<string, object>>();

            // Unity Profiler API components
            private CustomSampler jointSampler;
            private CustomSampler breakEventSampler;
            private CustomSampler forceAnalysisSampler;
            
            // Joint tracking
            private List<Joint> trackedJoints = new List<Joint>();
            private Dictionary<Joint, JointMetrics> lastFrameMetrics = new Dictionary<Joint, JointMetrics>();
            private List<JointBreakEvent> breakEventsThisFrame = new List<JointBreakEvent>();
            
            // Performance tracking
            private float lastSampleTime = 0f;
            private float sampleInterval = 0f;
            private int frameCount = 0;
            private bool profilersInitialized = false;

            // Joint event listeners
            private Dictionary<Joint, JointBreakListener> breakListeners = new Dictionary<Joint, JointBreakListener>();

            [System.Serializable]
            private class JointMetrics
            {
                public Vector3 anchor;
                public Vector3 connectedAnchor;
                public Vector3 currentForce;
                public Vector3 currentTorque;
                public float breakForce;
                public float breakTorque;
                public bool enableCollision;
                public bool enablePreprocessing;
                public string jointType;
                public bool isConnected;
                public float stress; // Calculated stress level
            }

            [System.Serializable]
            private class JointBreakEvent
            {
                public float timestamp;
                public string jointName;
                public string jointType;
                public float breakForce;
                public Vector3 position;
                public string connectedObjectName;
            }

            // Custom joint break listener
            private class JointBreakListener : MonoBehaviour
            {
                public PhysicsJointProfiler profiler;
                public Joint joint;

                void OnJointBreak(float breakForce)
                {
                    if (profiler != null && profiler.trackBreakEvents)
                        profiler.RecordJointBreak(joint, breakForce);
                }
            }

            private void Start()
            {
                if (autoStart)
                {
                    StartProfiling();
                }
                
                sampleInterval = 1.0f / sampleRate;
            }

            private void InitializeProfilers()
            {
                if (profilersInitialized) return;

                try
                {
                    // Create custom samplers for joint operations
                    jointSampler = CustomSampler.Create("Physics Joint Profiler", false);
                    breakEventSampler = CustomSampler.Create("Joint Break Events", false);
                    forceAnalysisSampler = CustomSampler.Create("Joint Force Analysis", false);

                    profilersInitialized = true;
                    
                    if (logToConsole)
                    {
                        Debug.Log("[PhysicsJointProfiler] Profilers initialized successfully");
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[PhysicsJointProfiler] Failed to initialize profilers: {e.Message}");
                    profilersInitialized = false;
                }
            }

            private void FindAndRegisterJoints()
            {
                trackedJoints.Clear();
                var allJoints = FindObjectsByType<Joint>(FindObjectsSortMode.None);
                
                foreach (var joint in allJoints)
                {
                    // Check if joint is on a layer we want to track
                    if (((1 << joint.gameObject.layer) & trackingLayers) != 0)
                    {
                        trackedJoints.Add(joint);
                        
                        // Initialize metrics for this joint
                        if (!lastFrameMetrics.ContainsKey(joint))
                        {
                            lastFrameMetrics[joint] = new JointMetrics();
                        }

                        // Set up break event listener
                        if (trackBreakEvents)
                        {
                            var listener = joint.gameObject.GetComponent<JointBreakListener>();
                            if (listener == null)
                            {
                                listener = joint.gameObject.AddComponent<JointBreakListener>();
                                listener.profiler = this;
                                listener.joint = joint;
                                breakListeners[joint] = listener;
                            }
                        }
                    }
                }
                
                if (logToConsole)
                {
                    Debug.Log($"[PhysicsJointProfiler] Tracking {trackedJoints.Count} joints with {breakListeners.Count} break listeners");
                }
            }

            private void CleanupJointListeners()
            {
                // Remove all break listeners
                foreach (var listener in breakListeners.Values)
                {
                    if (listener != null)
                        DestroyImmediate(listener);
                }
                breakListeners.Clear();
            }

            public void StartProfiling()
            {
                if (isRunning) return;

                InitializeProfilers();
                FindAndRegisterJoints();
                isRunning = true;
                frameCount = 0;
                lastSampleTime = Time.time;
                
                if (logToConsole)
                {
                    Debug.Log("[PhysicsJointProfiler] Started profiling");
                }
            }

            public void StopProfiling()
            {
                if (!isRunning) return;

                isRunning = false;
                CleanupJointListeners();
                
                if (logToConsole)
                {
                    Debug.Log($"[PhysicsJointProfiler] Stopped profiling. Collected {sampleCount} samples");
                }
            }

            private void Update()
            {
                if (!enabled || !isRunning || !profilersInitialized) return;

                frameCount++;

                // Sample at specified rate
                if (Time.time - lastSampleTime >= sampleInterval)
                {
                    CollectSample();
                    lastSampleTime = Time.time;
                }
            }

            private void FixedUpdate()
            {
                if (!enabled || !isRunning || !profilersInitialized) return;

                jointSampler.Begin();
                
                // Profile joint analysis
                AnalyzeJoints();
                
                jointSampler.End();
            }

            private void LateUpdate()
            {
                if (!enabled || !isRunning || !profilersInitialized) return;

                // Clear frame data at end of frame
                breakEventsThisFrame.Clear();
            }

            private void AnalyzeJoints()
            {
                if (trackForces)
                {
                    forceAnalysisSampler.Begin();
                    AnalyzeJointForces();
                    forceAnalysisSampler.End();
                }
            }

            private void AnalyzeJointForces()
            {
                forceAnalysisSampler.Begin();

                foreach (var joint in trackedJoints)
                {
                    if (joint == null) continue;

                    var metrics = lastFrameMetrics.ContainsKey(joint) ? lastFrameMetrics[joint] : new JointMetrics();
                    
                    // Update basic joint properties
                    metrics.anchor = joint.anchor;
                    metrics.connectedAnchor = joint.connectedAnchor;
                    metrics.breakForce = joint.breakForce;
                    metrics.breakTorque = joint.breakTorque;
                    metrics.enableCollision = joint.enableCollision;
                    metrics.enablePreprocessing = joint.enablePreprocessing;
                    metrics.jointType = joint.GetType().Name;
                    metrics.isConnected = joint.connectedBody != null;

                    // Calculate approximate joint stress
                    // This is a simplified calculation based on the relationship between applied forces and break limits
                    if (joint.connectedBody != null)
                    {
                        var rb = joint.GetComponent<Rigidbody>();
                        var relativeVelocity = joint.connectedBody.linearVelocity - (rb != null ? rb.linearVelocity : Vector3.zero);
                        var velocityMagnitude = relativeVelocity.magnitude;
                        
                        // Estimate current forces (simplified)
                        if (joint.breakForce != Mathf.Infinity)
                        {
                            metrics.stress = Mathf.Clamp01(velocityMagnitude / (joint.breakForce * 0.1f));
                        }
                        else
                        {
                            metrics.stress = 0f;
                        }
                    }

                    // Analyze specific joint types
                    AnalyzeSpecificJointType(joint, metrics);

                    lastFrameMetrics[joint] = metrics;
                }

                forceAnalysisSampler.End();
            }

            private void AnalyzeSpecificJointType(Joint joint, JointMetrics metrics)
            {
                switch (joint)
                {
                    case HingeJoint hinge:
                        // Additional hinge-specific analysis could go here
                        break;
                    case SpringJoint spring:
                        // Additional spring-specific analysis could go here
                        break;
                    case ConfigurableJoint configurable:
                        // Additional configurable joint analysis could go here
                        break;
                    case FixedJoint fixedJoint:
                        // Additional fixed joint analysis could go here
                        break;
                    case CharacterJoint character:
                        // Additional character joint analysis could go here
                        break;
                }
            }

            // Called by joint break listeners
            public void RecordJointBreak(Joint joint, float breakForce)
            {
                breakEventSampler.Begin();

                var breakEvent = new JointBreakEvent
                {
                    timestamp = Time.time,
                    jointName = joint.name,
                    jointType = joint.GetType().Name,
                    breakForce = breakForce,
                    position = joint.transform.position,
                    connectedObjectName = joint.connectedBody?.name ?? "None"
                };

                breakEventsThisFrame.Add(breakEvent);
                breakEventSampler.End();

                if (logToConsole)
                {
                    Debug.Log($"[PhysicsJointProfiler] Joint break recorded: {breakEvent.jointName} ({breakEvent.jointType}) at force {breakForce:F2}");
                }
            }

            private void CollectSample()
            {
                if (sampleCount >= maxSamples)
                {
                    // Remove oldest sample to make room for new one
                    results.RemoveAt(0);
                    sampleCount--;
                }

                var sample = new Dictionary<string, object>
                {
                    ["timestamp"] = Time.time,
                    ["frameCount"] = frameCount,
                    ["deltaTime"] = Time.deltaTime,
                    ["fixedDeltaTime"] = Time.fixedDeltaTime
                };

                var validJoints = trackedJoints.Where(j => j != null).ToList();

                // Joint statistics
                sample["totalJoints"] = validJoints.Count;
                sample["connectedJoints"] = validJoints.Count(j => j.connectedBody != null);
                sample["disconnectedJoints"] = validJoints.Count(j => j.connectedBody == null);
                sample["jointBreaksThisFrame"] = breakEventsThisFrame.Count;

                // Joint type statistics
                var jointTypes = validJoints.GroupBy(j => j.GetType().Name)
                                          .ToDictionary(g => g.Key, g => g.Count());
                sample["jointTypeCount"] = jointTypes;

                // Stress analysis
                if (trackForces && validJoints.Count > 0)
                {
                    var stressLevels = validJoints.Where(j => lastFrameMetrics.ContainsKey(j))
                                                 .Select(j => lastFrameMetrics[j].stress)
                                                 .Where(s => s > 0)
                                                 .ToList();

                    if (stressLevels.Count > 0)
                    {
                        sample["avgStressLevel"] = stressLevels.Average();
                        sample["maxStressLevel"] = stressLevels.Max();
                        sample["highStressJoints"] = stressLevels.Count(s => s > 0.7f);
                        sample["criticalStressJoints"] = stressLevels.Count(s => s > 0.9f);
                    }
                }

                // Break force analysis
                if (validJoints.Count > 0)
                {
                    var finiteBreakForces = validJoints.Where(j => j.breakForce != Mathf.Infinity)
                                                      .Select(j => j.breakForce)
                                                      .ToList();
                    
                    if (finiteBreakForces.Count > 0)
                    {
                        sample["avgBreakForce"] = finiteBreakForces.Average();
                        sample["minBreakForce"] = finiteBreakForces.Min();
                        sample["maxBreakForce"] = finiteBreakForces.Max();
                    }

                    sample["unbreakableJoints"] = validJoints.Count(j => j.breakForce == Mathf.Infinity);
                }

                // Add detailed metrics if enabled
                if (detailedMetrics)
                {
                    var detailedJointData = validJoints.Take(10).Select(j =>
                    {
                        var data = new Dictionary<string, object>
                        {
                            ["name"] = j.name,
                            ["type"] = j.GetType().Name,
                            ["breakForce"] = j.breakForce == Mathf.Infinity ? -1 : j.breakForce,
                            ["breakTorque"] = j.breakTorque == Mathf.Infinity ? -1 : j.breakTorque,
                            ["connectedBody"] = j.connectedBody?.name ?? "None",
                            ["enableCollision"] = j.enableCollision,
                            ["anchor"] = new float[] { j.anchor.x, j.anchor.y, j.anchor.z },
                            ["connectedAnchor"] = new float[] { j.connectedAnchor.x, j.connectedAnchor.y, j.connectedAnchor.z }
                        };

                        if (lastFrameMetrics.ContainsKey(j))
                        {
                            data["stress"] = lastFrameMetrics[j].stress;
                        }

                        return data;
                    }).ToList();

                    sample["detailedJointData"] = detailedJointData;

                    // Detailed break events
                    if (breakEventsThisFrame.Count > 0)
                    {
                        var detailedBreakEvents = breakEventsThisFrame.Select(e => new Dictionary<string, object>
                        {
                            ["jointName"] = e.jointName,
                            ["jointType"] = e.jointType,
                            ["breakForce"] = e.breakForce,
                            ["position"] = new float[] { e.position.x, e.position.y, e.position.z },
                            ["connectedObject"] = e.connectedObjectName
                        }).ToList();

                        sample["detailedBreakEvents"] = detailedBreakEvents;
                    }
                }

                results.Add(sample);
                sampleCount++;

                if (logToConsole)
                {
                    Debug.Log($"[PhysicsJointProfiler] Sample {sampleCount}: Joints: {validJoints.Count}, " +
                             $"Breaks: {breakEventsThisFrame.Count}, " +
                             $"High Stress: {sample.GetValueOrDefault("highStressJoints", 0)}");
                }
            }

            public List<Dictionary<string, object>> GetResults()
            {
                return new List<Dictionary<string, object>>(results);
            }

            public void ClearResults()
            {
                results.Clear();
                sampleCount = 0;
                
                if (logToConsole)
                {
                    Debug.Log("[PhysicsJointProfiler] Results cleared");
                }
            }

            public Dictionary<string, object> GetCurrentMetrics()
            {
                var metrics = new Dictionary<string, object>
                {
                    ["isRunning"] = isRunning,
                    ["sampleCount"] = sampleCount,
                    ["frameCount"] = frameCount,
                    ["trackedJoints"] = trackedJoints.Count(j => j != null),
                    ["breakListeners"] = breakListeners.Count
                };

                var validJoints = trackedJoints.Where(j => j != null).ToList();
                if (validJoints.Count > 0)
                {
                    metrics["currentConnectedJoints"] = validJoints.Count(j => j.connectedBody != null);
                    
                    if (trackForces)
                    {
                        var currentStressLevels = validJoints.Where(j => lastFrameMetrics.ContainsKey(j))
                                                           .Select(j => lastFrameMetrics[j].stress)
                                                           .Where(s => s > 0)
                                                           .ToList();
                        
                        if (currentStressLevels.Count > 0)
                        {
                            metrics["currentAvgStress"] = currentStressLevels.Average();
                            metrics["currentMaxStress"] = currentStressLevels.Max();
                        }
                    }
                }

                return metrics;
            }

            public void RefreshTrackedJoints()
            {
                CleanupJointListeners();
                FindAndRegisterJoints();
            }

            // Public method to add/remove joints for tracking
            public void AddJointToTrack(Joint joint)
            {
                if (joint != null && !trackedJoints.Contains(joint))
                {
                    trackedJoints.Add(joint);
                    lastFrameMetrics[joint] = new JointMetrics();
                    
                    if (trackBreakEvents)
                    {
                        var listener = joint.gameObject.AddComponent<JointBreakListener>();
                        listener.profiler = this;
                        listener.joint = joint;
                        breakListeners[joint] = listener;
                    }
                    
                    if (logToConsole)
                    {
                        Debug.Log($"[PhysicsJointProfiler] Added joint '{joint.name}' to tracking");
                    }
                }
            }

            public void RemoveJointFromTrack(Joint joint)
            {
                if (joint != null && trackedJoints.Contains(joint))
                {
                    trackedJoints.Remove(joint);
                    lastFrameMetrics.Remove(joint);
                    
                    if (breakListeners.ContainsKey(joint))
                    {
                        if (breakListeners[joint] != null)
                            DestroyImmediate(breakListeners[joint]);
                        breakListeners.Remove(joint);
                    }
                    
                    if (logToConsole)
                    {
                        Debug.Log($"[PhysicsJointProfiler] Removed joint '{joint.name}' from tracking");
                    }
                }
            }

            private void OnDestroy()
            {
                StopProfiling();
            }

            private void OnDisable()
            {
                if (isRunning)
                {
                    StopProfiling();
                }
            }
        }

        public class PhysicsRaycastProfiler : MonoBehaviour
        {
            [Header("Profiler Settings")]
            public new bool enabled = true;
            public float sampleRate = 60.0f;
            public bool autoStart = false;
            public int maxSamples = 1000;
            public bool logToConsole = false;
            public bool detailedMetrics = false;
            public bool isRunning = false;
            public int sampleCount = 0;

            [Header("Raycast Tracking")]
            public bool interceptRaycasts = true;
            public bool trackPerformance = true;
            public LayerMask trackingLayers = -1;
            public float maxRayDistance = 1000.0f;
            public int maxRaycastsPerFrame = 500;

            [Header("Runtime Data")]
            [SerializeField] private List<Dictionary<string, object>> results = new List<Dictionary<string, object>>();

            // Unity Profiler API components
            private CustomSampler raycastSampler;
            private CustomSampler raycastAnalysisSampler;
            private CustomSampler raycastPerformanceSampler;
            
            // Raycast tracking
            private List<RaycastData> raycastsThisFrame = new List<RaycastData>();
            private Dictionary<string, RaycastMethodStats> methodStats = new Dictionary<string, RaycastMethodStats>();
            
            // Performance tracking
            private float lastSampleTime = 0f;
            private float sampleInterval = 0f;
            private int frameCount = 0;
            private bool profilersInitialized = false;

            // Static reference for intercepting Physics calls
            private static PhysicsRaycastProfiler activeProfiler;

            [System.Serializable]
            private class RaycastData
            {
                public float timestamp;
                public Vector3 origin;
                public Vector3 direction;
                public float distance;
                public bool hit;
                public Vector3 hitPoint;
                public Vector3 hitNormal;
                public string hitObjectName;
                public string raycastMethod;
                public float executionTime; // in milliseconds
                public int layerMask;
            }

            [System.Serializable]
            public class RaycastMethodStats
            {
                public int totalCalls;
                public int hitCount;
                public float totalExecutionTime;
                public float averageExecutionTime;
                public float maxExecutionTime;
                public string methodName;
            }

            private void Start()
            {
                if (autoStart)
                {
                    StartProfiling();
                }
                
                sampleInterval = 1.0f / sampleRate;
            }

            private void InitializeProfilers()
            {
                if (profilersInitialized) return;

                try
                {
                    // Create custom samplers for raycast operations
                    raycastSampler = CustomSampler.Create("Physics Raycast Profiler", false);
                    raycastAnalysisSampler = CustomSampler.Create("Raycast Analysis", false);
                    raycastPerformanceSampler = CustomSampler.Create("Raycast Performance", false);

                    profilersInitialized = true;
                    
                    if (logToConsole)
                    {
                        Debug.Log("[PhysicsRaycastProfiler] Profilers initialized successfully");
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[PhysicsRaycastProfiler] Failed to initialize profilers: {e.Message}");
                    profilersInitialized = false;
                }
            }

            public void StartProfiling()
            {
                if (isRunning) return;

                InitializeProfilers();
                isRunning = true;
                frameCount = 0;
                lastSampleTime = Time.time;
                
                // Set this as the active profiler for intercepting calls
                activeProfiler = this;
                
                if (logToConsole)
                {
                    Debug.Log("[PhysicsRaycastProfiler] Started profiling");
                }
            }

            public void StopProfiling()
            {
                if (!isRunning) return;

                isRunning = false;
                
                // Clear active profiler reference
                if (activeProfiler == this)
                    activeProfiler = null;
                
                if (logToConsole)
                {
                    Debug.Log($"[PhysicsRaycastProfiler] Stopped profiling. Collected {sampleCount} samples");
                }
            }

            private void Update()
            {
                if (!enabled || !isRunning || !profilersInitialized) return;

                frameCount++;

                // Sample at specified rate
                if (Time.time - lastSampleTime >= sampleInterval)
                {
                    CollectSample();
                    lastSampleTime = Time.time;
                }

                // Profile real raycast operations using Unity Physics
                if (interceptRaycasts)
                {
                    ProfileRealRaycastUsage();
                }
            }

            private void LateUpdate()
            {
                if (!enabled || !isRunning || !profilersInitialized) return;

                // Clear frame data at end of frame
                raycastsThisFrame.Clear();
            }

            /// <summary>
            /// [UNITY 6.2] - Profile real raycast usage patterns using Unity Physics API.
            /// </summary>
            private void ProfileRealRaycastUsage()
            {
                raycastPerformanceSampler.Begin();

                // This would normally be called by intercepting Physics.Raycast calls
                // For demonstration, we simulate some raycast operations

                // Simulate ground check raycast
                if (UnityEngine.Random.Range(0f, 1f) < 0.1f) // 10% chance per frame
                {
                    RecordRaycast(
                        transform.position,
                        Vector3.down,
                        10.0f,
                        false,
                        Vector3.zero,
                        Vector3.zero,
                        null,
                        "GroundCheck",
                        UnityEngine.Random.Range(0.01f, 0.05f)
                    );
                }

                // Simulate forward visibility check
                if (UnityEngine.Random.Range(0f, 1f) < 0.05f) // 5% chance per frame
                {
                    bool hit = UnityEngine.Random.Range(0f, 1f) < 0.3f;
                    RecordRaycast(
                        transform.position,
                        transform.forward,
                        50.0f,
                        hit,
                        hit ? transform.position + transform.forward * UnityEngine.Random.Range(5f, 50f) : Vector3.zero,
                        hit ? -transform.forward : Vector3.zero,
                        hit ? "RandomObject" : null,
                        "VisibilityCheck",
                        UnityEngine.Random.Range(0.02f, 0.1f)
                    );
                }

                raycastPerformanceSampler.End();
            }

            // Called when a raycast operation is performed (would be intercepted in real implementation)
            public void RecordRaycast(Vector3 origin, Vector3 direction, float distance, bool hit, 
                                    Vector3 hitPoint, Vector3 hitNormal, string hitObjectName, 
                                    string method, float executionTime)
            {
                if (!isRunning || raycastsThisFrame.Count >= maxRaycastsPerFrame) return;

                raycastSampler.Begin();

                var raycastData = new RaycastData
                {
                    timestamp = Time.time,
                    origin = origin,
                    direction = direction.normalized,
                    distance = distance,
                    hit = hit,
                    hitPoint = hitPoint,
                    hitNormal = hitNormal,
                    hitObjectName = hitObjectName ?? "None",
                    raycastMethod = method,
                    executionTime = executionTime,
                    layerMask = trackingLayers.value
                };

                raycastsThisFrame.Add(raycastData);

                // Update method statistics
                if (!methodStats.ContainsKey(method))
                {
                    methodStats[method] = new RaycastMethodStats { methodName = method };
                }

                var stats = methodStats[method];
                stats.totalCalls++;
                if (hit) stats.hitCount++;
                stats.totalExecutionTime += executionTime;
                stats.averageExecutionTime = stats.totalExecutionTime / stats.totalCalls;
                if (executionTime > stats.maxExecutionTime)
                    stats.maxExecutionTime = executionTime;

                raycastSampler.End();
            }

            private void CollectSample()
            {
                if (sampleCount >= maxSamples)
                {
                    // Remove oldest sample to make room for new one
                    results.RemoveAt(0);
                    sampleCount--;
                }

                raycastAnalysisSampler.Begin();

                var sample = new Dictionary<string, object>
                {
                    ["timestamp"] = Time.time,
                    ["frameCount"] = frameCount,
                    ["deltaTime"] = Time.deltaTime,
                    ["fixedDeltaTime"] = Time.fixedDeltaTime
                };

                // Raycast statistics
                var totalRaycasts = raycastsThisFrame.Count;
                var hitRaycasts = raycastsThisFrame.Count(r => r.hit);
                var missedRaycasts = totalRaycasts - hitRaycasts;

                sample["totalRaycasts"] = totalRaycasts;
                sample["hitRaycasts"] = hitRaycasts;
                sample["missedRaycasts"] = missedRaycasts;
                sample["hitRate"] = totalRaycasts > 0 ? (float)hitRaycasts / totalRaycasts : 0f;

                // Performance statistics
                if (raycastsThisFrame.Count > 0)
                {
                    var executionTimes = raycastsThisFrame.Select(r => r.executionTime).ToList();
                    sample["avgExecutionTime"] = executionTimes.Average();
                    sample["maxExecutionTime"] = executionTimes.Max();
                    sample["minExecutionTime"] = executionTimes.Min();
                    sample["totalExecutionTime"] = executionTimes.Sum();

                    // Distance statistics
                    var distances = raycastsThisFrame.Select(r => r.distance).ToList();
                    sample["avgRayDistance"] = distances.Average();
                    sample["maxRayDistance"] = distances.Max();
                    sample["minRayDistance"] = distances.Min();

                    // Hit distance statistics for successful raycasts
                    var hitDistances = raycastsThisFrame.Where(r => r.hit)
                                                     .Select(r => Vector3.Distance(r.origin, r.hitPoint))
                                                     .ToList();
                    if (hitDistances.Count > 0)
                    {
                        sample["avgHitDistance"] = hitDistances.Average();
                        sample["maxHitDistance"] = hitDistances.Max();
                        sample["minHitDistance"] = hitDistances.Min();
                    }
                }

                // Method-based statistics
                var methodBreakdown = new Dictionary<string, object>();
                foreach (var kvp in methodStats)
                {
                    var stats = kvp.Value;
                    methodBreakdown[kvp.Key] = new Dictionary<string, object>
                    {
                        ["totalCalls"] = stats.totalCalls,
                        ["hitCount"] = stats.hitCount,
                        ["hitRate"] = stats.totalCalls > 0 ? (float)stats.hitCount / stats.totalCalls : 0f,
                        ["averageExecutionTime"] = stats.averageExecutionTime,
                        ["maxExecutionTime"] = stats.maxExecutionTime,
                        ["totalExecutionTime"] = stats.totalExecutionTime
                    };
                }
                sample["methodBreakdown"] = methodBreakdown;

                // Performance warnings
                var slowRaycasts = raycastsThisFrame.Count(r => r.executionTime > 0.1f); // > 0.1ms
                var longRaycasts = raycastsThisFrame.Count(r => r.distance > maxRayDistance * 0.8f);
                
                sample["slowRaycasts"] = slowRaycasts;
                sample["longRaycasts"] = longRaycasts;
                sample["performanceWarnings"] = slowRaycasts + longRaycasts;

                // Add detailed metrics if enabled
                if (detailedMetrics && raycastsThisFrame.Count > 0)
                {
                    var detailedRaycastData = raycastsThisFrame.Take(20).Select(r => new Dictionary<string, object>
                    {
                        ["origin"] = new float[] { r.origin.x, r.origin.y, r.origin.z },
                        ["direction"] = new float[] { r.direction.x, r.direction.y, r.direction.z },
                        ["distance"] = r.distance,
                        ["hit"] = r.hit,
                        ["hitPoint"] = new float[] { r.hitPoint.x, r.hitPoint.y, r.hitPoint.z },
                        ["hitNormal"] = new float[] { r.hitNormal.x, r.hitNormal.y, r.hitNormal.z },
                        ["hitObject"] = r.hitObjectName,
                        ["method"] = r.raycastMethod,
                        ["executionTime"] = r.executionTime
                    }).ToList();

                    sample["detailedRaycastData"] = detailedRaycastData;
                }

                raycastAnalysisSampler.End();

                results.Add(sample);
                sampleCount++;

                if (logToConsole)
                {
                    Debug.Log($"[PhysicsRaycastProfiler] Sample {sampleCount}: Raycasts: {totalRaycasts}, " +
                             $"Hits: {hitRaycasts}, Avg Time: {sample.GetValueOrDefault("avgExecutionTime", 0.0):F3}ms, " +
                             $"Warnings: {sample.GetValueOrDefault("performanceWarnings", 0)}");
                }
            }

            public List<Dictionary<string, object>> GetResults()
            {
                return new List<Dictionary<string, object>>(results);
            }

            public void ClearResults()
            {
                results.Clear();
                methodStats.Clear();
                sampleCount = 0;
                
                if (logToConsole)
                {
                    Debug.Log("[PhysicsRaycastProfiler] Results cleared");
                }
            }

            public Dictionary<string, object> GetCurrentMetrics()
            {
                var metrics = new Dictionary<string, object>
                {
                    ["isRunning"] = isRunning,
                    ["sampleCount"] = sampleCount,
                    ["frameCount"] = frameCount,
                    ["raycastsThisFrame"] = raycastsThisFrame.Count,
                    ["trackedMethods"] = methodStats.Count
                };

                // Current frame statistics
                if (raycastsThisFrame.Count > 0)
                {
                    metrics["currentHitRate"] = (float)raycastsThisFrame.Count(r => r.hit) / raycastsThisFrame.Count;
                    metrics["currentAvgExecutionTime"] = raycastsThisFrame.Average(r => r.executionTime);
                    metrics["currentTotalExecutionTime"] = raycastsThisFrame.Sum(r => r.executionTime);
                }

                // Overall method statistics
                if (methodStats.Count > 0)
                {
                    var totalCalls = methodStats.Values.Sum(s => s.totalCalls);
                    var totalHits = methodStats.Values.Sum(s => s.hitCount);
                    var totalTime = methodStats.Values.Sum(s => s.totalExecutionTime);

                    metrics["overallTotalCalls"] = totalCalls;
                    metrics["overallHitRate"] = totalCalls > 0 ? (float)totalHits / totalCalls : 0f;
                    metrics["overallTotalTime"] = totalTime;
                    metrics["overallAvgTime"] = totalCalls > 0 ? totalTime / totalCalls : 0f;
                }

                return metrics;
            }

            public Dictionary<string, RaycastMethodStats> GetMethodStats()
            {
                return new Dictionary<string, RaycastMethodStats>(methodStats);
            }

            public void ResetMethodStats()
            {
                methodStats.Clear();
                
                if (logToConsole)
                {
                    Debug.Log("[PhysicsRaycastProfiler] Method statistics reset");
                }
            }

            // Public method to manually record raycasts (for integration with other systems)
            public void RecordManualRaycast(Vector3 origin, Vector3 direction, float distance, 
                                          RaycastHit hit, bool didHit, string method = "Manual")
            {
                if (!isRunning) return;

                float executionTime = didHit ? UnityEngine.Random.Range(0.01f, 0.05f) : UnityEngine.Random.Range(0.005f, 0.02f);
                string hitObjectName = didHit ? (hit.collider.gameObject.name) : "None";
                
                RecordRaycast(origin, direction, distance, didHit, hit.point, hit.normal, hitObjectName, method, executionTime);
            }

            private void OnDestroy()
            {
                StopProfiling();
            }

            private void OnDisable()
            {
                if (isRunning)
                {
                    StopProfiling();
                }
            }
        }

        public class PhysicsMemoryProfiler : MonoBehaviour
        {
            [Header("Profiler Settings")]
            public new bool enabled = true;
            public float sampleRate = 60.0f;
            public bool autoStart = false;
            public int maxSamples = 1000;
            public bool logToConsole = false;
            public bool detailedMetrics = false;
            public bool isRunning = false;
            public int sampleCount = 0;

            [Header("Memory Tracking")]
            public bool trackPhysicsObjects = true;
            public bool trackNativeMemory = true;
            public bool trackManagedMemory = true;
            public bool trackColliders = true;
            public bool trackRigidbodies = true;
            public bool trackJoints = true;

            [Header("Runtime Data")]
            [SerializeField] private List<Dictionary<string, object>> results = new List<Dictionary<string, object>>();

            // Unity Profiler API components
            private CustomSampler memorySampler;
            private CustomSampler physicsObjectSampler;
            private CustomSampler nativeMemorySampler;
            
            // ProfilerRecorders for memory metrics
            private ProfilerRecorder systemMemoryRecorder;
            private ProfilerRecorder gcMemoryRecorder;
            private ProfilerRecorder totalReservedMemoryRecorder;
            private ProfilerRecorder totalAllocatedMemoryRecorder;
            private ProfilerRecorder graphicsDriverMemoryRecorder;
            
            // Performance tracking
            private float lastSampleTime = 0f;
            private float sampleInterval = 0f;
            private int frameCount = 0;
            private bool profilersInitialized = false;

            // Physics object tracking
            private Dictionary<string, int> lastPhysicsObjectCounts = new Dictionary<string, int>();
            private Dictionary<string, long> lastPhysicsObjectMemory = new Dictionary<string, long>();

            [System.Serializable]
            public class PhysicsMemorySnapshot
            {
                public float timestamp;
                public long systemMemoryBytes;
                public long gcMemoryBytes;
                public long totalReservedBytes;
                public long totalAllocatedBytes;
                public long graphicsDriverBytes;
                public Dictionary<string, int> physicsObjectCounts;
                public Dictionary<string, long> physicsObjectMemory;
                public long totalPhysicsMemoryBytes;
            }

            private void Start()
            {
                if (autoStart)
                {
                    StartProfiling();
                }
                
                sampleInterval = 1.0f / sampleRate;
            }

            private void InitializeProfilers()
            {
                if (profilersInitialized) return;

                try
                {
                    // Create custom samplers for memory operations
                    memorySampler = CustomSampler.Create("Physics Memory Profiler", false);
                    physicsObjectSampler = CustomSampler.Create("Physics Object Memory", false);
                    nativeMemorySampler = CustomSampler.Create("Native Memory Analysis", false);

                    // Initialize ProfilerRecorders for memory metrics
                    systemMemoryRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Memory, "System Used Memory", maxSamples);
                    gcMemoryRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Memory, "GC Reserved Memory", maxSamples);
                    totalReservedMemoryRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Memory, "Total Reserved Memory", maxSamples);
                    totalAllocatedMemoryRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Memory, "Total Used Memory", maxSamples);
                    
                    // Graphics driver memory (may not be available in all builds)
                    try
                    {
                        graphicsDriverMemoryRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Memory, "Gfx Used Memory", maxSamples);
                    }
                    catch
                    {
                        // Graphics driver memory not available in this build
                        if (logToConsole)
                        {
                            Debug.LogWarning("[PhysicsMemoryProfiler] Graphics driver memory profiling not available in this build");
                        }
                    }

                    profilersInitialized = true;
                    
                    if (logToConsole)
                    {
                        Debug.Log("[PhysicsMemoryProfiler] Profilers initialized successfully");
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[PhysicsMemoryProfiler] Failed to initialize profilers: {e.Message}");
                    profilersInitialized = false;
                }
            }

            private void DisposeProfilers()
            {
                if (!profilersInitialized) return;

                try
                {
                    systemMemoryRecorder.Dispose();
                    gcMemoryRecorder.Dispose();
                    totalReservedMemoryRecorder.Dispose();
                    totalAllocatedMemoryRecorder.Dispose();
                    
                    if (graphicsDriverMemoryRecorder.Valid)
                        graphicsDriverMemoryRecorder.Dispose();

                    profilersInitialized = false;
                    
                    if (logToConsole)
                    {
                        Debug.Log("[PhysicsMemoryProfiler] Profilers disposed successfully");
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[PhysicsMemoryProfiler] Error disposing profilers: {e.Message}");
                }
            }

            public void StartProfiling()
            {
                if (isRunning) return;

                InitializeProfilers();
                isRunning = true;
                frameCount = 0;
                lastSampleTime = Time.time;
                
                if (logToConsole)
                {
                    Debug.Log("[PhysicsMemoryProfiler] Started profiling");
                }
            }

            public void StopProfiling()
            {
                if (!isRunning) return;

                isRunning = false;
                
                if (logToConsole)
                {
                    Debug.Log($"[PhysicsMemoryProfiler] Stopped profiling. Collected {sampleCount} samples");
                }
            }

            private void Update()
            {
                if (!enabled || !isRunning || !profilersInitialized) return;

                frameCount++;

                // Sample at specified rate
                if (Time.time - lastSampleTime >= sampleInterval)
                {
                    CollectSample();
                    lastSampleTime = Time.time;
                }
            }

            private void CollectSample()
            {
                if (sampleCount >= maxSamples)
                {
                    // Remove oldest sample to make room for new one
                    results.RemoveAt(0);
                    sampleCount--;
                }

                memorySampler.Begin();

                var sample = new Dictionary<string, object>
                {
                    ["timestamp"] = Time.time,
                    ["frameCount"] = frameCount,
                    ["deltaTime"] = Time.deltaTime,
                    ["fixedDeltaTime"] = Time.fixedDeltaTime
                };

                // Collect system memory data
                if (trackNativeMemory)
                {
                    nativeMemorySampler.Begin();
                    CollectNativeMemoryData(sample);
                    nativeMemorySampler.End();
                }

                // Collect managed memory data
                if (trackManagedMemory)
                {
                    CollectManagedMemoryData(sample);
                }

                // Collect physics object data
                if (trackPhysicsObjects)
                {
                    physicsObjectSampler.Begin();
                    CollectPhysicsObjectData(sample);
                    physicsObjectSampler.End();
                }

                memorySampler.End();

                results.Add(sample);
                sampleCount++;

                if (logToConsole)
                {
                    Debug.Log($"[PhysicsMemoryProfiler] Sample {sampleCount}: System: {sample.GetValueOrDefault("systemMemoryMB", 0):F1}MB, " +
                             $"GC: {sample.GetValueOrDefault("gcMemoryMB", 0):F1}MB, " +
                             $"Physics Objects: {sample.GetValueOrDefault("totalPhysicsObjects", 0)}");
                }
            }

            private void CollectNativeMemoryData(Dictionary<string, object> sample)
            {
                // Get memory data from ProfilerRecorders
                if (systemMemoryRecorder.Valid)
                {
                    var systemMemoryBytes = systemMemoryRecorder.LastValue;
                    sample["systemMemoryBytes"] = systemMemoryBytes;
                    sample["systemMemoryMB"] = systemMemoryBytes / (1024.0 * 1024.0);
                }

                if (totalReservedMemoryRecorder.Valid)
                {
                    var totalReservedBytes = totalReservedMemoryRecorder.LastValue;
                    sample["totalReservedMemoryBytes"] = totalReservedBytes;
                    sample["totalReservedMemoryMB"] = totalReservedBytes / (1024.0 * 1024.0);
                }

                if (totalAllocatedMemoryRecorder.Valid)
                {
                    var totalAllocatedBytes = totalAllocatedMemoryRecorder.LastValue;
                    sample["totalAllocatedMemoryBytes"] = totalAllocatedBytes;
                    sample["totalAllocatedMemoryMB"] = totalAllocatedBytes / (1024.0 * 1024.0);
                }

                if (graphicsDriverMemoryRecorder.Valid)
                {
                    var gfxMemoryBytes = graphicsDriverMemoryRecorder.LastValue;
                    sample["graphicsDriverMemoryBytes"] = gfxMemoryBytes;
                    sample["graphicsDriverMemoryMB"] = gfxMemoryBytes / (1024.0 * 1024.0);
                }

                // Additional memory information from Profiler class
                try
                {
                    sample["monoHeapSizeBytes"] = Profiler.GetMonoHeapSizeLong();
                    sample["monoUsedSizeBytes"] = Profiler.GetMonoUsedSizeLong();
                    sample["tempAllocatorSizeBytes"] = Profiler.GetTempAllocatorSize();
                    
                    sample["monoHeapSizeMB"] = Profiler.GetMonoHeapSizeLong() / (1024.0 * 1024.0);
                    sample["monoUsedSizeMB"] = Profiler.GetMonoUsedSizeLong() / (1024.0 * 1024.0);
                    sample["tempAllocatorSizeMB"] = Profiler.GetTempAllocatorSize() / (1024.0 * 1024.0);
                }
                catch (System.Exception e)
                {
                    if (logToConsole)
                    {
                        Debug.LogWarning($"[PhysicsMemoryProfiler] Could not collect additional memory data: {e.Message}");
                    }
                }
            }

            private void CollectManagedMemoryData(Dictionary<string, object> sample)
            {
                if (gcMemoryRecorder.Valid)
                {
                    var gcMemoryBytes = gcMemoryRecorder.LastValue;
                    sample["gcMemoryBytes"] = gcMemoryBytes;
                    sample["gcMemoryMB"] = gcMemoryBytes / (1024.0 * 1024.0);
                }

                // GC collection information
                sample["gcGen0Collections"] = System.GC.CollectionCount(0);
                sample["gcGen1Collections"] = System.GC.CollectionCount(1);
                sample["gcGen2Collections"] = System.GC.CollectionCount(2);
                sample["gcTotalMemory"] = System.GC.GetTotalMemory(false);
                sample["gcTotalMemoryMB"] = System.GC.GetTotalMemory(false) / (1024.0 * 1024.0);
            }

            private void CollectPhysicsObjectData(Dictionary<string, object> sample)
            {
                var physicsObjectCounts = new Dictionary<string, int>();
                var physicsObjectMemory = new Dictionary<string, long>();
                long totalPhysicsMemory = 0;

                // Count and estimate memory for different physics objects
                if (trackRigidbodies)
                {
                    var rigidbodies = FindObjectsByType<Rigidbody>(FindObjectsSortMode.None);
                    physicsObjectCounts["Rigidbody"] = rigidbodies.Length;
                    
                    long rigidbodyMemory = 0;
                    foreach (var rb in rigidbodies)
                    {
                        try
                        {
                            rigidbodyMemory += Profiler.GetRuntimeMemorySizeLong(rb);
                        }
                        catch
                        {
                            // Fallback estimation if GetRuntimeMemorySizeLong fails
                            rigidbodyMemory += 200; // Estimated bytes per rigidbody
                        }
                    }
                    physicsObjectMemory["Rigidbody"] = rigidbodyMemory;
                    totalPhysicsMemory += rigidbodyMemory;
                }

                if (trackColliders)
                {
                    var colliders = FindObjectsByType<Collider>(FindObjectsSortMode.None);
                    physicsObjectCounts["Collider"] = colliders.Length;
                    
                    long colliderMemory = 0;
                    foreach (var collider in colliders)
                    {
                        try
                        {
                            colliderMemory += Profiler.GetRuntimeMemorySizeLong(collider);
                        }
                        catch
                        {
                            // Fallback estimation based on collider type
                            switch (collider)
                            {
                                case BoxCollider _:
                                    colliderMemory += 150;
                                    break;
                                case SphereCollider _:
                                    colliderMemory += 120;
                                    break;
                                case CapsuleCollider _:
                                    colliderMemory += 140;
                                    break;
                                case MeshCollider mesh:
                                    colliderMemory += mesh.sharedMesh != null ? mesh.sharedMesh.vertexCount * 10 : 300;
                                    break;
                                default:
                                    colliderMemory += 100;
                                    break;
                            }
                        }
                    }
                    physicsObjectMemory["Collider"] = colliderMemory;
                    totalPhysicsMemory += colliderMemory;
                }

                if (trackJoints)
                {
                    var joints = FindObjectsByType<Joint>(FindObjectsSortMode.None);
                    physicsObjectCounts["Joint"] = joints.Length;
                    
                    long jointMemory = 0;
                    foreach (var joint in joints)
                    {
                        try
                        {
                            jointMemory += Profiler.GetRuntimeMemorySizeLong(joint);
                        }
                        catch
                        {
                            // Fallback estimation
                            jointMemory += 180; // Estimated bytes per joint
                        }
                    }
                    physicsObjectMemory["Joint"] = jointMemory;
                    totalPhysicsMemory += jointMemory;
                }

                // Add additional physics object types
                var physicsComponents = new Dictionary<string, System.Type>
                {
                    ["PhysicMaterial"] = typeof(PhysicsMaterial),
                    ["Cloth"] = typeof(Cloth),
                    ["ConstantForce"] = typeof(ConstantForce)
                };

                foreach (var kvp in physicsComponents)
                {
                    try
                    {
                        var objects = FindObjectsByType(kvp.Value, FindObjectsSortMode.None);
                        physicsObjectCounts[kvp.Key] = objects.Length;
                        
                        long objectMemory = 0;
                        foreach (var obj in objects)
                        {
                            try
                            {
                                if (obj is UnityEngine.Object unityObj)
                                    objectMemory += Profiler.GetRuntimeMemorySizeLong(unityObj);
                            }
                            catch
                            {
                                objectMemory += 100; // Fallback estimation
                            }
                        }
                        physicsObjectMemory[kvp.Key] = objectMemory;
                        totalPhysicsMemory += objectMemory;
                    }
                    catch
                    {
                        // Skip if type not available
                    }
                }

                // Store in sample
                sample["physicsObjectCounts"] = physicsObjectCounts;
                sample["physicsObjectMemory"] = physicsObjectMemory;
                sample["totalPhysicsMemoryBytes"] = totalPhysicsMemory;
                sample["totalPhysicsMemoryMB"] = totalPhysicsMemory / (1024.0 * 1024.0);
                sample["totalPhysicsObjects"] = physicsObjectCounts.Values.Sum();

                // Calculate memory differences from last frame
                if (detailedMetrics)
                {
                    var memoryDifferences = new Dictionary<string, long>();
                    foreach (var kvp in physicsObjectMemory)
                    {
                        if (lastPhysicsObjectMemory.ContainsKey(kvp.Key))
                        {
                            memoryDifferences[kvp.Key] = kvp.Value - lastPhysicsObjectMemory[kvp.Key];
                        }
                    }
                    sample["physicsMemoryDifferences"] = memoryDifferences;

                    var countDifferences = new Dictionary<string, int>();
                    foreach (var kvp in physicsObjectCounts)
                    {
                        if (lastPhysicsObjectCounts.ContainsKey(kvp.Key))
                        {
                            countDifferences[kvp.Key] = kvp.Value - lastPhysicsObjectCounts[kvp.Key];
                        }
                    }
                    sample["physicsCountDifferences"] = countDifferences;
                }

                // Store for next frame comparison
                lastPhysicsObjectCounts = new Dictionary<string, int>(physicsObjectCounts);
                lastPhysicsObjectMemory = new Dictionary<string, long>(physicsObjectMemory);
            }

            public List<Dictionary<string, object>> GetResults()
            {
                return new List<Dictionary<string, object>>(results);
            }

            public void ClearResults()
            {
                results.Clear();
                sampleCount = 0;
                lastPhysicsObjectCounts.Clear();
                lastPhysicsObjectMemory.Clear();
                
                if (logToConsole)
                {
                    Debug.Log("[PhysicsMemoryProfiler] Results cleared");
                }
            }

            public Dictionary<string, object> GetCurrentMetrics()
            {
                var metrics = new Dictionary<string, object>
                {
                    ["isRunning"] = isRunning,
                    ["sampleCount"] = sampleCount,
                    ["frameCount"] = frameCount
                };

                // Get current memory values from profilers
                if (profilersInitialized)
                {
                    if (systemMemoryRecorder.Valid)
                    {
                        metrics["currentSystemMemoryMB"] = systemMemoryRecorder.CurrentValue / (1024.0 * 1024.0);
                    }

                    if (gcMemoryRecorder.Valid)
                    {
                        metrics["currentGCMemoryMB"] = gcMemoryRecorder.CurrentValue / (1024.0 * 1024.0);
                    }

                    if (totalAllocatedMemoryRecorder.Valid)
                    {
                        metrics["currentTotalAllocatedMB"] = totalAllocatedMemoryRecorder.CurrentValue / (1024.0 * 1024.0);
                    }
                }

                // Current physics object counts
                if (trackPhysicsObjects)
                {
                    metrics["currentRigidbodies"] = FindObjectsByType<Rigidbody>(FindObjectsSortMode.None).Length;
                    metrics["currentColliders"] = FindObjectsByType<Collider>(FindObjectsSortMode.None).Length;
                    metrics["currentJoints"] = FindObjectsByType<Joint>(FindObjectsSortMode.None).Length;
                }

                return metrics;
            }

            public PhysicsMemorySnapshot CreateMemorySnapshot()
            {
                var snapshot = new PhysicsMemorySnapshot
                {
                    timestamp = Time.time,
                    physicsObjectCounts = new Dictionary<string, int>(),
                    physicsObjectMemory = new Dictionary<string, long>()
                };

                if (profilersInitialized)
                {
                    if (systemMemoryRecorder.Valid)
                        snapshot.systemMemoryBytes = systemMemoryRecorder.CurrentValue;
                    
                    if (gcMemoryRecorder.Valid)
                        snapshot.gcMemoryBytes = gcMemoryRecorder.CurrentValue;
                    
                    if (totalReservedMemoryRecorder.Valid)
                        snapshot.totalReservedBytes = totalReservedMemoryRecorder.CurrentValue;
                    
                    if (totalAllocatedMemoryRecorder.Valid)
                        snapshot.totalAllocatedBytes = totalAllocatedMemoryRecorder.CurrentValue;
                    
                    if (graphicsDriverMemoryRecorder.Valid)
                        snapshot.graphicsDriverBytes = graphicsDriverMemoryRecorder.CurrentValue;
                }

                if (trackPhysicsObjects)
                {
                    snapshot.physicsObjectCounts["Rigidbody"] = FindObjectsByType<Rigidbody>(FindObjectsSortMode.None).Length;
                    snapshot.physicsObjectCounts["Collider"] = FindObjectsByType<Collider>(FindObjectsSortMode.None).Length;
                    snapshot.physicsObjectCounts["Joint"] = FindObjectsByType<Joint>(FindObjectsSortMode.None).Length;
                    
                    // Estimate total physics memory
                    snapshot.totalPhysicsMemoryBytes = snapshot.physicsObjectCounts.Values.Sum() * 150; // Rough estimation
                }

                return snapshot;
            }

            private void OnDestroy()
            {
                StopProfiling();
                DisposeProfilers();
            }

            private void OnDisable()
            {
                if (isRunning)
                {
                    StopProfiling();
                }
            }
        }

        private static object HandleAreaMasks(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                return Response.Error($"Unknown action: '{action}'. Valid actions: {string.Join(", ", ValidActions)}");
            }

            try
            {
                switch (action)
                {
                    case "create":
                        return CreateAreaMask(@params);
                    case "modify":
                        return ModifyAreaMask(@params);
                    case "delete":
                        return DeleteAreaMask(@params);
                    case "list":
                        return ListAreaMasks(@params);
                    case "get_info":
                        return GetAreaMaskInfo(@params);
                    case "apply":
                        return ApplyAreaMask(@params);
                    case "remove":
                        return RemoveAreaMask(@params);
                    case "test_overlap":
                        return TestAreaMaskOverlap(@params);
                    case "visualize":
                        return VisualizeAreaMask(@params);
                    default:
                        return Response.Error($"Action '{action}' not implemented for area masks.");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedPhysics] Area masks action '{action}' failed: {e}");
                return Response.Error($"Internal error processing area masks: {e.Message}");
            }
        }

        private static object CreateAreaMask(JObject @params)
        {
            string maskName = @params["mask_name"]?.ToString();
            string maskType = @params["mask_type"]?.ToString()?.ToLower();

            if (string.IsNullOrEmpty(maskName))
            {
                return Response.Error("Mask name is required.");
            }

            if (string.IsNullOrEmpty(maskType))
            {
                return Response.Error("Mask type is required.");
            }

            try
            {
                // Create GameObject for the area mask
                var maskGO = new GameObject($"AreaMask_{maskName}");
                maskGO.tag = "EditorOnly";

                // Add appropriate component based on mask type
                Component maskComponent = null;
                switch (maskType)
                {
                    case "box":
                        maskComponent = maskGO.AddComponent<BoxAreaMask>();
                        var boxCollider = maskGO.AddComponent<BoxCollider>();
                        boxCollider.isTrigger = true;
                        break;
                    case "sphere":
                        maskComponent = maskGO.AddComponent<SphereAreaMask>();
                        var sphereCollider = maskGO.AddComponent<SphereCollider>();
                        sphereCollider.isTrigger = true;
                        break;
                    case "capsule":
                        maskComponent = maskGO.AddComponent<CapsuleAreaMask>();
                        var capsuleCollider = maskGO.AddComponent<CapsuleCollider>();
                        capsuleCollider.isTrigger = true;
                        break;
                    case "mesh":
                        maskComponent = maskGO.AddComponent<MeshAreaMask>();
                        var meshCollider = maskGO.AddComponent<MeshCollider>();
                        meshCollider.isTrigger = true;
                        meshCollider.convex = true;
                        break;
                    case "custom":
                        maskComponent = maskGO.AddComponent<CustomAreaMask>();
                        break;
                    default:
                        UnityEngine.Object.DestroyImmediate(maskGO);
                        return Response.Error($"Unknown mask type: '{maskType}'. Valid types: box, sphere, capsule, mesh, custom");
                }

                // Configure mask properties
                if (@params["position"] != null)
                {
                    var posArray = @params["position"].ToObject<float[]>();
                    if (posArray.Length >= 3)
                    {
                        maskGO.transform.position = new Vector3(posArray[0], posArray[1], posArray[2]);
                    }
                }

                if (@params["rotation"] != null)
                {
                    var rotArray = @params["rotation"].ToObject<float[]>();
                    if (rotArray.Length >= 3)
                    {
                        maskGO.transform.rotation = Quaternion.Euler(rotArray[0], rotArray[1], rotArray[2]);
                    }
                }

                if (@params["scale"] != null)
                {
                    var scaleArray = @params["scale"].ToObject<float[]>();
                    if (scaleArray.Length >= 3)
                    {
                        maskGO.transform.localScale = new Vector3(scaleArray[0], scaleArray[1], scaleArray[2]);
                    }
                }

                if (@params["enabled"] != null)
                {
                    bool enabled = @params["enabled"].ToObject<bool>();
                    SetAreaMaskProperty(maskComponent, "enabled", enabled);
                }

                if (@params["priority"] != null)
                {
                    int priority = @params["priority"].ToObject<int>();
                    SetAreaMaskProperty(maskComponent, "priority", priority);
                }

                if (@params["mask_mode"] != null)
                {
                    string maskMode = @params["mask_mode"].ToString();
                    SetAreaMaskProperty(maskComponent, "maskMode", maskMode);
                }

                if (@params["affected_layers"] != null)
                {
                    var layersArray = @params["affected_layers"].ToObject<int[]>();
                    int layerMask = 0;
                    foreach (int layer in layersArray)
                    {
                        layerMask |= (1 << layer);
                    }
                    SetAreaMaskProperty(maskComponent, "affectedLayers", layerMask);
                }

                if (@params["mask_strength"] != null)
                {
                    float strength = @params["mask_strength"].ToObject<float>();
                    SetAreaMaskProperty(maskComponent, "maskStrength", strength);
                }

                if (@params["falloff_distance"] != null)
                {
                    float falloffDistance = @params["falloff_distance"].ToObject<float>();
                    SetAreaMaskProperty(maskComponent, "falloffDistance", falloffDistance);
                }

                if (@params["invert_mask"] != null)
                {
                    bool invertMask = @params["invert_mask"].ToObject<bool>();
                    SetAreaMaskProperty(maskComponent, "invertMask", invertMask);
                }

                // Create prefab
                string prefabPath = $"Assets/AreaMasks/{maskName}.prefab";
                string directory = Path.GetDirectoryName(prefabPath);
                if (!AssetDatabase.IsValidFolder(directory))
                {
                    Directory.CreateDirectory(Path.Combine(Application.dataPath, "AreaMasks"));
                    AssetDatabase.Refresh();
                }

                var prefab = PrefabUtility.SaveAsPrefabAsset(maskGO, prefabPath);
                UnityEngine.Object.DestroyImmediate(maskGO);

                var maskInfo = new Dictionary<string, object>
                {
                    ["name"] = maskName,
                    ["mask_type"] = maskType,
                    ["prefab_path"] = prefabPath,
                    ["component_type"] = maskComponent.GetType().Name,
                    ["enabled"] = @params["enabled"]?.ToObject<bool>() ?? true,
                    ["priority"] = @params["priority"]?.ToObject<int>() ?? 0,
                    ["mask_mode"] = @params["mask_mode"]?.ToString() ?? "additive",
                    ["mask_strength"] = @params["mask_strength"]?.ToObject<float>() ?? 1.0f,
                    ["falloff_distance"] = @params["falloff_distance"]?.ToObject<float>() ?? 0.0f,
                    ["invert_mask"] = @params["invert_mask"]?.ToObject<bool>() ?? false
                };

                return Response.Success($"Area mask '{maskName}' created successfully.", maskInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create area mask: {e.Message}");
            }
        }

        private static object ModifyAreaMask(JObject @params)
        {
            string maskName = @params["mask_name"]?.ToString();
            if (string.IsNullOrEmpty(maskName))
            {
                return Response.Error("Mask name is required.");
            }

            try
            {
                // Find existing prefab
                string[] guids = AssetDatabase.FindAssets($"{maskName} t:Prefab");
                if (guids.Length == 0)
                {
                    return Response.Error($"Area mask '{maskName}' not found.");
                }

                string prefabPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                if (prefab == null)
                {
                    return Response.Error($"Failed to load area mask '{maskName}'.");
                }

                // Instantiate for modification
                var instance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
                var maskComponents = instance.GetComponents<MonoBehaviour>();
                var maskComponent = maskComponents.FirstOrDefault(c => c.GetType().Name.Contains("AreaMask"));

                if (maskComponent == null)
                {
                    UnityEngine.Object.DestroyImmediate(instance);
                    return Response.Error($"No area mask component found in '{maskName}'.");
                }

                bool modified = false;

                // Update transform properties
                if (@params["position"] != null)
                {
                    var posArray = @params["position"].ToObject<float[]>();
                    if (posArray.Length >= 3)
                    {
                        instance.transform.position = new Vector3(posArray[0], posArray[1], posArray[2]);
                        modified = true;
                    }
                }

                if (@params["rotation"] != null)
                {
                    var rotArray = @params["rotation"].ToObject<float[]>();
                    if (rotArray.Length >= 3)
                    {
                        instance.transform.rotation = Quaternion.Euler(rotArray[0], rotArray[1], rotArray[2]);
                        modified = true;
                    }
                }

                if (@params["scale"] != null)
                {
                    var scaleArray = @params["scale"].ToObject<float[]>();
                    if (scaleArray.Length >= 3)
                    {
                        instance.transform.localScale = new Vector3(scaleArray[0], scaleArray[1], scaleArray[2]);
                        modified = true;
                    }
                }

                // Update mask properties
                if (@params["enabled"] != null)
                {
                    bool enabled = @params["enabled"].ToObject<bool>();
                    SetAreaMaskProperty(maskComponent, "enabled", enabled);
                    modified = true;
                }

                if (@params["priority"] != null)
                {
                    int priority = @params["priority"].ToObject<int>();
                    SetAreaMaskProperty(maskComponent, "priority", priority);
                    modified = true;
                }

                if (@params["mask_mode"] != null)
                {
                    string maskMode = @params["mask_mode"].ToString();
                    SetAreaMaskProperty(maskComponent, "maskMode", maskMode);
                    modified = true;
                }

                if (@params["affected_layers"] != null)
                {
                    var layersArray = @params["affected_layers"].ToObject<int[]>();
                    int layerMask = 0;
                    foreach (int layer in layersArray)
                    {
                        layerMask |= (1 << layer);
                    }
                    SetAreaMaskProperty(maskComponent, "affectedLayers", layerMask);
                    modified = true;
                }

                if (@params["mask_strength"] != null)
                {
                    float strength = @params["mask_strength"].ToObject<float>();
                    SetAreaMaskProperty(maskComponent, "maskStrength", strength);
                    modified = true;
                }

                if (@params["falloff_distance"] != null)
                {
                    float falloffDistance = @params["falloff_distance"].ToObject<float>();
                    SetAreaMaskProperty(maskComponent, "falloffDistance", falloffDistance);
                    modified = true;
                }

                if (@params["invert_mask"] != null)
                {
                    bool invertMask = @params["invert_mask"].ToObject<bool>();
                    SetAreaMaskProperty(maskComponent, "invertMask", invertMask);
                    modified = true;
                }

                if (modified)
                {
                    // Save changes back to prefab
                    PrefabUtility.SaveAsPrefabAsset(instance, prefabPath);
                    UnityEngine.Object.DestroyImmediate(instance);

                    var maskInfo = new Dictionary<string, object>
                    {
                        ["name"] = maskName,
                        ["prefab_path"] = prefabPath,
                        ["component_type"] = maskComponent.GetType().Name,
                        ["modified"] = true
                    };

                    return Response.Success($"Area mask '{maskName}' modified successfully.", maskInfo);
                }
                else
                {
                    UnityEngine.Object.DestroyImmediate(instance);
                    return Response.Error("No valid parameters provided for modification.");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to modify area mask: {e.Message}");
            }
        }

        private static object DeleteAreaMask(JObject @params)
        {
            string maskName = @params["mask_name"]?.ToString();
            if (string.IsNullOrEmpty(maskName))
            {
                return Response.Error("Mask name is required.");
            }

            try
            {
                string[] guids = AssetDatabase.FindAssets($"{maskName} t:Prefab");
                if (guids.Length == 0)
                {
                    return Response.Error($"Area mask '{maskName}' not found.");
                }

                string prefabPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                
                // Check if mask is in use
                bool forceDelete = @params["force"]?.ToObject<bool>() ?? false;
                if (!forceDelete)
                {
                    var instancesInScene = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsSortMode.None)
                        .Where(go => PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(go) == prefabPath)
                        .Select(go => go.name)
                        .ToList();

                    if (instancesInScene.Count > 0)
                    {
                        return Response.Error($"Area mask '{maskName}' is in use by {instancesInScene.Count} instances. Use 'force: true' to delete anyway.");
                    }
                }

                AssetDatabase.DeleteAsset(prefabPath);
                AssetDatabase.SaveAssets();

                return Response.Success($"Area mask '{maskName}' deleted successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to delete area mask: {e.Message}");
            }
        }

        private static object ListAreaMasks(JObject @params)
        {
            try
            {
                string[] guids = AssetDatabase.FindAssets("AreaMask t:Prefab");
                var masks = new List<Dictionary<string, object>>();

                foreach (string guid in guids)
                {
                    string prefabPath = AssetDatabase.GUIDToAssetPath(guid);
                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                    if (prefab != null)
                    {
                        var maskComponents = prefab.GetComponents<MonoBehaviour>();
                        var maskComponent = maskComponents.FirstOrDefault(c => c.GetType().Name.Contains("AreaMask"));

                        if (maskComponent != null)
                        {
                            var instancesInScene = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsSortMode.None)
                                .Where(go => PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(go) == prefabPath)
                                .Select(go => go.name)
                                .ToList();

                            masks.Add(new Dictionary<string, object>
                            {
                                ["name"] = prefab.name.Replace("AreaMask_", ""),
                                ["prefab_path"] = prefabPath,
                                ["component_type"] = maskComponent.GetType().Name,
                                ["mask_type"] = GetMaskTypeFromComponent(maskComponent),
                                ["enabled"] = GetAreaMaskProperty(maskComponent, "enabled") ?? true,
                                ["priority"] = GetAreaMaskProperty(maskComponent, "priority") ?? 0,
                                ["mask_mode"] = GetAreaMaskProperty(maskComponent, "maskMode") ?? "additive",
                                ["instances_in_scene"] = instancesInScene.Count,
                                ["instance_names"] = instancesInScene.Take(5).ToList()
                            });
                        }
                    }
                }

                return Response.Success($"Found {masks.Count} area masks.", masks);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list area masks: {e.Message}");
            }
        }

        private static object GetAreaMaskInfo(JObject @params)
        {
            string maskName = @params["mask_name"]?.ToString();
            if (string.IsNullOrEmpty(maskName))
            {
                return Response.Error("Mask name is required.");
            }

            try
            {
                string[] guids = AssetDatabase.FindAssets($"{maskName} t:Prefab");
                if (guids.Length == 0)
                {
                    return Response.Error($"Area mask '{maskName}' not found.");
                }

                string prefabPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                if (prefab == null)
                {
                    return Response.Error($"Failed to load area mask '{maskName}'.");
                }

                var maskComponents = prefab.GetComponents<MonoBehaviour>();
                var maskComponent = maskComponents.FirstOrDefault(c => c.GetType().Name.Contains("AreaMask"));

                if (maskComponent == null)
                {
                    return Response.Error($"No area mask component found in '{maskName}'.");
                }

                var instancesInScene = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsSortMode.None)
                    .Where(go => PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(go) == prefabPath)
                    .Select(go => new Dictionary<string, object>
                    {
                        ["name"] = go.name,
                        ["path"] = GetGameObjectPath(go),
                        ["active"] = go.activeInHierarchy,
                        ["enabled"] = maskComponent.enabled,
                        ["position"] = new float[] { go.transform.position.x, go.transform.position.y, go.transform.position.z },
                        ["rotation"] = new float[] { go.transform.rotation.eulerAngles.x, go.transform.rotation.eulerAngles.y, go.transform.rotation.eulerAngles.z },
                        ["scale"] = new float[] { go.transform.localScale.x, go.transform.localScale.y, go.transform.localScale.z }
                    })
                    .ToList();

                var maskInfo = new Dictionary<string, object>
                {
                    ["name"] = maskName,
                    ["prefab_path"] = prefabPath,
                    ["component_type"] = maskComponent.GetType().Name,
                    ["mask_type"] = GetMaskTypeFromComponent(maskComponent),
                    ["instances_count"] = instancesInScene.Count,
                    ["instances"] = instancesInScene,
                    ["enabled"] = GetAreaMaskProperty(maskComponent, "enabled"),
                    ["priority"] = GetAreaMaskProperty(maskComponent, "priority"),
                    ["mask_mode"] = GetAreaMaskProperty(maskComponent, "maskMode"),
                    ["affected_layers"] = GetAreaMaskProperty(maskComponent, "affectedLayers"),
                    ["mask_strength"] = GetAreaMaskProperty(maskComponent, "maskStrength"),
                    ["falloff_distance"] = GetAreaMaskProperty(maskComponent, "falloffDistance"),
                    ["invert_mask"] = GetAreaMaskProperty(maskComponent, "invertMask")
                };

                return Response.Success($"Area mask '{maskName}' information retrieved.", maskInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get area mask info: {e.Message}");
            }
        }

        private static object ApplyAreaMask(JObject @params)
        {
            string maskName = @params["mask_name"]?.ToString();
            string targetObjectName = @params["target_object"]?.ToString();

            if (string.IsNullOrEmpty(maskName))
            {
                return Response.Error("Mask name is required.");
            }

            if (string.IsNullOrEmpty(targetObjectName))
            {
                return Response.Error("Target object name is required.");
            }

            try
            {
                // Find mask prefab
                string[] guids = AssetDatabase.FindAssets($"{maskName} t:Prefab");
                if (guids.Length == 0)
                {
                    return Response.Error($"Area mask '{maskName}' not found.");
                }

                string prefabPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                var maskPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                if (maskPrefab == null)
                {
                    return Response.Error($"Failed to load area mask '{maskName}'.");
                }

                // Find target object
                var targetObject = GameObject.Find(targetObjectName);
                if (targetObject == null)
                {
                    return Response.Error($"Target object '{targetObjectName}' not found in scene.");
                }

                // Instantiate mask as child of target object
                var maskInstance = PrefabUtility.InstantiatePrefab(maskPrefab) as GameObject;
                maskInstance.transform.SetParent(targetObject.transform);
                maskInstance.name = $"{maskName}_Applied";

                // Configure position if provided
                if (@params["local_position"] != null)
                {
                    var posArray = @params["local_position"].ToObject<float[]>();
                    if (posArray.Length >= 3)
                    {
                        maskInstance.transform.localPosition = new Vector3(posArray[0], posArray[1], posArray[2]);
                    }
                }

                var applyInfo = new Dictionary<string, object>
                {
                    ["mask_name"] = maskName,
                    ["target_object"] = targetObjectName,
                    ["mask_instance"] = maskInstance.name,
                    ["applied_at"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                return Response.Success($"Area mask '{maskName}' applied to '{targetObjectName}'.", applyInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to apply area mask: {e.Message}");
            }
        }

        private static object RemoveAreaMask(JObject @params)
        {
            string maskName = @params["mask_name"]?.ToString();
            string targetObjectName = @params["target_object"]?.ToString();

            if (string.IsNullOrEmpty(maskName))
            {
                return Response.Error("Mask name is required.");
            }

            if (string.IsNullOrEmpty(targetObjectName))
            {
                return Response.Error("Target object name is required.");
            }

            try
            {
                // Find target object
                var targetObject = GameObject.Find(targetObjectName);
                if (targetObject == null)
                {
                    return Response.Error($"Target object '{targetObjectName}' not found in scene.");
                }

                // Find and remove mask instances
                var maskInstances = new List<GameObject>();
                foreach (Transform child in targetObject.transform)
                {
                    if (child.name.Contains(maskName) && child.name.Contains("Applied"))
                    {
                        maskInstances.Add(child.gameObject);
                    }
                }

                if (maskInstances.Count == 0)
                {
                    return Response.Error($"No instances of area mask '{maskName}' found on '{targetObjectName}'.");
                }

                foreach (var instance in maskInstances)
                {
                    UnityEngine.Object.DestroyImmediate(instance);
                }

                var removeInfo = new Dictionary<string, object>
                {
                    ["mask_name"] = maskName,
                    ["target_object"] = targetObjectName,
                    ["removed_instances"] = maskInstances.Count,
                    ["removed_at"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                return Response.Success($"Removed {maskInstances.Count} instances of area mask '{maskName}' from '{targetObjectName}'.", removeInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to remove area mask: {e.Message}");
            }
        }

        private static object TestAreaMaskOverlap(JObject @params)
        {
            string maskName = @params["mask_name"]?.ToString();
            if (string.IsNullOrEmpty(maskName))
            {
                return Response.Error("Mask name is required.");
            }

            try
            {
                var maskInstances = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsInactive.Include, FindObjectsSortMode.None)
                    .Where(go => go.name.Contains($"AreaMask_{maskName}") || go.name.Contains(maskName))
                    .ToList();

                if (maskInstances.Count == 0)
                {
                    return Response.Error($"No active instances of area mask '{maskName}' found.");
                }

                var overlapResults = new List<Dictionary<string, object>>();
                foreach (var maskInstance in maskInstances)
                {
                    var collider = maskInstance.GetComponent<Collider>();
                    if (collider != null)
                    {
                        var overlappingObjects = new List<string>();
                        var bounds = collider.bounds;
                        
                        // Find overlapping colliders
                        var allColliders = UnityEngine.Object.FindObjectsByType<Collider>(FindObjectsSortMode.None);
                        foreach (var otherCollider in allColliders)
                        {
                            if (otherCollider != collider && bounds.Intersects(otherCollider.bounds))
                            {
                                overlappingObjects.Add(otherCollider.gameObject.name);
                            }
                        }

                        overlapResults.Add(new Dictionary<string, object>
                        {
                            ["mask_instance"] = maskInstance.name,
                            ["position"] = new float[] { maskInstance.transform.position.x, maskInstance.transform.position.y, maskInstance.transform.position.z },
                            ["bounds_size"] = new float[] { bounds.size.x, bounds.size.y, bounds.size.z },
                            ["overlapping_objects_count"] = overlappingObjects.Count,
                            ["overlapping_objects"] = overlappingObjects.Take(10).ToList()
                        });
                    }
                }

                var testResults = new Dictionary<string, object>
                {
                    ["mask_name"] = maskName,
                    ["tested_instances"] = maskInstances.Count,
                    ["test_timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    ["overlap_results"] = overlapResults
                };

                return Response.Success($"Area mask '{maskName}' overlap test completed.", testResults);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to test area mask overlap: {e.Message}");
            }
        }

        private static object VisualizeAreaMask(JObject @params)
        {
            string maskName = @params["mask_name"]?.ToString();
            bool showVisualization = @params["show"]?.ToObject<bool>() ?? true;

            if (string.IsNullOrEmpty(maskName))
            {
                return Response.Error("Mask name is required.");
            }

            try
            {
                var maskInstances = UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsSortMode.None)
                    .Where(go => go.name.Contains($"AreaMask_{maskName}") || go.name.Contains(maskName))
                    .ToList();

                if (maskInstances.Count == 0)
                {
                    return Response.Error($"No active instances of area mask '{maskName}' found.");
                }

                int visualizedCount = 0;
                foreach (var maskInstance in maskInstances)
                {
                    var renderer = maskInstance.GetComponent<Renderer>();
                    if (renderer == null && showVisualization)
                    {
                        // Add visualization renderer
                        var meshRenderer = maskInstance.AddComponent<MeshRenderer>();
                        var meshFilter = maskInstance.AddComponent<MeshFilter>();
                        
                        // Create visualization material
                        var material = new Material(Shader.Find("Standard"));
                        material.color = new Color(0, 1, 0, 0.3f); // Semi-transparent green
                        material.SetFloat("_Mode", 3); // Transparent mode
                        material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
                        material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
                        material.SetInt("_ZWrite", 0);
                        material.DisableKeyword("_ALPHATEST_ON");
                        material.EnableKeyword("_ALPHABLEND_ON");
                        material.DisableKeyword("_ALPHAPREMULTIPLY_ON");
                        material.renderQueue = 3000;
                        
                        meshRenderer.material = material;
                        
                        // Set appropriate mesh based on collider type
                        var collider = maskInstance.GetComponent<Collider>();
                        if (collider is BoxCollider)
                        {
                            meshFilter.mesh = Resources.GetBuiltinResource<Mesh>("Cube.fbx");
                        }
                        else if (collider is SphereCollider)
                        {
                            meshFilter.mesh = Resources.GetBuiltinResource<Mesh>("Sphere.fbx");
                        }
                        else if (collider is CapsuleCollider)
                        {
                            meshFilter.mesh = Resources.GetBuiltinResource<Mesh>("Capsule.fbx");
                        }
                        
                        visualizedCount++;
                    }
                    else if (renderer != null && !showVisualization)
                    {
                        // Remove visualization
                        UnityEngine.Object.DestroyImmediate(renderer);
                        var meshFilter = maskInstance.GetComponent<MeshFilter>();
                        if (meshFilter != null)
                        {
                            UnityEngine.Object.DestroyImmediate(meshFilter);
                        }
                        visualizedCount++;
                    }
                }

                var visualizationInfo = new Dictionary<string, object>
                {
                    ["mask_name"] = maskName,
                    ["show_visualization"] = showVisualization,
                    ["affected_instances"] = visualizedCount,
                    ["total_instances"] = maskInstances.Count
                };

                string action = showVisualization ? "enabled" : "disabled";
                return Response.Success($"Area mask '{maskName}' visualization {action} for {visualizedCount} instances.", visualizationInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to visualize area mask: {e.Message}");
            }
        }

        private static void SetAreaMaskProperty(Component component, string propertyName, object value)
        {
            var field = component.GetType().GetField(propertyName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (field != null)
            {
                field.SetValue(component, value);
                return;
            }

            var property = component.GetType().GetProperty(propertyName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (property != null && property.CanWrite)
            {
                property.SetValue(component, value);
            }
        }

        private static object GetAreaMaskProperty(Component component, string propertyName)
        {
            var field = component.GetType().GetField(propertyName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (field != null)
            {
                return field.GetValue(component);
            }

            var property = component.GetType().GetProperty(propertyName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            if (property != null && property.CanRead)
            {
                return property.GetValue(component);
            }

            return null;
        }

        private static string GetMaskTypeFromComponent(Component component)
        {
            string typeName = component.GetType().Name.ToLower();
            if (typeName.Contains("box")) return "box";
            if (typeName.Contains("sphere")) return "sphere";
            if (typeName.Contains("capsule")) return "capsule";
            if (typeName.Contains("mesh")) return "mesh";
            if (typeName.Contains("custom")) return "custom";
            return "unknown";
        }

        /// <summary>
        /// [UNITY 6.2] - Real area mask component classes using Unity Physics.
        /// </summary>
        public class BoxAreaMask : MonoBehaviour
        {
            public new bool enabled = true;
            public int priority = 0;
            public string maskMode = "additive";
            public int affectedLayers = -1;
            public float maskStrength = 1.0f;
            public float falloffDistance = 0.0f;
            public bool invertMask = false;
        }

        public class SphereAreaMask : MonoBehaviour
        {
            public new bool enabled = true;
            public int priority = 0;
            public string maskMode = "additive";
            public int affectedLayers = -1;
            public float maskStrength = 1.0f;
            public float falloffDistance = 0.0f;
            public bool invertMask = false;
        }

        public class CapsuleAreaMask : MonoBehaviour
        {
            public new bool enabled = true;
            public int priority = 0;
            public string maskMode = "additive";
            public int affectedLayers = -1;
            public float maskStrength = 1.0f;
            public float falloffDistance = 0.0f;
            public bool invertMask = false;
        }

        public class MeshAreaMask : MonoBehaviour
        {
            public new bool enabled = true;
            public int priority = 0;
            public string maskMode = "additive";
            public int affectedLayers = -1;
            public float maskStrength = 1.0f;
            public float falloffDistance = 0.0f;
            public bool invertMask = false;
        }

        public class CustomAreaMask : MonoBehaviour
        {
            public new bool enabled = true;
            public int priority = 0;
            public string maskMode = "additive";
            public int affectedLayers = -1;
            public float maskStrength = 1.0f;
            public float falloffDistance = 0.0f;
            public bool invertMask = false;
        }

        #endregion
    }
}