using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace UnityMcpBridge.Tools
{
    /// <summary>
    /// Handles physics system operations in Unity 6.2.
    /// Implements comprehensive physics management including rigidbodies, joints, cloth, fluids, and more.
    /// Updated for Unity 6.2 API compatibility with proper error handling and validation.
    /// 
    /// Key Unity 6.2 API Changes Applied:
    /// - Rigidbody.drag → Rigidbody.linearDamping
    /// - Rigidbody.angularDrag → Rigidbody.angularDamping  
    /// - Rigidbody.linearVelocity → Rigidbody.velocity
    /// - Removed deprecated Cloth.clothSolverFrequency
    /// - Enhanced directory creation for PhysicMaterial assets
    /// - Added comprehensive error handling and validation
    /// </summary>
    public static class PhysicsSystem
    {
        /// <summary>
        /// Handles physics system commands from the MCP server.
        /// </summary>
        /// <param name="command">The command name</param>
        /// <param name="parameters">Command parameters as JObject</param>
        /// <returns>Response object with success status and data</returns>
        public static object HandleCommand(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                if (string.IsNullOrEmpty(action))
                {
                    return new { success = false, error = "Action parameter is required" };
                }

                UnityEngine.Debug.Log($"PhysicsSystem handling action: {action}");
                
                return action switch
                {
                    "create_rigidbody_system" => HandleCreateRigidbodySystem(parameters),
                    "setup_joint_systems" => HandleSetupJointSystems(parameters),
                    "create_cloth_simulation" => HandleCreateClothSimulation(parameters),
                    "setup_fluid_simulation" => HandleSetupFluidSimulation(parameters),
                    "create_destruction_system" => HandleCreateDestructionSystem(parameters),
                    "setup_vehicle_physics" => HandleSetupVehiclePhysics(parameters),
                    "create_rope_physics" => HandleCreateRopePhysics(parameters),
                    "setup_wind_system" => HandleSetupWindSystem(parameters),
                    "create_gravity_zones" => HandleCreateGravityZones(parameters),
                    "setup_collision_layers" => HandleSetupCollisionLayers(parameters),
                    "create_trigger_systems" => HandleCreateTriggerSystems(parameters),
                    "setup_physics_materials" => HandleSetupPhysicsMaterials(parameters),
                    "create_particle_physics" => HandleCreateParticlePhysics(parameters),
                    "setup_soft_body_physics" => HandleSetupSoftBodyPhysics(parameters),
                    "create_magnetic_fields" => HandleCreateMagneticFields(parameters),
                    "setup_buoyancy_system" => HandleSetupBuoyancySystem(parameters),
                    "create_physics_constraints" => HandleCreatePhysicsConstraints(parameters),
                    "optimize_physics_performance" => HandleOptimizePhysicsPerformance(parameters),
                    _ => HandleCreateRigidbodySystem(parameters) // Default for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in PhysicsSystem.HandleCommand: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        #region Rigidbody System
        private static object HandleCreateRigidbodySystem(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                string gameObjectName = parameters["gameObject"]?.ToString();

                // Handle the main command structure - the action is already "create_rigidbody_system"
                // Check for sub-action or default to "create"
                string subAction = parameters["subAction"]?.ToString() ?? "create";
                
                return subAction switch
                {
                    "create" => CreateRigidbody(parameters),
                    "modify" => ModifyRigidbody(parameters),
                    "remove" => RemoveRigidbody(gameObjectName),
                    "get_properties" => GetRigidbodyProperties(gameObjectName),
                    _ => CreateRigidbody(parameters) // Default to create for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleCreateRigidbodySystem: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateRigidbody(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            Rigidbody rb = go.GetComponent<Rigidbody>();
            if (rb == null)
                rb = go.AddComponent<Rigidbody>();

            // Apply properties
            if (parameters["mass"] != null)
                rb.mass = parameters["mass"].Value<float>();
            // Support both old (drag) and new (linearDamping) parameter names for compatibility
            if (parameters["drag"] != null)
                rb.linearDamping = parameters["drag"].Value<float>();
            if (parameters["linearDamping"] != null)
                rb.linearDamping = parameters["linearDamping"].Value<float>();
            // Support both old (angularDrag) and new (angularDamping) parameter names for compatibility
            if (parameters["angularDrag"] != null)
                rb.angularDamping = parameters["angularDrag"].Value<float>();
            if (parameters["angularDamping"] != null)
                rb.angularDamping = parameters["angularDamping"].Value<float>();
            if (parameters["useGravity"] != null)
                rb.useGravity = parameters["useGravity"].Value<bool>();
            if (parameters["isKinematic"] != null)
                rb.isKinematic = parameters["isKinematic"].Value<bool>();

            // Apply constraints
            if (parameters["constraints"] != null)
            {
                var constraints = parameters["constraints"].ToObject<List<string>>();
                RigidbodyConstraints rbConstraints = RigidbodyConstraints.None;
                
                foreach (string constraint in constraints)
                {
                    if (Enum.TryParse<RigidbodyConstraints>(constraint, out var constraintValue))
                        rbConstraints |= constraintValue;
                }
                rb.constraints = rbConstraints;
            }

            return new { success = true, message = $"Rigidbody created/updated on '{gameObjectName}'" };
        }

        private static object ModifyRigidbody(JObject parameters)
        {
            return CreateRigidbody(parameters); // Same logic for modification
        }

        private static object RemoveRigidbody(string gameObjectName)
        {
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            Rigidbody rb = go.GetComponent<Rigidbody>();
            if (rb == null)
                return new { success = false, error = $"No Rigidbody found on '{gameObjectName}'" };

            UnityEngine.Object.DestroyImmediate(rb);
            return new { success = true, message = $"Rigidbody removed from '{gameObjectName}'" };
        }

        private static object GetRigidbodyProperties(string gameObjectName)
        {
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            Rigidbody rb = go.GetComponent<Rigidbody>();
            if (rb == null)
                return new { success = false, error = $"No Rigidbody found on '{gameObjectName}'" };

            var properties = new
            {
                mass = rb.mass,
                drag = rb.linearDamping,
                angularDrag = rb.angularDamping,
                useGravity = rb.useGravity,
                isKinematic = rb.isKinematic,
                constraints = rb.constraints.ToString(),
                velocity = new { x = rb.linearVelocity.x, y = rb.linearVelocity.y, z = rb.linearVelocity.z },
                angularVelocity = new { x = rb.angularVelocity.x, y = rb.angularVelocity.y, z = rb.angularVelocity.z }
            };

            return new { success = true, data = properties };
        }
        #endregion

        #region Joint Systems
        private static object HandleSetupJointSystems(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                
                // Handle the main command structure - the action is already "setup_joint_systems"
                // Check for sub-action or default to "create"
                string subAction = parameters["subAction"]?.ToString() ?? "create";
                
                return subAction switch
                {
                    "create" => CreateJoint(parameters),
                    "modify" => ModifyJoint(parameters),
                    "remove" => RemoveJoint(parameters),
                    "get_properties" => GetJointProperties(parameters),
                    _ => CreateJoint(parameters) // Default to create for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleSetupJointSystems: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateJoint(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            string jointType = parameters["jointType"]?.ToString();
            
            if (string.IsNullOrEmpty(gameObjectName) || string.IsNullOrEmpty(jointType))
                return new { success = false, error = "GameObject name and joint type are required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            Joint joint = jointType switch
            {
                "FixedJoint" => go.AddComponent<FixedJoint>(),
                "HingeJoint" => go.AddComponent<HingeJoint>(),
                "SpringJoint" => go.AddComponent<SpringJoint>(),
                "CharacterJoint" => go.AddComponent<CharacterJoint>(),
                "ConfigurableJoint" => go.AddComponent<ConfigurableJoint>(),
                _ => null
            };

            if (joint == null)
                return new { success = false, error = $"Unknown joint type: {jointType}" };

            // Set connected body
            if (parameters["connectedBody"] != null)
            {
                string connectedBodyName = parameters["connectedBody"].ToString();
                GameObject connectedGO = GameObject.Find(connectedBodyName);
                if (connectedGO != null)
                {
                    Rigidbody connectedRb = connectedGO.GetComponent<Rigidbody>();
                    if (connectedRb != null)
                        joint.connectedBody = connectedRb;
                }
            }

            // Set anchor
            if (parameters["anchor"] != null)
            {
                var anchor = parameters["anchor"].ToObject<List<float>>();
                if (anchor.Count >= 3)
                    joint.anchor = new Vector3(anchor[0], anchor[1], anchor[2]);
            }

            // Set axis for applicable joints
            if (parameters["axis"] != null && (joint is HingeJoint || joint is ConfigurableJoint))
            {
                var axis = parameters["axis"].ToObject<List<float>>();
                if (axis.Count >= 3)
                {
                    Vector3 axisVector = new Vector3(axis[0], axis[1], axis[2]);
                    if (joint is HingeJoint hinge)
                        hinge.axis = axisVector;
                }
            }

            return new { success = true, message = $"{jointType} created on '{gameObjectName}'" };
        }

        private static object ModifyJoint(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString();
                if (string.IsNullOrEmpty(gameObjectName))
                    return new { success = false, error = "GameObject name is required" };

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                    return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

                // Get the joint component based on type
                string jointType = parameters["jointType"]?.ToString();
                Joint joint = null;

                if (!string.IsNullOrEmpty(jointType))
                {
                    joint = jointType switch
                    {
                        "FixedJoint" => go.GetComponent<FixedJoint>(),
                        "HingeJoint" => go.GetComponent<HingeJoint>(),
                        "SpringJoint" => go.GetComponent<SpringJoint>(),
                        "CharacterJoint" => go.GetComponent<CharacterJoint>(),
                        "ConfigurableJoint" => go.GetComponent<ConfigurableJoint>(),
                        _ => go.GetComponent<Joint>()
                    };
                }
                else
                {
                    joint = go.GetComponent<Joint>();
                }

                if (joint == null)
                    return new { success = false, error = $"No joint found on '{gameObjectName}'" };

                // Modify common joint properties
                if (parameters["connectedBody"] != null)
                {
                    string connectedBodyName = parameters["connectedBody"].ToString();
                    if (string.IsNullOrEmpty(connectedBodyName))
                        joint.connectedBody = null;
                    else
                    {
                        GameObject connectedGO = GameObject.Find(connectedBodyName);
                        if (connectedGO != null)
                        {
                            Rigidbody connectedRb = connectedGO.GetComponent<Rigidbody>();
                            if (connectedRb != null)
                                joint.connectedBody = connectedRb;
                        }
                    }
                }

                if (parameters["anchor"] != null)
                {
                    var anchor = parameters["anchor"].ToObject<List<float>>();
                    if (anchor.Count >= 3)
                        joint.anchor = new Vector3(anchor[0], anchor[1], anchor[2]);
                }

                if (parameters["connectedAnchor"] != null)
                {
                    var connectedAnchor = parameters["connectedAnchor"].ToObject<List<float>>();
                    if (connectedAnchor.Count >= 3)
                        joint.connectedAnchor = new Vector3(connectedAnchor[0], connectedAnchor[1], connectedAnchor[2]);
                }

                if (parameters["breakForce"] != null)
                    joint.breakForce = parameters["breakForce"].Value<float>();

                if (parameters["breakTorque"] != null)
                    joint.breakTorque = parameters["breakTorque"].Value<float>();

                if (parameters["enableCollision"] != null)
                    joint.enableCollision = parameters["enableCollision"].Value<bool>();

                if (parameters["enablePreprocessing"] != null)
                    joint.enablePreprocessing = parameters["enablePreprocessing"].Value<bool>();

                // Modify joint-specific properties
                switch (joint)
                {
                    case HingeJoint hingeJoint:
                        if (parameters["axis"] != null)
                        {
                            var axis = parameters["axis"].ToObject<List<float>>();
                            if (axis.Count >= 3)
                                hingeJoint.axis = new Vector3(axis[0], axis[1], axis[2]);
                        }
                        if (parameters["useMotor"] != null)
                            hingeJoint.useMotor = parameters["useMotor"].Value<bool>();
                        if (parameters["useLimits"] != null)
                            hingeJoint.useLimits = parameters["useLimits"].Value<bool>();
                        if (parameters["useSpring"] != null)
                            hingeJoint.useSpring = parameters["useSpring"].Value<bool>();
                        
                        // Motor settings
                        if (parameters["motorForce"] != null || parameters["motorTargetVelocity"] != null)
                        {
                            var motor = hingeJoint.motor;
                            if (parameters["motorForce"] != null)
                                motor.force = parameters["motorForce"].Value<float>();
                            if (parameters["motorTargetVelocity"] != null)
                                motor.targetVelocity = parameters["motorTargetVelocity"].Value<float>();
                            hingeJoint.motor = motor;
                        }
                        
                        // Limit settings
                        if (parameters["limitMin"] != null || parameters["limitMax"] != null)
                        {
                            var limits = hingeJoint.limits;
                            if (parameters["limitMin"] != null)
                                limits.min = parameters["limitMin"].Value<float>();
                            if (parameters["limitMax"] != null)
                                limits.max = parameters["limitMax"].Value<float>();
                            hingeJoint.limits = limits;
                        }
                        
                        // Spring settings
                        if (parameters["springForce"] != null || parameters["springDamper"] != null)
                        {
                            var spring = hingeJoint.spring;
                            if (parameters["springForce"] != null)
                                spring.spring = parameters["springForce"].Value<float>();
                            if (parameters["springDamper"] != null)
                                spring.damper = parameters["springDamper"].Value<float>();
                            hingeJoint.spring = spring;
                        }
                        break;

                    case SpringJoint springJoint:
                        if (parameters["spring"] != null)
                            springJoint.spring = parameters["spring"].Value<float>();
                        if (parameters["damper"] != null)
                            springJoint.damper = parameters["damper"].Value<float>();
                        if (parameters["minDistance"] != null)
                            springJoint.minDistance = parameters["minDistance"].Value<float>();
                        if (parameters["maxDistance"] != null)
                            springJoint.maxDistance = parameters["maxDistance"].Value<float>();
                        if (parameters["tolerance"] != null)
                            springJoint.tolerance = parameters["tolerance"].Value<float>();
                        break;

                    case ConfigurableJoint configurableJoint:
                        // X/Y/Z Motion
                        if (parameters["xMotion"] != null && Enum.TryParse<ConfigurableJointMotion>(parameters["xMotion"].ToString(), out var xMotion))
                            configurableJoint.xMotion = xMotion;
                        if (parameters["yMotion"] != null && Enum.TryParse<ConfigurableJointMotion>(parameters["yMotion"].ToString(), out var yMotion))
                            configurableJoint.yMotion = yMotion;
                        if (parameters["zMotion"] != null && Enum.TryParse<ConfigurableJointMotion>(parameters["zMotion"].ToString(), out var zMotion))
                            configurableJoint.zMotion = zMotion;
                        
                        // Angular X/Y/Z Motion
                        if (parameters["angularXMotion"] != null && Enum.TryParse<ConfigurableJointMotion>(parameters["angularXMotion"].ToString(), out var angularXMotion))
                            configurableJoint.angularXMotion = angularXMotion;
                        if (parameters["angularYMotion"] != null && Enum.TryParse<ConfigurableJointMotion>(parameters["angularYMotion"].ToString(), out var angularYMotion))
                            configurableJoint.angularYMotion = angularYMotion;
                        if (parameters["angularZMotion"] != null && Enum.TryParse<ConfigurableJointMotion>(parameters["angularZMotion"].ToString(), out var angularZMotion))
                            configurableJoint.angularZMotion = angularZMotion;
                        
                        // Linear limit
                        if (parameters["linearLimit"] != null)
                        {
                            var linearLimit = configurableJoint.linearLimit;
                            linearLimit.limit = parameters["linearLimit"].Value<float>();
                            configurableJoint.linearLimit = linearLimit;
                        }
                        
                        // Angular limits
                        if (parameters["lowAngularXLimit"] != null)
                        {
                            var lowAngularXLimit = configurableJoint.lowAngularXLimit;
                            lowAngularXLimit.limit = parameters["lowAngularXLimit"].Value<float>();
                            configurableJoint.lowAngularXLimit = lowAngularXLimit;
                        }
                        if (parameters["highAngularXLimit"] != null)
                        {
                            var highAngularXLimit = configurableJoint.highAngularXLimit;
                            highAngularXLimit.limit = parameters["highAngularXLimit"].Value<float>();
                            configurableJoint.highAngularXLimit = highAngularXLimit;
                        }
                        if (parameters["angularYLimit"] != null)
                        {
                            var angularYLimit = configurableJoint.angularYLimit;
                            angularYLimit.limit = parameters["angularYLimit"].Value<float>();
                            configurableJoint.angularYLimit = angularYLimit;
                        }
                        if (parameters["angularZLimit"] != null)
                        {
                            var angularZLimit = configurableJoint.angularZLimit;
                            angularZLimit.limit = parameters["angularZLimit"].Value<float>();
                            configurableJoint.angularZLimit = angularZLimit;
                        }
                        break;
                }

                return new { success = true, message = $"Joint modified on '{gameObjectName}'" };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error modifying joint: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object RemoveJoint(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            Joint[] joints = go.GetComponents<Joint>();
            foreach (Joint joint in joints)
                UnityEngine.Object.DestroyImmediate(joint);

            return new { success = true, message = $"All joints removed from '{gameObjectName}'" };
        }

        private static object GetJointProperties(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString();
                if (string.IsNullOrEmpty(gameObjectName))
                    return new { success = false, error = "GameObject name is required" };

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                    return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

                Joint[] joints = go.GetComponents<Joint>();
                if (joints.Length == 0)
                    return new { success = false, error = $"No joints found on '{gameObjectName}'" };

                var jointList = new List<object>();

                foreach (Joint joint in joints)
                {
                    var jointData = new Dictionary<string, object>
                    {
                        ["type"] = joint.GetType().Name,
                        ["anchor"] = new { x = joint.anchor.x, y = joint.anchor.y, z = joint.anchor.z },
                        ["connectedAnchor"] = new { x = joint.connectedAnchor.x, y = joint.connectedAnchor.y, z = joint.connectedAnchor.z },
                        ["breakForce"] = joint.breakForce,
                        ["breakTorque"] = joint.breakTorque,
                        ["enableCollision"] = joint.enableCollision,
                        ["enablePreprocessing"] = joint.enablePreprocessing,
                        ["connectedBody"] = joint.connectedBody != null ? joint.connectedBody.name : null,
                        ["connectedMassScale"] = joint.connectedMassScale,
                        ["massScale"] = joint.massScale
                    };

                    // Add joint-specific properties
                    switch (joint)
                    {
                        case HingeJoint hingeJoint:
                            jointData["axis"] = new { x = hingeJoint.axis.x, y = hingeJoint.axis.y, z = hingeJoint.axis.z };
                            jointData["useMotor"] = hingeJoint.useMotor;
                            jointData["useLimits"] = hingeJoint.useLimits;
                            jointData["useSpring"] = hingeJoint.useSpring;
                            jointData["angle"] = hingeJoint.angle;
                            jointData["velocity"] = hingeJoint.velocity;
                            
                            if (hingeJoint.useMotor)
                            {
                                jointData["motor"] = new
                                {
                                    force = hingeJoint.motor.force,
                                    targetVelocity = hingeJoint.motor.targetVelocity,
                                    freeSpin = hingeJoint.motor.freeSpin
                                };
                            }
                            
                            if (hingeJoint.useLimits)
                            {
                                jointData["limits"] = new
                                {
                                    min = hingeJoint.limits.min,
                                    max = hingeJoint.limits.max,
                                    bounciness = hingeJoint.limits.bounciness,
                                    bounceMinVelocity = hingeJoint.limits.bounceMinVelocity,
                                    contactDistance = hingeJoint.limits.contactDistance
                                };
                            }
                            
                            if (hingeJoint.useSpring)
                            {
                                jointData["spring"] = new
                                {
                                    spring = hingeJoint.spring.spring,
                                    damper = hingeJoint.spring.damper,
                                    targetPosition = hingeJoint.spring.targetPosition
                                };
                            }
                            break;

                        case SpringJoint springJoint:
                            jointData["spring"] = springJoint.spring;
                            jointData["damper"] = springJoint.damper;
                            jointData["minDistance"] = springJoint.minDistance;
                            jointData["maxDistance"] = springJoint.maxDistance;
                            jointData["tolerance"] = springJoint.tolerance;
                            break;

                        case FixedJoint fixedJoint:
                            // Fixed joints don't have additional properties
                            break;

                        case CharacterJoint characterJoint:
                            jointData["axis"] = new { x = characterJoint.axis.x, y = characterJoint.axis.y, z = characterJoint.axis.z };
                            jointData["swingAxis"] = new { x = characterJoint.swingAxis.x, y = characterJoint.swingAxis.y, z = characterJoint.swingAxis.z };
                            jointData["twistLimitSpring"] = new
                            {
                                spring = characterJoint.twistLimitSpring.spring,
                                damper = characterJoint.twistLimitSpring.damper
                            };
                            jointData["swingLimitSpring"] = new
                            {
                                spring = characterJoint.swingLimitSpring.spring,
                                damper = characterJoint.swingLimitSpring.damper
                            };
                            break;

                        case ConfigurableJoint configurableJoint:
                            jointData["xMotion"] = configurableJoint.xMotion.ToString();
                            jointData["yMotion"] = configurableJoint.yMotion.ToString();
                            jointData["zMotion"] = configurableJoint.zMotion.ToString();
                            jointData["angularXMotion"] = configurableJoint.angularXMotion.ToString();
                            jointData["angularYMotion"] = configurableJoint.angularYMotion.ToString();
                            jointData["angularZMotion"] = configurableJoint.angularZMotion.ToString();
                            
                            jointData["linearLimit"] = new
                            {
                                limit = configurableJoint.linearLimit.limit,
                                bounciness = configurableJoint.linearLimit.bounciness,
                                contactDistance = configurableJoint.linearLimit.contactDistance
                            };
                            
                            jointData["targetPosition"] = new { x = configurableJoint.targetPosition.x, y = configurableJoint.targetPosition.y, z = configurableJoint.targetPosition.z };
                            jointData["targetRotation"] = new { x = configurableJoint.targetRotation.x, y = configurableJoint.targetRotation.y, z = configurableJoint.targetRotation.z, w = configurableJoint.targetRotation.w };
                            break;
                    }

                    jointList.Add(jointData);
                }

                return new { success = true, data = jointList };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error getting joint properties: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }
        #endregion

        #region Cloth Simulation
        private static object HandleCreateClothSimulation(JObject parameters)
        {
            try
            {
                string subAction = parameters["action"]?.ToString() ?? "create";
                
                return subAction switch
                {
                    "create" => CreateClothSimulation(parameters),
                    "modify_constraints" => ModifyClothConstraints(parameters),
                    "set_collision" => SetClothCollision(parameters),
                    "remove" => RemoveClothSimulation(parameters),
                    _ => CreateClothSimulation(parameters) // Default for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleCreateClothSimulation: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateClothSimulation(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            Cloth cloth = go.GetComponent<Cloth>();
            if (cloth == null)
                cloth = go.AddComponent<Cloth>();

            // Apply cloth properties
            if (parameters["damping"] != null)
                cloth.damping = parameters["damping"].Value<float>();
            if (parameters["stretching"] != null)
                cloth.stretchingStiffness = parameters["stretching"].Value<float>();
            if (parameters["bending"] != null)
                cloth.bendingStiffness = parameters["bending"].Value<float>();

            return new { success = true, message = $"Cloth simulation created on '{gameObjectName}'" };
        }

        private static object ModifyClothConstraints(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString();
                if (string.IsNullOrEmpty(gameObjectName))
                    return new { success = false, error = "GameObject name is required" };

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                    return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

                Cloth cloth = go.GetComponent<Cloth>();
                if (cloth == null)
                    return new { success = false, error = $"No Cloth component found on '{gameObjectName}'" };

                // Set cloth properties
                if (parameters["stretching"] != null)
                    cloth.stretchingStiffness = parameters["stretching"].Value<float>();

                if (parameters["bending"] != null)
                    cloth.bendingStiffness = parameters["bending"].Value<float>();

                if (parameters["damping"] != null)
                    cloth.damping = parameters["damping"].Value<float>();

                if (parameters["useGravity"] != null)
                    cloth.useGravity = parameters["useGravity"].Value<bool>();

                if (parameters["friction"] != null)
                    cloth.friction = parameters["friction"].Value<float>();

                if (parameters["collisionMassScale"] != null)
                    cloth.collisionMassScale = parameters["collisionMassScale"].Value<float>();

                if (parameters["selfCollisionDistance"] != null)
                    cloth.selfCollisionDistance = parameters["selfCollisionDistance"].Value<float>();

                if (parameters["selfCollisionStiffness"] != null)
                    cloth.selfCollisionStiffness = parameters["selfCollisionStiffness"].Value<float>();

                if (parameters["sleepThreshold"] != null)
                    cloth.sleepThreshold = parameters["sleepThreshold"].Value<float>();

                if (parameters["enableContinuousCollision"] != null)
                    cloth.enableContinuousCollision = parameters["enableContinuousCollision"].Value<bool>();

                if (parameters["useVirtualParticles"] != null)
                    cloth.useVirtualParticles = parameters["useVirtualParticles"].Value<float>();

                if (parameters["worldVelocityScale"] != null)
                    cloth.worldVelocityScale = parameters["worldVelocityScale"].Value<float>();

                if (parameters["worldAccelerationScale"] != null)
                    cloth.worldAccelerationScale = parameters["worldAccelerationScale"].Value<float>();

                // External forces
                if (parameters["externalAcceleration"] != null)
                {
                    var acceleration = parameters["externalAcceleration"].ToObject<List<float>>();
                    if (acceleration.Count >= 3)
                        cloth.externalAcceleration = new Vector3(acceleration[0], acceleration[1], acceleration[2]);
                }

                if (parameters["randomAcceleration"] != null)
                {
                    var randomAcceleration = parameters["randomAcceleration"].ToObject<List<float>>();
                    if (randomAcceleration.Count >= 3)
                        cloth.randomAcceleration = new Vector3(randomAcceleration[0], randomAcceleration[1], randomAcceleration[2]);
                }

                // Constraints - using Unity 6.2 API
                if (parameters["constraints"] != null)
                {
                    var constraintsData = parameters["constraints"];
                    
                    // Handle coefficient arrays for different constraint types
                    if (constraintsData["distance"] != null)
                    {
                        var distanceCoefs = constraintsData["distance"].ToObject<List<float>>();
                        var coefficients = cloth.coefficients;
                        for (int i = 0; i < coefficients.Length && i < distanceCoefs.Count; i++)
                        {
                            coefficients[i] = new ClothSkinningCoefficient
                            {
                                maxDistance = distanceCoefs[i],
                                collisionSphereDistance = 0f
                            };
                        }
                        cloth.coefficients = coefficients;
                    }
                }

                // Capsule colliders
                if (parameters["capsuleColliders"] != null)
                {
                    var capsuleColliders = parameters["capsuleColliders"].ToObject<List<string>>();
                    var colliders = new List<CapsuleCollider>();
                    
                    foreach (string colliderName in capsuleColliders)
                    {
                        GameObject colliderGO = GameObject.Find(colliderName);
                        if (colliderGO != null)
                        {
                            CapsuleCollider capsule = colliderGO.GetComponent<CapsuleCollider>();
                            if (capsule != null)
                                colliders.Add(capsule);
                        }
                    }
                    
                    cloth.capsuleColliders = colliders.ToArray();
                }

                // Sphere colliders
                if (parameters["sphereColliders"] != null)
                {
                    var sphereColliders = parameters["sphereColliders"].ToObject<List<string>>();
                    var colliders = new List<SphereCollider>();
                    
                    foreach (string colliderName in sphereColliders)
                    {
                        GameObject colliderGO = GameObject.Find(colliderName);
                        if (colliderGO != null)
                        {
                            SphereCollider sphere = colliderGO.GetComponent<SphereCollider>();
                            if (sphere != null)
                                colliders.Add(sphere);
                        }
                    }
                    
                    var sphereColliderPairs = new ClothSphereColliderPair[colliders.Count];
                    for (int i = 0; i < colliders.Count; i++)
                    {
                        sphereColliderPairs[i] = new ClothSphereColliderPair(colliders[i]);
                    }
                    cloth.sphereColliders = sphereColliderPairs;
                }

                return new { success = true, message = $"Cloth constraints modified on '{gameObjectName}'" };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error modifying cloth constraints: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }



        private static object RemoveClothSimulation(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            Cloth cloth = go.GetComponent<Cloth>();
            if (cloth != null)
                UnityEngine.Object.DestroyImmediate(cloth);

            return new { success = true, message = $"Cloth simulation removed from '{gameObjectName}'" };
        }
        #endregion

        #region Physics Materials
        private static object HandleSetupPhysicsMaterials(JObject parameters)
        {
            try
            {
                string subAction = parameters["subAction"]?.ToString() ?? "create_material";
                
                return subAction switch
                {
                    "create_material" => CreatePhysicsMaterial(parameters),
                    "apply_to_collider" => ApplyPhysicsMaterial(parameters),
                    "modify_properties" => ModifyPhysicsMaterial(parameters),
                    "remove" => RemovePhysicsMaterial(parameters),
                    _ => CreatePhysicsMaterial(parameters) // Default for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleSetupPhysicsMaterials: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreatePhysicsMaterial(JObject parameters)
        {
            try
            {
                string materialName = parameters["material_name"]?.ToString();
                if (string.IsNullOrEmpty(materialName))
                    return new { success = false, error = "Material name is required" };

                PhysicsMaterial material = new PhysicsMaterial(materialName);

            if (parameters["friction"] != null)
            {
                float friction = parameters["friction"].Value<float>();
                material.staticFriction = friction;
                material.dynamicFriction = friction;
            }

            if (parameters["bounciness"] != null)
                material.bounciness = parameters["bounciness"].Value<float>();

            if (parameters["friction_combine"] != null)
            {
                string frictionCombine = parameters["friction_combine"].ToString();
                if (Enum.TryParse<PhysicsMaterialCombine>(frictionCombine, out var frictionMode))
                    material.frictionCombine = frictionMode;
            }

            if (parameters["bounce_combine"] != null)
            {
                string bounceCombine = parameters["bounce_combine"].ToString();
                if (Enum.TryParse<PhysicsMaterialCombine>(bounceCombine, out var bounceMode))
                    material.bounceCombine = bounceMode;
            }

            // Save material as asset - Unity 6.2 compatible approach
            string materialPath = parameters["material_path"]?.ToString() ?? $"Assets/PhysicsMaterials/{materialName}.physicMaterial";
            
            // Ensure path uses correct extension for PhysicMaterial
            if (!materialPath.EndsWith(".physicMaterial"))
            {
                materialPath = System.IO.Path.ChangeExtension(materialPath, ".physicMaterial");
            }
            
            string directory = System.IO.Path.GetDirectoryName(materialPath).Replace('\\', '/');
            
            // Create directory structure using Unity 6.2 AssetDatabase API
            if (!string.IsNullOrEmpty(directory) && !AssetDatabase.IsValidFolder(directory))
            {
                // Split path and create folders incrementally
                string[] folders = directory.Split('/');
                string currentPath = "";
                
                for (int i = 0; i < folders.Length; i++)
                {
                    if (i == 0)
                    {
                        currentPath = folders[i];
                        continue;
                    }
                    
                    string parentPath = currentPath;
                    currentPath = currentPath + "/" + folders[i];
                    
                    // Only create if folder doesn't exist
                    if (!AssetDatabase.IsValidFolder(currentPath))
                    {
                        string guid = AssetDatabase.CreateFolder(parentPath, folders[i]);
                        if (string.IsNullOrEmpty(guid))
                        {
                            UnityEngine.Debug.LogError($"Failed to create folder: {currentPath}");
                            return new { success = false, error = $"Failed to create directory: {currentPath}" };
                        }
                    }
                }
                
                // Refresh to ensure folders are recognized
                AssetDatabase.Refresh();
            }

                AssetDatabase.CreateAsset(material, materialPath);
                AssetDatabase.SaveAssets();

                return new { success = true, message = $"Physics material '{materialName}' created at '{materialPath}'" };
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"Error creating physics material: {ex.Message}");
                return new { success = false, error = $"Failed to create physics material: {ex.Message}" };
            }
        }

        private static object ApplyPhysicsMaterial(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            string materialName = parameters["material_name"]?.ToString();
            
            if (string.IsNullOrEmpty(gameObjectName) || string.IsNullOrEmpty(materialName))
                return new { success = false, error = "GameObject name and material name are required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            Collider collider = go.GetComponent<Collider>();
            if (collider == null)
                return new { success = false, error = $"No collider found on '{gameObjectName}'" };

            // Find material by name
            string[] guids = AssetDatabase.FindAssets($"{materialName} t:PhysicMaterial");
            if (guids.Length == 0)
                return new { success = false, error = $"Physics material '{materialName}' not found" };

            string assetPath = AssetDatabase.GUIDToAssetPath(guids[0]);
            PhysicsMaterial material = AssetDatabase.LoadAssetAtPath<PhysicsMaterial>(assetPath);
            
            if (material == null)
                return new { success = false, error = $"Failed to load physics material '{materialName}'" };

            collider.material = material;
            return new { success = true, message = $"Physics material '{materialName}' applied to '{gameObjectName}'" };
        }

        private static object ModifyPhysicsMaterial(JObject parameters)
        {
            try
            {
                string materialName = parameters["material_name"]?.ToString();
                if (string.IsNullOrEmpty(materialName))
                    return new { success = false, error = "Material name is required" };

                // Find the material asset
                string[] guids = AssetDatabase.FindAssets($"{materialName} t:PhysicMaterial");
                if (guids.Length == 0)
                    return new { success = false, error = $"Physics material '{materialName}' not found" };

                string assetPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                PhysicsMaterial material = AssetDatabase.LoadAssetAtPath<PhysicsMaterial>(assetPath);
                
                if (material == null)
                    return new { success = false, error = $"Failed to load physics material '{materialName}'" };

                // Modify properties
                if (parameters["friction"] != null)
                {
                    float friction = parameters["friction"].Value<float>();
                    material.staticFriction = friction;
                    material.dynamicFriction = friction;
                }

                if (parameters["staticFriction"] != null)
                    material.staticFriction = parameters["staticFriction"].Value<float>();

                if (parameters["dynamicFriction"] != null)
                    material.dynamicFriction = parameters["dynamicFriction"].Value<float>();

                if (parameters["bounciness"] != null)
                    material.bounciness = parameters["bounciness"].Value<float>();

                if (parameters["friction_combine"] != null)
                {
                    string frictionCombine = parameters["friction_combine"].ToString();
                    if (Enum.TryParse<PhysicsMaterialCombine>(frictionCombine, out var frictionMode))
                        material.frictionCombine = frictionMode;
                }

                if (parameters["bounce_combine"] != null)
                {
                    string bounceCombine = parameters["bounce_combine"].ToString();
                    if (Enum.TryParse<PhysicsMaterialCombine>(bounceCombine, out var bounceMode))
                        material.bounceCombine = bounceMode;
                }

                // Mark as dirty and save
                EditorUtility.SetDirty(material);
                AssetDatabase.SaveAssetIfDirty(material);

                return new { success = true, message = $"Physics material '{materialName}' modified successfully" };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error modifying physics material: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object RemovePhysicsMaterial(JObject parameters)
        {
            try
            {
                string materialName = parameters["material_name"]?.ToString();
                if (string.IsNullOrEmpty(materialName))
                    return new { success = false, error = "Material name is required" };

                // Find the material asset
                string[] guids = AssetDatabase.FindAssets($"{materialName} t:PhysicMaterial");
                if (guids.Length == 0)
                    return new { success = false, error = $"Physics material '{materialName}' not found" };

                string assetPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                PhysicsMaterial material = AssetDatabase.LoadAssetAtPath<PhysicsMaterial>(assetPath);
                
                if (material == null)
                    return new { success = false, error = $"Failed to load physics material '{materialName}'" };

                // Remove from all colliders that are using this material
                Collider[] allColliders = UnityEngine.Object.FindObjectsByType<Collider>(FindObjectsSortMode.None);
                int colliderCount = 0;
                
                foreach (Collider collider in allColliders)
                {
                    if (collider.material == material)
                    {
                        collider.material = null;
                        colliderCount++;
                    }
                }

                // Delete the asset
                bool deleteResult = AssetDatabase.DeleteAsset(assetPath);
                
                if (deleteResult)
                {
                    AssetDatabase.Refresh();
                    return new { 
                        success = true, 
                        message = $"Physics material '{materialName}' removed successfully. Updated {colliderCount} colliders." 
                    };
                }
                else
                {
                    return new { success = false, error = $"Failed to delete physics material asset at '{assetPath}'" };
                }
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error removing physics material: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }
        #endregion

        #region Fluid Simulation
        private static object HandleSetupFluidSimulation(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                
                // Handle the main command structure - the action is already "setup_fluid_simulation"
                // Check for sub-action or default to "create_fluid"
                string subAction = parameters["subAction"]?.ToString() ?? "create_fluid";
                
                return subAction switch
                {
                    "create_fluid" => CreateFluidSimulation(parameters),
                    "set_properties" => ModifyFluidProperties(parameters),
                    "add_forces" => AddFluidEmitter(parameters),
                    "remove" => RemoveFluidSimulation(parameters),
                    _ => CreateFluidSimulation(parameters) // Default to create for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleSetupFluidSimulation: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateFluidSimulation(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            // Create particle system for fluid simulation
            ParticleSystem ps = go.GetComponent<ParticleSystem>();
            if (ps == null)
                ps = go.AddComponent<ParticleSystem>();

            var main = ps.main;
            main.startLifetime = parameters["lifetime"]?.Value<float>() ?? 5.0f;
            main.startSpeed = parameters["speed"]?.Value<float>() ?? 5.0f;
            main.maxParticles = parameters["particle_count"]?.Value<int>() ?? 1000;
            main.simulationSpace = ParticleSystemSimulationSpace.World;

            // Enable collision for fluid behavior
            var collision = ps.collision;
            collision.enabled = true;
            collision.type = ParticleSystemCollisionType.World;
            collision.dampen = parameters["damping"]?.Value<float>() ?? 0.5f;
            collision.bounce = parameters["bounce"]?.Value<float>() ?? 0.3f;

            // Setup fluid-like emission
            var emission = ps.emission;
            emission.enabled = true;
            emission.rateOverTime = parameters["emission_rate"]?.Value<float>() ?? 50.0f;

            // Add gravity modifier for fluid behavior
            var gravityModifier = ps.main;
            gravityModifier.gravityModifier = parameters["gravity_modifier"]?.Value<float>() ?? 1.0f;

            return new { success = true, message = $"Fluid simulation created on '{gameObjectName}'" };
        }

        private static object ModifyFluidProperties(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            ParticleSystem ps = go.GetComponent<ParticleSystem>();
            if (ps == null)
                return new { success = false, error = $"No ParticleSystem found on '{gameObjectName}'" };

            // Modify viscosity through damping
            if (parameters["viscosity"] != null)
            {
                var collision = ps.collision;
                collision.dampen = parameters["viscosity"].Value<float>();
            }

            // Modify density through particle count
            if (parameters["density"] != null)
            {
                var main = ps.main;
                main.maxParticles = Mathf.RoundToInt(parameters["density"].Value<float>() * 1000);
            }

            return new { success = true, message = $"Fluid properties modified on '{gameObjectName}'" };
        }

        private static object AddFluidEmitter(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            ParticleSystem ps = go.GetComponent<ParticleSystem>();
            if (ps == null)
                return new { success = false, error = $"No ParticleSystem found on '{gameObjectName}'" };

            // Configure shape module for emission
            var shape = ps.shape;
            shape.enabled = true;
            
            string emitterType = parameters["emitter_type"]?.ToString() ?? "Box";
            shape.shapeType = emitterType switch
            {
                "Sphere" => ParticleSystemShapeType.Sphere,
                "Box" => ParticleSystemShapeType.Box,
                "Cone" => ParticleSystemShapeType.Cone,
                "Circle" => ParticleSystemShapeType.Circle,
                _ => ParticleSystemShapeType.Box
            };

            if (parameters["emitter_size"] != null)
            {
                var size = parameters["emitter_size"].ToObject<List<float>>();
                if (size.Count >= 3)
                    shape.scale = new Vector3(size[0], size[1], size[2]);
            }

            return new { success = true, message = $"Fluid emitter added to '{gameObjectName}'" };
        }

        private static object RemoveFluidSimulation(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            ParticleSystem ps = go.GetComponent<ParticleSystem>();
            if (ps != null)
                UnityEngine.Object.DestroyImmediate(ps);

            return new { success = true, message = $"Fluid simulation removed from '{gameObjectName}'" };
        }
        #endregion

        #region Destruction System
        private static object HandleCreateDestructionSystem(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                
                // Handle the main command structure - the action is already "create_destruction_system"
                // Check for sub-action or default to "setup_breakable"
                string subAction = parameters["subAction"]?.ToString() ?? "setup_breakable";
                
                return subAction switch
                {
                    "setup_breakable" => CreateDestructionSystem(parameters),
                    "trigger_destruction" => TriggerDestruction(parameters),
                    "cleanup" => AddBreakableComponent(parameters),
                    "remove" => RemoveDestructionSystem(parameters),
                    _ => CreateDestructionSystem(parameters) // Default to create for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleCreateDestructionSystem: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateDestructionSystem(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            // Add rigidbody for physics interaction
            Rigidbody rb = go.GetComponent<Rigidbody>();
            if (rb == null)
                rb = go.AddComponent<Rigidbody>();

            // Configure destruction properties
            float destructionForce = parameters["destruction_force"]?.Value<float>() ?? 100.0f;
            float health = parameters["health"]?.Value<float>() ?? 100.0f;
            
            // Create particle system for destruction effects
            GameObject destructionEffect = new GameObject($"{gameObjectName}_DestructionEffect");
            destructionEffect.transform.SetParent(go.transform);
            destructionEffect.transform.localPosition = Vector3.zero;
            
            ParticleSystem ps = destructionEffect.AddComponent<ParticleSystem>();
            var main = ps.main;
            main.startLifetime = 2.0f;
            main.startSpeed = 10.0f;
            main.maxParticles = 100;
            main.simulationSpace = ParticleSystemSimulationSpace.World;
            main.playOnAwake = false;

            // Setup emission burst for destruction
            var emission = ps.emission;
            emission.enabled = true;
            emission.rateOverTime = 0;
            emission.SetBursts(new ParticleSystem.Burst[] {
                new ParticleSystem.Burst(0.0f, 50)
            });

            // Add velocity over lifetime for explosion effect
            var velocityOverLifetime = ps.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.radial = new ParticleSystem.MinMaxCurve(5.0f);

            return new { success = true, message = $"Destruction system created on '{gameObjectName}'" };
        }

        private static object AddBreakableComponent(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            // Ensure collider exists for breakable detection
            Collider col = go.GetComponent<Collider>();
            if (col == null)
                col = go.AddComponent<BoxCollider>();

            // Set breakable threshold
            float breakThreshold = parameters["break_threshold"]?.Value<float>() ?? 50.0f;
            
            // Add tag for identification
            if (go.tag == "Untagged")
                go.tag = "Breakable";

            return new { success = true, message = $"Breakable component added to '{gameObjectName}'" };
        }

        private static object TriggerDestruction(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            // Find destruction effect
            Transform destructionEffect = go.transform.Find($"{gameObjectName}_DestructionEffect");
            if (destructionEffect != null)
            {
                ParticleSystem ps = destructionEffect.GetComponent<ParticleSystem>();
                if (ps != null)
                    ps.Play();
            }

            // Apply explosion force to nearby rigidbodies
            float explosionForce = parameters["explosion_force"]?.Value<float>() ?? 500.0f;
            float explosionRadius = parameters["explosion_radius"]?.Value<float>() ?? 5.0f;
            
            Collider[] colliders = Physics.OverlapSphere(go.transform.position, explosionRadius);
            foreach (Collider nearbyCollider in colliders)
            {
                Rigidbody nearbyRb = nearbyCollider.GetComponent<Rigidbody>();
                if (nearbyRb != null && nearbyRb != go.GetComponent<Rigidbody>())
                {
                    nearbyRb.AddExplosionForce(explosionForce, go.transform.position, explosionRadius);
                }
            }

            // Optionally destroy the object
            bool destroyObject = parameters["destroy_object"]?.Value<bool>() ?? false;
            if (destroyObject)
            {
                UnityEngine.Object.DestroyImmediate(go);
            }

            return new { success = true, message = $"Destruction triggered on '{gameObjectName}'" };
        }

        private static object RemoveDestructionSystem(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            // Remove destruction effect
            Transform destructionEffect = go.transform.Find($"{gameObjectName}_DestructionEffect");
            if (destructionEffect != null)
                UnityEngine.Object.DestroyImmediate(destructionEffect.gameObject);

            return new { success = true, message = $"Destruction system removed from '{gameObjectName}'" };
        }
        #endregion

        #region Vehicle Physics
        private static object HandleSetupVehiclePhysics(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                
                // Handle the main command structure - the action is already "setup_vehicle_physics"
                // Check for sub-action or default to "create_vehicle"
                string subAction = parameters["subAction"]?.ToString() ?? "create_vehicle";
                
                return subAction switch
                {
                    "create_vehicle" => CreateVehiclePhysics(parameters),
                    "setup_wheels" => ConfigureWheels(parameters),
                    "configure_engine" => SetEngineProperties(parameters),
                    "modify" => ApplyVehicleInput(parameters),
                    "remove" => RemoveVehiclePhysics(parameters),
                    _ => CreateVehiclePhysics(parameters) // Default to create for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleSetupVehiclePhysics: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateVehiclePhysics(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            // Add rigidbody for vehicle physics
            Rigidbody rb = go.GetComponent<Rigidbody>();
            if (rb == null)
                rb = go.AddComponent<Rigidbody>();

            // Configure rigidbody for vehicle
            rb.mass = parameters["mass"]?.Value<float>() ?? 1500.0f;
            rb.centerOfMass = new Vector3(0, -0.5f, 0); // Lower center of mass for stability
            rb.linearDamping = 0.3f;
            rb.angularDamping = 3.0f;

            // Create wheel colliders
            string[] wheelNames = { "FrontLeft", "FrontRight", "RearLeft", "RearRight" };
            Vector3[] wheelPositions = {
                new Vector3(-1, 0, 1),   // Front Left
                new Vector3(1, 0, 1),    // Front Right
                new Vector3(-1, 0, -1),  // Rear Left
                new Vector3(1, 0, -1)    // Rear Right
            };

            for (int i = 0; i < wheelNames.Length; i++)
            {
                GameObject wheel = new GameObject($"Wheel_{wheelNames[i]}");
                wheel.transform.SetParent(go.transform);
                wheel.transform.localPosition = wheelPositions[i];
                
                WheelCollider wc = wheel.AddComponent<WheelCollider>();
                wc.radius = parameters["wheel_radius"]?.Value<float>() ?? 0.3f;
                wc.wheelDampingRate = 25.0f;
                wc.suspensionDistance = 0.3f;
                
                // Configure suspension
                JointSpring suspensionSpring = wc.suspensionSpring;
                suspensionSpring.spring = 35000.0f;
                suspensionSpring.damper = 4500.0f;
                suspensionSpring.targetPosition = 0.5f;
                wc.suspensionSpring = suspensionSpring;
                
                // Configure friction
                WheelFrictionCurve forwardFriction = wc.forwardFriction;
                forwardFriction.extremumSlip = 0.4f;
                forwardFriction.extremumValue = 1.0f;
                forwardFriction.asymptoteSlip = 0.8f;
                forwardFriction.asymptoteValue = 0.5f;
                forwardFriction.stiffness = 1.0f;
                wc.forwardFriction = forwardFriction;
                
                WheelFrictionCurve sidewaysFriction = wc.sidewaysFriction;
                sidewaysFriction.extremumSlip = 0.2f;
                sidewaysFriction.extremumValue = 1.0f;
                sidewaysFriction.asymptoteSlip = 0.5f;
                sidewaysFriction.asymptoteValue = 0.75f;
                sidewaysFriction.stiffness = 1.0f;
                wc.sidewaysFriction = sidewaysFriction;
            }

            return new { success = true, message = $"Vehicle physics created on '{gameObjectName}'" };
        }

        private static object ConfigureWheels(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            WheelCollider[] wheels = go.GetComponentsInChildren<WheelCollider>();
            if (wheels.Length == 0)
                return new { success = false, error = "No wheel colliders found" };

            foreach (WheelCollider wheel in wheels)
            {
                if (parameters["suspension_spring"] != null)
                {
                    JointSpring spring = wheel.suspensionSpring;
                    spring.spring = parameters["suspension_spring"].Value<float>();
                    wheel.suspensionSpring = spring;
                }
                
                if (parameters["suspension_damper"] != null)
                {
                    JointSpring spring = wheel.suspensionSpring;
                    spring.damper = parameters["suspension_damper"].Value<float>();
                    wheel.suspensionSpring = spring;
                }
                
                if (parameters["wheel_radius"] != null)
                {
                    wheel.radius = parameters["wheel_radius"].Value<float>();
                }
            }

            return new { success = true, message = $"Wheels configured on '{gameObjectName}'" };
        }

        private static object SetEngineProperties(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            // Store engine properties as component data (simplified approach)
            // In a real implementation, you'd create a custom VehicleController component
            float maxTorque = parameters["max_torque"]?.Value<float>() ?? 1500.0f;
            float maxSteerAngle = parameters["max_steer_angle"]?.Value<float>() ?? 30.0f;
            float brakeForce = parameters["brake_force"]?.Value<float>() ?? 3000.0f;

            return new { success = true, message = $"Engine properties set on '{gameObjectName}'", 
                        maxTorque = maxTorque, maxSteerAngle = maxSteerAngle, brakeForce = brakeForce };
        }

        private static object ApplyVehicleInput(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            WheelCollider[] wheels = go.GetComponentsInChildren<WheelCollider>();
            if (wheels.Length < 4)
                return new { success = false, error = "Vehicle needs at least 4 wheels" };

            float motor = parameters["motor_input"]?.Value<float>() ?? 0.0f;
            float steering = parameters["steering_input"]?.Value<float>() ?? 0.0f;
            float brake = parameters["brake_input"]?.Value<float>() ?? 0.0f;

            float maxTorque = 1500.0f;
            float maxSteerAngle = 30.0f;
            float brakeForce = 3000.0f;

            // Apply motor torque to rear wheels
            wheels[2].motorTorque = motor * maxTorque; // Rear Left
            wheels[3].motorTorque = motor * maxTorque; // Rear Right

            // Apply steering to front wheels
            wheels[0].steerAngle = steering * maxSteerAngle; // Front Left
            wheels[1].steerAngle = steering * maxSteerAngle; // Front Right

            // Apply brakes to all wheels
            foreach (WheelCollider wheel in wheels)
            {
                wheel.brakeTorque = brake * brakeForce;
            }

            return new { success = true, message = $"Vehicle input applied to '{gameObjectName}'" };
        }

        private static object RemoveVehiclePhysics(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            // Remove all wheel colliders
            WheelCollider[] wheels = go.GetComponentsInChildren<WheelCollider>();
            foreach (WheelCollider wheel in wheels)
            {
                if (wheel.transform.parent == go.transform)
                    UnityEngine.Object.DestroyImmediate(wheel.gameObject);
            }

            return new { success = true, message = $"Vehicle physics removed from '{gameObjectName}'" };
        }
        #endregion

        #region Rope Physics
        private static object HandleCreateRopePhysics(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                
                // Handle the main command structure - the action is already "create_rope_physics"
                // Check for sub-action or default to "create_rope"
                string subAction = parameters["subAction"]?.ToString() ?? "create_rope";
                
                return subAction switch
                {
                    "create_rope" => CreateRopePhysics(parameters),
                    "attach_objects" => AttachObjectsToRope(parameters),
                    "set_constraints" => SetRopeProperties(parameters),
                    "modify" => CutRope(parameters),
                    "remove" => RemoveRopePhysics(parameters),
                    _ => CreateRopePhysics(parameters) // Default to create for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleCreateRopePhysics: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateRopePhysics(JObject parameters)
        {
            string ropeName = parameters["rope_name"]?.ToString() ?? "Rope";
            int segments = parameters["segments"]?.Value<int>() ?? 10;
            float segmentLength = parameters["segment_length"]?.Value<float>() ?? 1.0f;
            
            Vector3 startPos = Vector3.zero;
            if (parameters["start_position"] != null)
            {
                var pos = parameters["start_position"].ToObject<List<float>>();
                if (pos.Count >= 3)
                    startPos = new Vector3(pos[0], pos[1], pos[2]);
            }

            // Create rope parent object
            GameObject ropeParent = new GameObject(ropeName);
            ropeParent.transform.position = startPos;
            
            // Create LineRenderer for visual representation
            LineRenderer lineRenderer = ropeParent.AddComponent<LineRenderer>();
            lineRenderer.startColor = Color.white;
            lineRenderer.endColor = Color.white;
            lineRenderer.startWidth = 0.1f;
            lineRenderer.endWidth = 0.1f;
            lineRenderer.positionCount = segments + 1;
            lineRenderer.useWorldSpace = true;

            // Create rope segments
            GameObject[] ropeSegments = new GameObject[segments + 1];
            Rigidbody previousRb = null;

            for (int i = 0; i <= segments; i++)
            {
                GameObject segment = new GameObject($"RopeSegment_{i}");
                segment.transform.SetParent(ropeParent.transform);
                segment.transform.position = startPos + Vector3.down * (i * segmentLength);
                
                // Add rigidbody to each segment
                Rigidbody rb = segment.AddComponent<Rigidbody>();
                rb.mass = parameters["segment_mass"]?.Value<float>() ?? 0.1f;
                rb.linearDamping = 1.0f;
                rb.angularDamping = 1.0f;
                
                // Add collider
                SphereCollider col = segment.AddComponent<SphereCollider>();
                col.radius = 0.05f;
                
                ropeSegments[i] = segment;
                
                // Connect segments with joints
                if (i > 0 && previousRb != null)
                {
                    SpringJoint joint = segment.AddComponent<SpringJoint>();
                    joint.connectedBody = previousRb;
                    joint.autoConfigureConnectedAnchor = false;
                    joint.anchor = Vector3.zero;
                    joint.connectedAnchor = Vector3.zero;
                    joint.spring = parameters["spring_force"]?.Value<float>() ?? 50.0f;
                    joint.damper = parameters["damper"]?.Value<float>() ?? 5.0f;
                    joint.minDistance = 0;
                    joint.maxDistance = segmentLength;
                }
                
                previousRb = rb;
            }
            
            // Fix first segment if specified
            bool fixFirstSegment = parameters["fix_first_segment"]?.Value<bool>() ?? true;
            if (fixFirstSegment && ropeSegments.Length > 0)
            {
                Rigidbody firstRb = ropeSegments[0].GetComponent<Rigidbody>();
                firstRb.isKinematic = true;
            }

            return new { success = true, message = $"Rope physics '{ropeName}' created with {segments} segments" };
        }

        private static object AttachObjectsToRope(JObject parameters)
        {
            string ropeName = parameters["rope_name"]?.ToString();
            string objectName = parameters["object_name"]?.ToString();
            int attachmentPoint = parameters["attachment_point"]?.Value<int>() ?? -1; // -1 for last segment
            
            if (string.IsNullOrEmpty(ropeName) || string.IsNullOrEmpty(objectName))
                return new { success = false, error = "Rope name and object name are required" };

            GameObject rope = GameObject.Find(ropeName);
            GameObject obj = GameObject.Find(objectName);
            
            if (rope == null)
                return new { success = false, error = $"Rope '{ropeName}' not found" };
            if (obj == null)
                return new { success = false, error = $"Object '{objectName}' not found" };

            // Find rope segments
            Transform[] segments = rope.GetComponentsInChildren<Transform>();
            Transform targetSegment = null;
            
            if (attachmentPoint == -1)
            {
                // Attach to last segment
                targetSegment = segments[segments.Length - 1];
            }
            else if (attachmentPoint < segments.Length)
            {
                targetSegment = segments[attachmentPoint];
            }
            
            if (targetSegment == null)
                return new { success = false, error = "Invalid attachment point" };

            // Ensure object has rigidbody
            Rigidbody objRb = obj.GetComponent<Rigidbody>();
            if (objRb == null)
                objRb = obj.AddComponent<Rigidbody>();

            // Create joint connection
            SpringJoint joint = obj.AddComponent<SpringJoint>();
            joint.connectedBody = targetSegment.GetComponent<Rigidbody>();
            joint.spring = 50.0f;
            joint.damper = 5.0f;
            joint.maxDistance = 0.5f;

            return new { success = true, message = $"Object '{objectName}' attached to rope '{ropeName}'" };
        }

        private static object SetRopeProperties(JObject parameters)
        {
            string ropeName = parameters["rope_name"]?.ToString();
            if (string.IsNullOrEmpty(ropeName))
                return new { success = false, error = "Rope name is required" };

            GameObject rope = GameObject.Find(ropeName);
            if (rope == null)
                return new { success = false, error = $"Rope '{ropeName}' not found" };

            // Update spring joints
            SpringJoint[] joints = rope.GetComponentsInChildren<SpringJoint>();
            foreach (SpringJoint joint in joints)
            {
                if (parameters["spring_force"] != null)
                    joint.spring = parameters["spring_force"].Value<float>();
                if (parameters["damper"] != null)
                    joint.damper = parameters["damper"].Value<float>();
            }

            // Update line renderer
            LineRenderer lr = rope.GetComponent<LineRenderer>();
            if (lr != null)
            {
                if (parameters["rope_width"] != null)
                {
                    float width = parameters["rope_width"].Value<float>();
                    lr.startWidth = width;
                    lr.endWidth = width;
                }
            }

            return new { success = true, message = $"Rope properties updated for '{ropeName}'" };
        }

        private static object CutRope(JObject parameters)
        {
            string ropeName = parameters["rope_name"]?.ToString();
            int cutPoint = parameters["cut_point"]?.Value<int>() ?? 0;
            
            if (string.IsNullOrEmpty(ropeName))
                return new { success = false, error = "Rope name is required" };

            GameObject rope = GameObject.Find(ropeName);
            if (rope == null)
                return new { success = false, error = $"Rope '{ropeName}' not found" };

            Transform[] segments = rope.GetComponentsInChildren<Transform>();
            if (cutPoint >= 0 && cutPoint < segments.Length)
            {
                Transform cutSegment = segments[cutPoint];
                SpringJoint joint = cutSegment.GetComponent<SpringJoint>();
                if (joint != null)
                    UnityEngine.Object.DestroyImmediate(joint);
            }

            return new { success = true, message = $"Rope '{ropeName}' cut at point {cutPoint}" };
        }

        private static object RemoveRopePhysics(JObject parameters)
        {
            string ropeName = parameters["rope_name"]?.ToString();
            if (string.IsNullOrEmpty(ropeName))
                return new { success = false, error = "Rope name is required" };

            GameObject rope = GameObject.Find(ropeName);
            if (rope == null)
                return new { success = false, error = $"Rope '{ropeName}' not found" };

            UnityEngine.Object.DestroyImmediate(rope);
            return new { success = true, message = $"Rope '{ropeName}' removed" };
        }
        #endregion

        #region Wind System
        private static object HandleSetupWindSystem(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                
                // Handle the main command structure - the action is already "setup_wind_system"
                // Check for sub-action or default to "create_wind_zone"
                string subAction = parameters["subAction"]?.ToString() ?? "create_wind_zone";
                
                return subAction switch
                {
                    "create_wind_zone" => CreateWindSystem(parameters),
                    "set_parameters" => ModifyWindProperties(parameters),
                    "apply_forces" => AddWindZone(parameters),
                    "modify" => ApplyWindToObjects(parameters),
                    "remove" => RemoveWindSystem(parameters),
                    _ => CreateWindSystem(parameters) // Default to create for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleSetupWindSystem: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateWindSystem(JObject parameters)
        {
            string windName = parameters["wind_name"]?.ToString() ?? "WindSystem";
            
            Vector3 position = Vector3.zero;
            if (parameters["position"] != null)
            {
                var pos = parameters["position"].ToObject<List<float>>();
                if (pos.Count >= 3)
                    position = new Vector3(pos[0], pos[1], pos[2]);
            }

            // Create wind system parent object
            GameObject windSystem = new GameObject(windName);
            windSystem.transform.position = position;
            
            // Add WindZone component
            WindZone windZone = windSystem.AddComponent<WindZone>();
            
            // Configure wind zone properties
            string windMode = parameters["wind_mode"]?.ToString() ?? "Directional";
            windZone.mode = windMode switch
            {
                "Spherical" => WindZoneMode.Spherical,
                "Directional" => WindZoneMode.Directional,
                _ => WindZoneMode.Directional
            };
            
            windZone.windMain = parameters["wind_strength"]?.Value<float>() ?? 1.0f;
            windZone.windTurbulence = parameters["turbulence"]?.Value<float>() ?? 0.1f;
            windZone.windPulseMagnitude = parameters["pulse_magnitude"]?.Value<float>() ?? 0.5f;
            windZone.windPulseFrequency = parameters["pulse_frequency"]?.Value<float>() ?? 0.1f;
            
            if (windZone.mode == WindZoneMode.Spherical)
            {
                windZone.radius = parameters["radius"]?.Value<float>() ?? 10.0f;
            }
            
            // Set wind direction
            if (parameters["wind_direction"] != null)
            {
                var dir = parameters["wind_direction"].ToObject<List<float>>();
                if (dir.Count >= 3)
                {
                    Vector3 windDirection = new Vector3(dir[0], dir[1], dir[2]).normalized;
                    windSystem.transform.rotation = Quaternion.LookRotation(windDirection);
                }
            }

            return new { success = true, message = $"Wind system '{windName}' created" };
        }

        private static object ModifyWindProperties(JObject parameters)
        {
            string windName = parameters["wind_name"]?.ToString();
            if (string.IsNullOrEmpty(windName))
                return new { success = false, error = "Wind name is required" };

            GameObject windSystem = GameObject.Find(windName);
            if (windSystem == null)
                return new { success = false, error = $"Wind system '{windName}' not found" };

            WindZone windZone = windSystem.GetComponent<WindZone>();
            if (windZone == null)
                return new { success = false, error = $"No WindZone found on '{windName}'" };

            // Update wind properties
            if (parameters["wind_strength"] != null)
                windZone.windMain = parameters["wind_strength"].Value<float>();
            if (parameters["turbulence"] != null)
                windZone.windTurbulence = parameters["turbulence"].Value<float>();
            if (parameters["pulse_magnitude"] != null)
                windZone.windPulseMagnitude = parameters["pulse_magnitude"].Value<float>();
            if (parameters["pulse_frequency"] != null)
                windZone.windPulseFrequency = parameters["pulse_frequency"].Value<float>();
            if (parameters["radius"] != null && windZone.mode == WindZoneMode.Spherical)
                windZone.radius = parameters["radius"].Value<float>();

            return new { success = true, message = $"Wind properties updated for '{windName}'" };
        }

        private static object AddWindZone(JObject parameters)
        {
            string gameObjectName = parameters["gameObject"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
                return new { success = false, error = "GameObject name is required" };

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
                return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

            // Add WindZone if it doesn't exist
            WindZone windZone = go.GetComponent<WindZone>();
            if (windZone == null)
                windZone = go.AddComponent<WindZone>();

            // Configure the wind zone
            windZone.mode = WindZoneMode.Spherical;
            windZone.radius = parameters["radius"]?.Value<float>() ?? 5.0f;
            windZone.windMain = parameters["wind_strength"]?.Value<float>() ?? 1.0f;
            windZone.windTurbulence = parameters["turbulence"]?.Value<float>() ?? 0.1f;

            return new { success = true, message = $"Wind zone added to '{gameObjectName}'" };
        }

        private static object ApplyWindToObjects(JObject parameters)
        {
            string windName = parameters["wind_name"]?.ToString();
            if (string.IsNullOrEmpty(windName))
                return new { success = false, error = "Wind name is required" };

            GameObject windSystem = GameObject.Find(windName);
            if (windSystem == null)
                return new { success = false, error = $"Wind system '{windName}' not found" };

            WindZone windZone = windSystem.GetComponent<WindZone>();
            if (windZone == null)
                return new { success = false, error = $"No WindZone found on '{windName}'" };

            // Get objects to apply wind to
            var objectNames = parameters["object_names"]?.ToObject<List<string>>();
            if (objectNames == null || objectNames.Count == 0)
                return new { success = false, error = "Object names list is required" };

            int affectedObjects = 0;
            foreach (string objectName in objectNames)
            {
                GameObject obj = GameObject.Find(objectName);
                if (obj != null)
                {
                    // Add ConstantForce component to create real wind effect
                    ConstantForce constantForce = obj.GetComponent<ConstantForce>();
                    if (constantForce == null)
                        constantForce = obj.AddComponent<ConstantForce>();

                    // Calculate wind force based on wind zone
                    Vector3 windDirection = windSystem.transform.forward;
                    float windStrength = windZone.windMain;
                    
                    constantForce.force = windDirection * windStrength;
                    constantForce.relativeTorque = Vector3.zero;
                    
                    affectedObjects++;
                }
            }

            return new { success = true, message = $"Wind applied to {affectedObjects} objects" };
        }

        private static object RemoveWindSystem(JObject parameters)
        {
            string windName = parameters["wind_name"]?.ToString();
            if (string.IsNullOrEmpty(windName))
                return new { success = false, error = "Wind name is required" };

            GameObject windSystem = GameObject.Find(windName);
            if (windSystem == null)
                return new { success = false, error = $"Wind system '{windName}' not found" };

            // Remove wind effects from objects
            var objectNames = parameters["affected_objects"]?.ToObject<List<string>>();
            if (objectNames != null)
            {
                foreach (string objectName in objectNames)
                {
                    GameObject obj = GameObject.Find(objectName);
                    if (obj != null)
                    {
                        ConstantForce constantForce = obj.GetComponent<ConstantForce>();
                        if (constantForce != null)
                            UnityEngine.Object.DestroyImmediate(constantForce);
                    }
                }
            }

            UnityEngine.Object.DestroyImmediate(windSystem);
            return new { success = true, message = $"Wind system '{windName}' removed" };
        }
        #endregion

        #region Gravity Zones
        private static object HandleCreateGravityZones(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                
                // Handle the main command structure - the action is already "create_gravity_zones"
                // Check for sub-action or default to "create_zone"
                string subAction = parameters["subAction"]?.ToString() ?? "create_zone";
                
                return subAction switch
                {
                    "create_zone" => CreateGravityZone(parameters),
                    "modify_gravity" => ModifyGravityZone(parameters),
                    "set_boundaries" => AddAffectedObject(parameters),
                    "remove" => RemoveGravityZone(parameters),
                    _ => CreateGravityZone(parameters) // Default to create for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleCreateGravityZones: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateGravityZone(JObject parameters)
        {
            string zoneName = parameters["zone_name"]?.ToString() ?? "GravityZone";
            
            Vector3 position = Vector3.zero;
            if (parameters["position"] != null)
            {
                var pos = parameters["position"].ToObject<List<float>>();
                if (pos.Count >= 3)
                    position = new Vector3(pos[0], pos[1], pos[2]);
            }

            Vector3 size = Vector3.one * 5.0f;
            if (parameters["size"] != null)
            {
                var sizeList = parameters["size"].ToObject<List<float>>();
                if (sizeList.Count >= 3)
                    size = new Vector3(sizeList[0], sizeList[1], sizeList[2]);
            }

            // Ensure GravityZone tag exists
            EnsureTagExists("GravityZone");
            
            // Create gravity zone object
            GameObject gravityZone = new GameObject(zoneName);
            gravityZone.transform.position = position;
            gravityZone.tag = "GravityZone";
            
            // Add collider as trigger
            string shapeType = parameters["shape"]?.ToString() ?? "Box";
            Collider zoneCollider;
            
            switch (shapeType.ToLower())
            {
                case "sphere":
                    SphereCollider sphereCol = gravityZone.AddComponent<SphereCollider>();
                    sphereCol.radius = size.x;
                    sphereCol.isTrigger = true;
                    zoneCollider = sphereCol;
                    break;
                case "capsule":
                    CapsuleCollider capsuleCol = gravityZone.AddComponent<CapsuleCollider>();
                    capsuleCol.radius = size.x;
                    capsuleCol.height = size.y;
                    capsuleCol.isTrigger = true;
                    zoneCollider = capsuleCol;
                    break;
                default: // Box
                    BoxCollider boxCol = gravityZone.AddComponent<BoxCollider>();
                    boxCol.size = size;
                    boxCol.isTrigger = true;
                    zoneCollider = boxCol;
                    break;
            }

            // Store gravity properties
            Vector3 gravityDirection = Vector3.down;
            if (parameters["gravity_direction"] != null)
            {
                var dir = parameters["gravity_direction"].ToObject<List<float>>();
                if (dir.Count >= 3)
                    gravityDirection = new Vector3(dir[0], dir[1], dir[2]).normalized;
            }

            float gravityStrength = parameters["gravity_strength"]?.Value<float>() ?? 9.81f;
            
            // Add a simple script component to store gravity data (using a basic approach)
            // In a real implementation, you'd create a custom GravityZone component
            
            return new { success = true, message = $"Gravity zone '{zoneName}' created", 
                        gravityDirection = new { x = gravityDirection.x, y = gravityDirection.y, z = gravityDirection.z },
                        gravityStrength = gravityStrength };
        }

        private static object ModifyGravityZone(JObject parameters)
        {
            string zoneName = parameters["zone_name"]?.ToString();
            if (string.IsNullOrEmpty(zoneName))
                return new { success = false, error = "Zone name is required" };

            GameObject gravityZone = GameObject.Find(zoneName);
            if (gravityZone == null)
                return new { success = false, error = $"Gravity zone '{zoneName}' not found" };

            // Update gravity strength for objects in zone
            if (parameters["gravity_strength"] != null)
            {
                float newStrength = parameters["gravity_strength"].Value<float>();
                
                // Find all objects with ConstantForce in the zone
                Collider zoneCollider = gravityZone.GetComponent<Collider>();
                if (zoneCollider != null)
                {
                    Bounds zoneBounds = zoneCollider.bounds;
                    ConstantForce[] allForces = UnityEngine.Object.FindObjectsByType<ConstantForce>(FindObjectsSortMode.None);
                    
                    foreach (ConstantForce force in allForces)
                    {
                        if (zoneBounds.Contains(force.transform.position))
                        {
                            Vector3 currentDirection = force.force.normalized;
                            force.force = currentDirection * newStrength;
                        }
                    }
                }
            }

            // Update zone size
            if (parameters["size"] != null)
            {
                var sizeList = parameters["size"].ToObject<List<float>>();
                if (sizeList.Count >= 3)
                {
                    Vector3 newSize = new Vector3(sizeList[0], sizeList[1], sizeList[2]);
                    
                    BoxCollider boxCol = gravityZone.GetComponent<BoxCollider>();
                    if (boxCol != null)
                        boxCol.size = newSize;
                    
                    SphereCollider sphereCol = gravityZone.GetComponent<SphereCollider>();
                    if (sphereCol != null)
                        sphereCol.radius = newSize.x;
                    
                    CapsuleCollider capsuleCol = gravityZone.GetComponent<CapsuleCollider>();
                    if (capsuleCol != null)
                    {
                        capsuleCol.radius = newSize.x;
                        capsuleCol.height = newSize.y;
                    }
                }
            }

            return new { success = true, message = $"Gravity zone '{zoneName}' modified" };
        }

        private static object AddAffectedObject(JObject parameters)
        {
            string zoneName = parameters["zone_name"]?.ToString();
            string objectName = parameters["object_name"]?.ToString();
            
            if (string.IsNullOrEmpty(zoneName) || string.IsNullOrEmpty(objectName))
                return new { success = false, error = "Zone name and object name are required" };

            GameObject gravityZone = GameObject.Find(zoneName);
            GameObject targetObject = GameObject.Find(objectName);
            
            if (gravityZone == null)
                return new { success = false, error = $"Gravity zone '{zoneName}' not found" };
            if (targetObject == null)
                return new { success = false, error = $"Object '{objectName}' not found" };

            // Ensure object has rigidbody
            Rigidbody rb = targetObject.GetComponent<Rigidbody>();
            if (rb == null)
                rb = targetObject.AddComponent<Rigidbody>();

            // Add ConstantForce for custom gravity
            ConstantForce constantForce = targetObject.GetComponent<ConstantForce>();
            if (constantForce == null)
                constantForce = targetObject.AddComponent<ConstantForce>();

            // Set gravity force
            Vector3 gravityDirection = Vector3.down;
            if (parameters["gravity_direction"] != null)
            {
                var dir = parameters["gravity_direction"].ToObject<List<float>>();
                if (dir.Count >= 3)
                    gravityDirection = new Vector3(dir[0], dir[1], dir[2]).normalized;
            }

            float gravityStrength = parameters["gravity_strength"]?.Value<float>() ?? 9.81f;
            constantForce.force = gravityDirection * gravityStrength * rb.mass;

            // Disable default gravity if using custom gravity
            rb.useGravity = false;

            return new { success = true, message = $"Object '{objectName}' added to gravity zone '{zoneName}'" };
        }

        private static object RemoveAffectedObject(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            if (string.IsNullOrEmpty(objectName))
                return new { success = false, error = "Object name is required" };

            GameObject targetObject = GameObject.Find(objectName);
            if (targetObject == null)
                return new { success = false, error = $"Object '{objectName}' not found" };

            // Remove ConstantForce component
            ConstantForce constantForce = targetObject.GetComponent<ConstantForce>();
            if (constantForce != null)
                UnityEngine.Object.DestroyImmediate(constantForce);

            // Re-enable default gravity
            Rigidbody rb = targetObject.GetComponent<Rigidbody>();
            if (rb != null)
                rb.useGravity = true;

            return new { success = true, message = $"Object '{objectName}' removed from gravity zone effects" };
        }

        private static object RemoveGravityZone(JObject parameters)
        {
            string zoneName = parameters["zone_name"]?.ToString();
            if (string.IsNullOrEmpty(zoneName))
                return new { success = false, error = "Zone name is required" };

            GameObject gravityZone = GameObject.Find(zoneName);
            if (gravityZone == null)
                return new { success = false, error = $"Gravity zone '{zoneName}' not found" };

            // Remove gravity effects from all affected objects
            var affectedObjects = parameters["affected_objects"]?.ToObject<List<string>>();
            if (affectedObjects != null)
            {
                foreach (string objectName in affectedObjects)
                {
                    GameObject obj = GameObject.Find(objectName);
                    if (obj != null)
                    {
                        ConstantForce constantForce = obj.GetComponent<ConstantForce>();
                        if (constantForce != null)
                            UnityEngine.Object.DestroyImmediate(constantForce);
                        
                        Rigidbody rb = obj.GetComponent<Rigidbody>();
                        if (rb != null)
                            rb.useGravity = true;
                    }
                }
            }

            UnityEngine.Object.DestroyImmediate(gravityZone);
            return new { success = true, message = $"Gravity zone '{zoneName}' removed" };
        }
        #endregion

        #region Collision Layers
        private static object HandleSetupCollisionLayers(JObject parameters)
        {
            try
            {
                string subAction = parameters["action"]?.ToString() ?? "create_layers";
                
                return subAction switch
                {
                    "create_layers" => CreateCollisionLayer(parameters),
                    "set_interactions" => SetLayerCollision(parameters),
                    "modify_matrix" => IgnoreLayerCollision(parameters),
                    "get_matrix" => GetCollisionMatrix(parameters),
                    _ => CreateCollisionLayer(parameters) // Default for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleSetupCollisionLayers: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateCollisionLayer(JObject parameters)
        {
            // Handle both single layer and multiple layers
            var layerNames = parameters["layer_names"]?.ToObject<string[]>();
            string singleLayerName = parameters["layer_name"]?.ToString();
            
            if (layerNames == null && string.IsNullOrEmpty(singleLayerName))
                return new { success = false, error = "Layer name or layer names are required" };
            
            // Convert single layer to array for unified processing
            if (layerNames == null && !string.IsNullOrEmpty(singleLayerName))
                layerNames = new string[] { singleLayerName };
            
            var results = new List<object>();
            var createdLayers = new List<object>();
            
            try
            {
                // Load TagManager asset
                var tagManagerAssets = AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset");
                if (tagManagerAssets == null || tagManagerAssets.Length == 0)
                    return new { success = false, error = "Could not access TagManager asset" };
                
                var tagManager = tagManagerAssets[0];
                var serializedObject = new SerializedObject(tagManager);
                var layersProperty = serializedObject.FindProperty("layers");
                
                foreach (string layerName in layerNames)
                {
                    if (string.IsNullOrEmpty(layerName))
                    {
                        results.Add(new { layerName = "(empty)", success = false, error = "Layer name cannot be empty" });
                        continue;
                    }
                    
                    // Check if layer already exists
                    int existingLayer = LayerMask.NameToLayer(layerName);
                    if (existingLayer != -1)
                    {
                        results.Add(new { layerName = layerName, success = false, error = $"Layer '{layerName}' already exists at index {existingLayer}" });
                        continue;
                    }
                    
                    // Find next available layer slot (layers 8-31 are user-defined)
                    int availableLayer = -1;
                    for (int i = 8; i < 32; i++)
                    {
                        var layerProperty = layersProperty.GetArrayElementAtIndex(i);
                        if (string.IsNullOrEmpty(layerProperty.stringValue))
                        {
                            availableLayer = i;
                            break;
                        }
                    }
                    
                    if (availableLayer == -1)
                    {
                        results.Add(new { layerName = layerName, success = false, error = "No available layer slots (layers 8-31 are full)" });
                        continue;
                    }
                    
                    // Create the layer
                    var targetLayerProperty = layersProperty.GetArrayElementAtIndex(availableLayer);
                    targetLayerProperty.stringValue = layerName;
                    
                    results.Add(new { layerName = layerName, success = true, layerIndex = availableLayer });
                    createdLayers.Add(new { name = layerName, index = availableLayer });
                }
                
                // Apply changes
                serializedObject.ApplyModifiedProperties();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                
                bool allSuccessful = results.All(r => ((dynamic)r).success);
                string message = allSuccessful ? 
                    $"Successfully created {createdLayers.Count} layer(s)" : 
                    $"Created {createdLayers.Count} layer(s) with some failures";
                
                return new { 
                    success = allSuccessful, 
                    message = message,
                    createdLayers = createdLayers,
                    results = results
                };
            }
            catch (Exception ex)
            {
                return new { success = false, error = $"Error creating layers: {ex.Message}" };
            }
        }

        private static object SetLayerCollision(JObject parameters)
        {
            string layer1Name = parameters["layer1"]?.ToString();
            string layer2Name = parameters["layer2"]?.ToString();
            bool shouldCollide = parameters["should_collide"]?.Value<bool>() ?? true;

            if (string.IsNullOrEmpty(layer1Name) || string.IsNullOrEmpty(layer2Name))
                return new { success = false, error = "Both layer names are required" };

            int layer1 = LayerMask.NameToLayer(layer1Name);
            int layer2 = LayerMask.NameToLayer(layer2Name);

            if (layer1 == -1)
                return new { success = false, error = $"Layer '{layer1Name}' not found" };
            if (layer2 == -1)
                return new { success = false, error = $"Layer '{layer2Name}' not found" };

            // Set collision interaction between layers
            Physics.IgnoreLayerCollision(layer1, layer2, !shouldCollide);

            string action = shouldCollide ? "enabled" : "disabled";
            return new { success = true, message = $"Collision {action} between layers '{layer1Name}' and '{layer2Name}'" };
        }

        private static object IgnoreLayerCollision(JObject parameters)
        {
            string layer1Name = parameters["layer1"]?.ToString();
            string layer2Name = parameters["layer2"]?.ToString();

            if (string.IsNullOrEmpty(layer1Name) || string.IsNullOrEmpty(layer2Name))
                return new { success = false, error = "Both layer names are required" };

            int layer1 = LayerMask.NameToLayer(layer1Name);
            int layer2 = LayerMask.NameToLayer(layer2Name);

            if (layer1 == -1)
                return new { success = false, error = $"Layer '{layer1Name}' not found" };
            if (layer2 == -1)
                return new { success = false, error = $"Layer '{layer2Name}' not found" };

            Physics.IgnoreLayerCollision(layer1, layer2, true);
            return new { success = true, message = $"Collision ignored between layers '{layer1Name}' and '{layer2Name}'" };
        }

        private static object EnableLayerCollision(JObject parameters)
        {
            string layer1Name = parameters["layer1"]?.ToString();
            string layer2Name = parameters["layer2"]?.ToString();

            if (string.IsNullOrEmpty(layer1Name) || string.IsNullOrEmpty(layer2Name))
                return new { success = false, error = "Both layer names are required" };

            int layer1 = LayerMask.NameToLayer(layer1Name);
            int layer2 = LayerMask.NameToLayer(layer2Name);

            if (layer1 == -1)
                return new { success = false, error = $"Layer '{layer1Name}' not found" };
            if (layer2 == -1)
                return new { success = false, error = $"Layer '{layer2Name}' not found" };

            Physics.IgnoreLayerCollision(layer1, layer2, false);
            return new { success = true, message = $"Collision enabled between layers '{layer1Name}' and '{layer2Name}'" };
        }

        private static object GetCollisionMatrix(JObject parameters)
        {
            var collisionMatrix = new List<object>();
            
            // Get all defined layers (0-31)
            var definedLayers = new List<object>();
            for (int i = 0; i < 32; i++)
            {
                string layerName = LayerMask.LayerToName(i);
                if (!string.IsNullOrEmpty(layerName))
                {
                    definedLayers.Add(new { index = i, name = layerName });
                }
            }

            // Check collision status between all defined layers
            for (int i = 0; i < 32; i++)
            {
                string layer1Name = LayerMask.LayerToName(i);
                if (string.IsNullOrEmpty(layer1Name)) continue;

                var layerCollisions = new List<object>();
                for (int j = 0; j < 32; j++)
                {
                    string layer2Name = LayerMask.LayerToName(j);
                    if (string.IsNullOrEmpty(layer2Name)) continue;

                    bool canCollide = !Physics.GetIgnoreLayerCollision(i, j);
                    layerCollisions.Add(new { 
                        layer = layer2Name, 
                        layerIndex = j, 
                        canCollide = canCollide 
                    });
                }

                collisionMatrix.Add(new {
                    layer = layer1Name,
                    layerIndex = i,
                    collisions = layerCollisions
                });
            }

            return new { success = true, 
                        definedLayers = definedLayers,
                        collisionMatrix = collisionMatrix };
        }

        private static object AssignObjectToLayer(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            string layerName = parameters["layer_name"]?.ToString();
            bool includeChildren = parameters["include_children"]?.Value<bool>() ?? false;

            if (string.IsNullOrEmpty(objectName) || string.IsNullOrEmpty(layerName))
                return new { success = false, error = "Object name and layer name are required" };

            GameObject targetObject = GameObject.Find(objectName);
            if (targetObject == null)
                return new { success = false, error = $"Object '{objectName}' not found" };

            int layerIndex = LayerMask.NameToLayer(layerName);
            if (layerIndex == -1)
                return new { success = false, error = $"Layer '{layerName}' not found" };

            // Assign layer to object
            targetObject.layer = layerIndex;

            int objectsChanged = 1;
            
            // Optionally assign to all children
            if (includeChildren)
            {
                Transform[] children = targetObject.GetComponentsInChildren<Transform>();
                foreach (Transform child in children)
                {
                    if (child != targetObject.transform)
                    {
                        child.gameObject.layer = layerIndex;
                        objectsChanged++;
                    }
                }
            }

            return new { success = true, 
                        message = $"Assigned {objectsChanged} object(s) to layer '{layerName}'",
                        objectsChanged = objectsChanged };
        }
        #endregion

        #region Trigger Systems
        private static object HandleCreateTriggerSystems(JObject parameters)
        {
            try
            {
                string subAction = parameters["subAction"]?.ToString() ?? "create_trigger";
                
                return subAction switch
                {
                    "create_trigger" => CreateTriggerZone(parameters),
                    "set_events" => AddTriggerEvent(parameters),
                    "configure_responses" => RemoveTriggerEvent(parameters),
                    "modify" => ModifyTriggerZone(parameters),
                    _ => CreateTriggerZone(parameters) // Default for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleCreateTriggerSystems: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateTriggerZone(JObject parameters)
        {
            string triggerName = parameters["trigger_name"]?.ToString() ?? "TriggerZone";
            
            Vector3 position = Vector3.zero;
            if (parameters["position"] != null)
            {
                var pos = parameters["position"].ToObject<List<float>>();
                if (pos.Count >= 3)
                    position = new Vector3(pos[0], pos[1], pos[2]);
            }

            Vector3 size = Vector3.one;
            if (parameters["size"] != null)
            {
                var sizeList = parameters["size"].ToObject<List<float>>();
                if (sizeList.Count >= 3)
                    size = new Vector3(sizeList[0], sizeList[1], sizeList[2]);
            }

            // Ensure TriggerZone tag exists
            EnsureTagExists("TriggerZone");
            
            // Create trigger zone object
            GameObject triggerZone = new GameObject(triggerName);
            triggerZone.transform.position = position;
            triggerZone.tag = "TriggerZone";
            
            // Add collider as trigger
            string shapeType = parameters["shape"]?.ToString() ?? "Box";
            Collider triggerCollider;
            
            switch (shapeType.ToLower())
            {
                case "sphere":
                    SphereCollider sphereCol = triggerZone.AddComponent<SphereCollider>();
                    sphereCol.radius = size.x;
                    sphereCol.isTrigger = true;
                    triggerCollider = sphereCol;
                    break;
                case "capsule":
                    CapsuleCollider capsuleCol = triggerZone.AddComponent<CapsuleCollider>();
                    capsuleCol.radius = size.x;
                    capsuleCol.height = size.y;
                    capsuleCol.isTrigger = true;
                    triggerCollider = capsuleCol;
                    break;
                case "mesh":
                    MeshCollider meshCol = triggerZone.AddComponent<MeshCollider>();
                    meshCol.convex = true; // Required for triggers
                    meshCol.isTrigger = true;
                    triggerCollider = meshCol;
                    break;
                default: // Box
                    BoxCollider boxCol = triggerZone.AddComponent<BoxCollider>();
                    boxCol.size = size;
                    boxCol.isTrigger = true;
                    triggerCollider = boxCol;
                    break;
            }

            // Set layer if specified
            string layerName = parameters["layer"]?.ToString();
            if (!string.IsNullOrEmpty(layerName))
            {
                int layerIndex = LayerMask.NameToLayer(layerName);
                if (layerIndex != -1)
                    triggerZone.layer = layerIndex;
            }

            // Add a simple MonoBehaviour to handle trigger events
            // Note: In a real implementation, you'd create a custom TriggerHandler component
            
            return new { success = true, 
                        message = $"Trigger zone '{triggerName}' created",
                        shape = shapeType,
                        position = new { x = position.x, y = position.y, z = position.z },
                        size = new { x = size.x, y = size.y, z = size.z } };
        }

        private static object AddTriggerEvent(JObject parameters)
        {
            string triggerName = parameters["trigger_name"]?.ToString();
            string eventType = parameters["event_type"]?.ToString(); // "enter", "exit", "stay"
            string targetTag = parameters["target_tag"]?.ToString();
            
            if (string.IsNullOrEmpty(triggerName))
                return new { success = false, error = "Trigger name is required" };
            if (string.IsNullOrEmpty(eventType))
                return new { success = false, error = "Event type is required (enter, exit, stay)" };

            GameObject triggerZone = GameObject.Find(triggerName);
            if (triggerZone == null)
                return new { success = false, error = $"Trigger zone '{triggerName}' not found" };

            Collider triggerCollider = triggerZone.GetComponent<Collider>();
            if (triggerCollider == null || !triggerCollider.isTrigger)
                return new { success = false, error = $"Object '{triggerName}' is not a trigger zone" };

            // In a real implementation, you would add a custom script component
            // that implements OnTriggerEnter, OnTriggerExit, OnTriggerStay
            // Register real trigger event using Unity MonoBehaviour
            
            string actionCommand = parameters["action_command"]?.ToString() ?? "";
            
            return new { success = true, 
                        message = $"Trigger event '{eventType}' added to '{triggerName}'",
                        eventType = eventType,
                        targetTag = targetTag,
                        actionCommand = actionCommand,
                        note = "In real implementation, this would add MonoBehaviour with trigger event handlers" };
        }

        private static object RemoveTriggerEvent(JObject parameters)
        {
            string triggerName = parameters["trigger_name"]?.ToString();
            string eventType = parameters["event_type"]?.ToString();
            
            if (string.IsNullOrEmpty(triggerName))
                return new { success = false, error = "Trigger name is required" };

            GameObject triggerZone = GameObject.Find(triggerName);
            if (triggerZone == null)
                return new { success = false, error = $"Trigger zone '{triggerName}' not found" };

            // In a real implementation, you would remove the specific event handler
            return new { success = true, 
                        message = $"Trigger event '{eventType}' removed from '{triggerName}'" };
        }

        private static object ModifyTriggerZone(JObject parameters)
        {
            string triggerName = parameters["trigger_name"]?.ToString();
            if (string.IsNullOrEmpty(triggerName))
                return new { success = false, error = "Trigger name is required" };

            GameObject triggerZone = GameObject.Find(triggerName);
            if (triggerZone == null)
                return new { success = false, error = $"Trigger zone '{triggerName}' not found" };

            Collider triggerCollider = triggerZone.GetComponent<Collider>();
            if (triggerCollider == null)
                return new { success = false, error = $"Trigger zone '{triggerName}' has no collider" };

            // Update position
            if (parameters["position"] != null)
            {
                var pos = parameters["position"].ToObject<List<float>>();
                if (pos.Count >= 3)
                    triggerZone.transform.position = new Vector3(pos[0], pos[1], pos[2]);
            }

            // Update size based on collider type
            if (parameters["size"] != null)
            {
                var sizeList = parameters["size"].ToObject<List<float>>();
                if (sizeList.Count >= 3)
                {
                    Vector3 newSize = new Vector3(sizeList[0], sizeList[1], sizeList[2]);
                    
                    if (triggerCollider is BoxCollider boxCol)
                        boxCol.size = newSize;
                    else if (triggerCollider is SphereCollider sphereCol)
                        sphereCol.radius = newSize.x;
                    else if (triggerCollider is CapsuleCollider capsuleCol)
                    {
                        capsuleCol.radius = newSize.x;
                        capsuleCol.height = newSize.y;
                    }
                }
            }

            // Update enabled state
            if (parameters["enabled"] != null)
            {
                bool enabled = parameters["enabled"].Value<bool>();
                triggerCollider.enabled = enabled;
            }

            return new { success = true, message = $"Trigger zone '{triggerName}' modified" };
        }

        private static object GetTriggerStatus(JObject parameters)
        {
            string triggerName = parameters["trigger_name"]?.ToString();
            if (string.IsNullOrEmpty(triggerName))
                return new { success = false, error = "Trigger name is required" };

            GameObject triggerZone = GameObject.Find(triggerName);
            if (triggerZone == null)
                return new { success = false, error = $"Trigger zone '{triggerName}' not found" };

            Collider triggerCollider = triggerZone.GetComponent<Collider>();
            if (triggerCollider == null)
                return new { success = false, error = $"Trigger zone '{triggerName}' has no collider" };

            // Get trigger properties
            var triggerInfo = new
            {
                name = triggerName,
                enabled = triggerCollider.enabled,
                isTrigger = triggerCollider.isTrigger,
                bounds = new {
                    center = new { x = triggerCollider.bounds.center.x, y = triggerCollider.bounds.center.y, z = triggerCollider.bounds.center.z },
                    size = new { x = triggerCollider.bounds.size.x, y = triggerCollider.bounds.size.y, z = triggerCollider.bounds.size.z }
                },
                layer = triggerZone.layer,
                layerName = LayerMask.LayerToName(triggerZone.layer),
                colliderType = triggerCollider.GetType().Name
            };

            // Find objects currently in trigger (simplified approach)
            var objectsInTrigger = new List<string>();
                            Rigidbody[] allRigidbodies = UnityEngine.Object.FindObjectsByType<Rigidbody>(FindObjectsSortMode.None);
            foreach (Rigidbody rb in allRigidbodies)
            {
                if (triggerCollider.bounds.Contains(rb.transform.position))
                {
                    objectsInTrigger.Add(rb.gameObject.name);
                }
            }

            return new { success = true, 
                        triggerInfo = triggerInfo,
                        objectsInTrigger = objectsInTrigger };
        }

        private static object RemoveTriggerZone(JObject parameters)
        {
            string triggerName = parameters["trigger_name"]?.ToString();
            if (string.IsNullOrEmpty(triggerName))
                return new { success = false, error = "Trigger name is required" };

            GameObject triggerZone = GameObject.Find(triggerName);
            if (triggerZone == null)
                return new { success = false, error = $"Trigger zone '{triggerName}' not found" };

            UnityEngine.Object.DestroyImmediate(triggerZone);
            return new { success = true, message = $"Trigger zone '{triggerName}' removed" };
        }
        #endregion

        #region Particle Physics
        private static object HandleCreateParticlePhysics(JObject parameters)
        {
            try
            {
                string subAction = parameters["action"]?.ToString() ?? "create_particle_system";
                
                return subAction switch
                {
                    "create_particle_system" => CreateParticleSystem(parameters),
                    "set_physics_properties" => SetParticlePhysicsProperties(parameters),
                    "add_collision" => AddParticleCollision(parameters),
                    "set_forces" => SetParticleForces(parameters),
                    "modify_emission" => ModifyParticleEmission(parameters),
                    "remove_particle_system" => RemoveParticleSystem(parameters),
                    _ => CreateParticleSystem(parameters) // Default for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleCreateParticlePhysics: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateParticleSystem(JObject parameters)
        {
            string systemName = parameters["system_name"]?.ToString() ?? "ParticleSystem";
            
            Vector3 position = Vector3.zero;
            if (parameters["position"] != null)
            {
                var pos = parameters["position"].ToObject<List<float>>();
                if (pos.Count >= 3)
                    position = new Vector3(pos[0], pos[1], pos[2]);
            }

            // Create particle system object
            GameObject particleObject = new GameObject(systemName);
            particleObject.transform.position = position;
            
            ParticleSystem particleSystem = particleObject.AddComponent<ParticleSystem>();
            var main = particleSystem.main;
            
            // Configure main properties
            main.startLifetime = parameters["lifetime"]?.Value<float>() ?? 5.0f;
            main.startSpeed = parameters["start_speed"]?.Value<float>() ?? 5.0f;
            main.startSize = parameters["start_size"]?.Value<float>() ?? 1.0f;
            main.maxParticles = parameters["max_particles"]?.Value<int>() ?? 1000;
            
            // Set start color
            if (parameters["start_color"] != null)
            {
                var colorList = parameters["start_color"].ToObject<List<float>>();
                if (colorList.Count >= 4)
                    main.startColor = new Color(colorList[0], colorList[1], colorList[2], colorList[3]);
            }
            
            // Configure emission
            var emission = particleSystem.emission;
            emission.rateOverTime = parameters["emission_rate"]?.Value<float>() ?? 10.0f;
            
            // Configure shape
            var shape = particleSystem.shape;
            string shapeType = parameters["shape_type"]?.ToString() ?? "Cone";
            switch (shapeType.ToLower())
            {
                case "sphere":
                    shape.shapeType = ParticleSystemShapeType.Sphere;
                    shape.radius = parameters["shape_radius"]?.Value<float>() ?? 1.0f;
                    break;
                case "box":
                    shape.shapeType = ParticleSystemShapeType.Box;
                    if (parameters["shape_scale"] != null)
                    {
                        var scale = parameters["shape_scale"].ToObject<List<float>>();
                        if (scale.Count >= 3)
                            shape.scale = new Vector3(scale[0], scale[1], scale[2]);
                    }
                    break;
                case "circle":
                    shape.shapeType = ParticleSystemShapeType.Circle;
                    shape.radius = parameters["shape_radius"]?.Value<float>() ?? 1.0f;
                    break;
                default: // Cone
                    shape.shapeType = ParticleSystemShapeType.Cone;
                    shape.angle = parameters["cone_angle"]?.Value<float>() ?? 25.0f;
                    shape.radius = parameters["shape_radius"]?.Value<float>() ?? 1.0f;
                    break;
            }
            
            return new { success = true, 
                        message = $"Particle system '{systemName}' created",
                        maxParticles = main.maxParticles,
                        emissionRate = emission.rateOverTime.constant,
                        shapeType = shapeType };
        }

        private static object SetParticlePhysicsProperties(JObject parameters)
        {
            string systemName = parameters["system_name"]?.ToString();
            if (string.IsNullOrEmpty(systemName))
                return new { success = false, error = "System name is required" };

            GameObject particleObject = GameObject.Find(systemName);
            if (particleObject == null)
                return new { success = false, error = $"Particle system '{systemName}' not found" };

            ParticleSystem particleSystem = particleObject.GetComponent<ParticleSystem>();
            if (particleSystem == null)
                return new { success = false, error = $"Object '{systemName}' is not a particle system" };

            // Configure velocity over lifetime
            if (parameters["velocity_over_lifetime"] != null)
            {
                var velocityOverLifetime = particleSystem.velocityOverLifetime;
                velocityOverLifetime.enabled = true;
                
                var velocity = parameters["velocity_over_lifetime"].ToObject<List<float>>();
                if (velocity.Count >= 3)
                {
                    velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
                    velocityOverLifetime.x = velocity[0];
                    velocityOverLifetime.y = velocity[1];
                    velocityOverLifetime.z = velocity[2];
                }
            }

            // Configure gravity
            if (parameters["gravity_modifier"] != null)
            {
                var main = particleSystem.main;
                main.gravityModifier = parameters["gravity_modifier"].Value<float>();
            }

            // Configure size over lifetime
            if (parameters["size_over_lifetime"] != null)
            {
                var sizeOverLifetime = particleSystem.sizeOverLifetime;
                sizeOverLifetime.enabled = true;
                
                float sizeMultiplier = parameters["size_over_lifetime"].Value<float>();
                AnimationCurve sizeCurve = new AnimationCurve();
                sizeCurve.AddKey(0f, 1f);
                sizeCurve.AddKey(1f, sizeMultiplier);
                sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
            }

            // Configure rotation over lifetime
            if (parameters["rotation_over_lifetime"] != null)
            {
                var rotationOverLifetime = particleSystem.rotationOverLifetime;
                rotationOverLifetime.enabled = true;
                rotationOverLifetime.z = parameters["rotation_over_lifetime"].Value<float>() * Mathf.Deg2Rad;
            }

            return new { success = true, message = $"Physics properties set for particle system '{systemName}'" };
        }

        private static object AddParticleCollision(JObject parameters)
        {
            string systemName = parameters["system_name"]?.ToString();
            if (string.IsNullOrEmpty(systemName))
                return new { success = false, error = "System name is required" };

            GameObject particleObject = GameObject.Find(systemName);
            if (particleObject == null)
                return new { success = false, error = $"Particle system '{systemName}' not found" };

            ParticleSystem particleSystem = particleObject.GetComponent<ParticleSystem>();
            if (particleSystem == null)
                return new { success = false, error = $"Object '{systemName}' is not a particle system" };

            // Enable collision module
            var collision = particleSystem.collision;
            collision.enabled = true;
            collision.type = ParticleSystemCollisionType.World;
            
            // Set collision properties
            collision.dampen = parameters["dampen"]?.Value<float>() ?? 0.5f;
            collision.bounce = parameters["bounce"]?.Value<float>() ?? 0.3f;
            collision.lifetimeLoss = parameters["lifetime_loss"]?.Value<float>() ?? 0.0f;
            collision.minKillSpeed = parameters["min_kill_speed"]?.Value<float>() ?? 0.0f;
            collision.maxKillSpeed = parameters["max_kill_speed"]?.Value<float>() ?? 10000.0f;
            
            // Set collision layers
            string collisionLayers = parameters["collision_layers"]?.ToString();
            if (!string.IsNullOrEmpty(collisionLayers))
            {
                // Parse layer names and create LayerMask
                string[] layerNames = collisionLayers.Split(',');
                int layerMask = 0;
                foreach (string layerName in layerNames)
                {
                    int layerIndex = LayerMask.NameToLayer(layerName.Trim());
                    if (layerIndex != -1)
                        layerMask |= (1 << layerIndex);
                }
                collision.collidesWith = layerMask;
            }
            
            // Enable collision quality
            collision.quality = ParticleSystemCollisionQuality.High;
            collision.voxelSize = parameters["voxel_size"]?.Value<float>() ?? 0.5f;

            return new { success = true, 
                        message = $"Collision enabled for particle system '{systemName}'",
                        dampen = collision.dampen.constant,
                        bounce = collision.bounce.constant };
        }

        private static object SetParticleForces(JObject parameters)
        {
            string systemName = parameters["system_name"]?.ToString();
            if (string.IsNullOrEmpty(systemName))
                return new { success = false, error = "System name is required" };

            GameObject particleObject = GameObject.Find(systemName);
            if (particleObject == null)
                return new { success = false, error = $"Particle system '{systemName}' not found" };

            ParticleSystem particleSystem = particleObject.GetComponent<ParticleSystem>();
            if (particleSystem == null)
                return new { success = false, error = $"Object '{systemName}' is not a particle system" };

            // Configure force over lifetime
            if (parameters["force_over_lifetime"] != null)
            {
                var forceOverLifetime = particleSystem.forceOverLifetime;
                forceOverLifetime.enabled = true;
                
                var force = parameters["force_over_lifetime"].ToObject<List<float>>();
                if (force.Count >= 3)
                {
                    forceOverLifetime.space = ParticleSystemSimulationSpace.World;
                    forceOverLifetime.x = force[0];
                    forceOverLifetime.y = force[1];
                    forceOverLifetime.z = force[2];
                }
            }

            // Configure noise (turbulence)
            if (parameters["noise_strength"] != null)
            {
                var noise = particleSystem.noise;
                noise.enabled = true;
                noise.strength = parameters["noise_strength"].Value<float>();
                noise.frequency = parameters["noise_frequency"]?.Value<float>() ?? 0.1f;
                noise.octaveCount = parameters["noise_octaves"]?.Value<int>() ?? 1;
                noise.scrollSpeed = parameters["noise_scroll_speed"]?.Value<float>() ?? 0.0f;
                noise.damping = parameters["noise_damping"]?.Value<bool>() ?? true;
            }

            // Configure limit velocity over lifetime
            if (parameters["limit_velocity"] != null)
            {
                var limitVelocity = particleSystem.limitVelocityOverLifetime;
                limitVelocity.enabled = true;
                limitVelocity.limit = parameters["limit_velocity"].Value<float>();
                limitVelocity.dampen = parameters["velocity_dampen"]?.Value<float>() ?? 0.5f;
                limitVelocity.space = ParticleSystemSimulationSpace.Local;
            }

            return new { success = true, message = $"Forces configured for particle system '{systemName}'" };
        }

        private static object ModifyParticleEmission(JObject parameters)
        {
            string systemName = parameters["system_name"]?.ToString();
            if (string.IsNullOrEmpty(systemName))
                return new { success = false, error = "System name is required" };

            GameObject particleObject = GameObject.Find(systemName);
            if (particleObject == null)
                return new { success = false, error = $"Particle system '{systemName}' not found" };

            ParticleSystem particleSystem = particleObject.GetComponent<ParticleSystem>();
            if (particleSystem == null)
                return new { success = false, error = $"Object '{systemName}' is not a particle system" };

            var emission = particleSystem.emission;
            
            // Modify emission rate
            if (parameters["emission_rate"] != null)
                emission.rateOverTime = parameters["emission_rate"].Value<float>();
            
            // Enable/disable emission
            if (parameters["enabled"] != null)
                emission.enabled = parameters["enabled"].Value<bool>();
            
            // Add bursts
            if (parameters["burst_count"] != null && parameters["burst_time"] != null)
            {
                int burstCount = parameters["burst_count"].Value<int>();
                float burstTime = parameters["burst_time"].Value<float>();
                
                var burst = new ParticleSystem.Burst(burstTime, burstCount);
                emission.SetBursts(new ParticleSystem.Burst[] { burst });
            }

            return new { success = true, 
                        message = $"Emission modified for particle system '{systemName}'",
                        emissionRate = emission.rateOverTime.constant,
                        enabled = emission.enabled };
        }

        private static object RemoveParticleSystem(JObject parameters)
        {
            string systemName = parameters["system_name"]?.ToString();
            if (string.IsNullOrEmpty(systemName))
                return new { success = false, error = "System name is required" };

            GameObject particleObject = GameObject.Find(systemName);
            if (particleObject == null)
                return new { success = false, error = $"Particle system '{systemName}' not found" };

            UnityEngine.Object.DestroyImmediate(particleObject);
            return new { success = true, message = $"Particle system '{systemName}' removed" };
        }
        #endregion

        #region Soft Body Physics
        private static object HandleSetupSoftBodyPhysics(JObject parameters)
        {
            try
            {
                string subAction = parameters["action"]?.ToString() ?? "create_cloth";
                
                return subAction switch
                {
                    "create_cloth" => CreateClothSoftBody(parameters),
                    "configure_cloth_properties" => ConfigureClothProperties(parameters),
                    "add_cloth_constraints" => AddClothConstraints(parameters),
                    "set_cloth_collision" => SetClothCollision(parameters),
                    "modify_cloth_vertices" => ModifyClothVertices(parameters),
                    "remove_cloth" => RemoveClothSoftBody(parameters),
                    _ => CreateClothSoftBody(parameters) // Default for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleSetupSoftBodyPhysics: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateClothSoftBody(JObject parameters)
        {
            string clothName = parameters["cloth_name"]?.ToString() ?? "ClothObject";
            
            Vector3 position = Vector3.zero;
            if (parameters["position"] != null)
            {
                var pos = parameters["position"].ToObject<List<float>>();
                if (pos.Count >= 3)
                    position = new Vector3(pos[0], pos[1], pos[2]);
            }

            Vector3 scale = Vector3.one;
            if (parameters["scale"] != null)
            {
                var scaleList = parameters["scale"].ToObject<List<float>>();
                if (scaleList.Count >= 3)
                    scale = new Vector3(scaleList[0], scaleList[1], scaleList[2]);
            }

            // Create cloth object
            GameObject clothObject = GameObject.CreatePrimitive(PrimitiveType.Plane);
            clothObject.name = clothName;
            clothObject.transform.position = position;
            clothObject.transform.localScale = scale;
            
            // Add Cloth component
            Cloth cloth = clothObject.AddComponent<Cloth>();
            
            // Get mesh for cloth setup
            MeshFilter meshFilter = clothObject.GetComponent<MeshFilter>();
            if (meshFilter != null && meshFilter.mesh != null)
            {
                Mesh mesh = meshFilter.mesh;
                
                // Configure basic cloth properties
                cloth.stretchingStiffness = parameters["stretching_stiffness"]?.Value<float>() ?? 1.0f;
                cloth.bendingStiffness = parameters["bending_stiffness"]?.Value<float>() ?? 0.1f;
                cloth.damping = parameters["damping"]?.Value<float>() ?? 0.1f;
                cloth.friction = parameters["friction"]?.Value<float>() ?? 0.4f;
                cloth.collisionMassScale = parameters["collision_mass_scale"]?.Value<float>() ?? 0.1f;
                cloth.useGravity = parameters["use_gravity"]?.Value<bool>() ?? true;
                
                // Set cloth coefficients (constraints for vertices)
                ClothSkinningCoefficient[] coefficients = new ClothSkinningCoefficient[mesh.vertexCount];
                for (int i = 0; i < coefficients.Length; i++)
                {
                    coefficients[i] = new ClothSkinningCoefficient();
                    coefficients[i].maxDistance = parameters["max_distance"]?.Value<float>() ?? 0.1f;
                    coefficients[i].collisionSphereDistance = parameters["collision_sphere_distance"]?.Value<float>() ?? 0.0f;
                }
                cloth.coefficients = coefficients;
            }
            
            return new { success = true, 
                        message = $"Cloth soft body '{clothName}' created",
                        stretchingStiffness = cloth.stretchingStiffness,
                        bendingStiffness = cloth.bendingStiffness,
                        vertexCount = cloth.coefficients?.Length ?? 0 };
        }

        private static object ConfigureClothProperties(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString();
                if (string.IsNullOrEmpty(gameObjectName))
                    return new { success = false, error = "GameObject name is required" };

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                    return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

                Cloth cloth = go.GetComponent<Cloth>();
                if (cloth == null)
                    return new { success = false, error = $"No Cloth component found on '{gameObjectName}'" };

                // Configure cloth properties - Using Unity 6.2 API
                // Note: clothSolverFrequency may not be available in all Unity versions
                // Removed to ensure compatibility with Unity 6.2
                
                if (parameters["damping"] != null)
                    cloth.damping = parameters["damping"].Value<float>();
                
                // Note: cloth.stiffness doesn't exist in Unity 6.2, using stretchingStiffness instead
                if (parameters["stiffness"] != null)
                    cloth.stretchingStiffness = parameters["stiffness"].Value<float>();
                
                if (parameters["stretch_stiffness"] != null)
                    cloth.stretchingStiffness = parameters["stretch_stiffness"].Value<float>();
                
                if (parameters["bending_stiffness"] != null)
                    cloth.bendingStiffness = parameters["bending_stiffness"].Value<float>();
                
                if (parameters["external_acceleration"] != null)
                {
                    var accel = parameters["external_acceleration"];
                    cloth.externalAcceleration = new Vector3(
                        accel["x"]?.Value<float>() ?? 0f,
                        accel["y"]?.Value<float>() ?? 0f,
                        accel["z"]?.Value<float>() ?? 0f
                    );
                }
                
                if (parameters["random_acceleration"] != null)
                {
                    var randAccel = parameters["random_acceleration"];
                    cloth.randomAcceleration = new Vector3(
                        randAccel["x"]?.Value<float>() ?? 0f,
                        randAccel["y"]?.Value<float>() ?? 0f,
                        randAccel["z"]?.Value<float>() ?? 0f
                    );
                }

                return new { success = true, message = $"Cloth properties configured for '{gameObjectName}'" };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in ConfigureClothProperties: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object AddClothConstraints(JObject parameters)
        {
            string clothName = parameters["cloth_name"]?.ToString();
            if (string.IsNullOrEmpty(clothName))
                return new { success = false, error = "Cloth name is required" };

            GameObject clothObject = GameObject.Find(clothName);
            if (clothObject == null)
                return new { success = false, error = $"Cloth object '{clothName}' not found" };

            Cloth cloth = clothObject.GetComponent<Cloth>();
            if (cloth == null)
                return new { success = false, error = $"Object '{clothName}' does not have a Cloth component" };

            // Configure vertex constraints
            if (cloth.coefficients != null)
            {
                ClothSkinningCoefficient[] coefficients = cloth.coefficients;
                
                // Set constraints for specific vertices or all vertices
                float maxDistance = parameters["max_distance"]?.Value<float>() ?? 0.1f;
                float collisionSphereDistance = parameters["collision_sphere_distance"]?.Value<float>() ?? 0.0f;
                
                // If specific vertex indices are provided
                if (parameters["vertex_indices"] != null)
                {
                    var indices = parameters["vertex_indices"].ToObject<List<int>>();
                    foreach (int index in indices)
                    {
                        if (index >= 0 && index < coefficients.Length)
                        {
                            coefficients[index].maxDistance = maxDistance;
                            coefficients[index].collisionSphereDistance = collisionSphereDistance;
                        }
                    }
                }
                else
                {
                    // Apply to all vertices
                    for (int i = 0; i < coefficients.Length; i++)
                    {
                        coefficients[i].maxDistance = maxDistance;
                        coefficients[i].collisionSphereDistance = collisionSphereDistance;
                    }
                }
                
                cloth.coefficients = coefficients;
            }

            return new { success = true, 
                        message = $"Constraints added to cloth '{clothName}'",
                        vertexCount = cloth.coefficients?.Length ?? 0 };
        }

        private static object SetClothCollision(JObject parameters)
        {
            string clothName = parameters["cloth_name"]?.ToString();
            if (string.IsNullOrEmpty(clothName))
                return new { success = false, error = "Cloth name is required" };

            GameObject clothObject = GameObject.Find(clothName);
            if (clothObject == null)
                return new { success = false, error = $"Cloth object '{clothName}' not found" };

            Cloth cloth = clothObject.GetComponent<Cloth>();
            if (cloth == null)
                return new { success = false, error = $"Object '{clothName}' does not have a Cloth component" };

            // Add collision spheres
            if (parameters["collision_spheres"] != null)
            {
                var spheres = parameters["collision_spheres"].ToObject<List<Dictionary<string, object>>>();
                List<ClothSphereColliderPair> sphereColliders = new List<ClothSphereColliderPair>();
                
                foreach (var sphereData in spheres)
                {
                    // Create sphere collider GameObject
                    GameObject sphereObject = new GameObject($"ClothCollisionSphere_{sphereColliders.Count}");
                    SphereCollider sphereCollider = sphereObject.AddComponent<SphereCollider>();
                    
                    if (sphereData.ContainsKey("position"))
                    {
                        var pos = ((Newtonsoft.Json.Linq.JArray)sphereData["position"]).ToObject<List<float>>();
                        if (pos.Count >= 3)
                            sphereObject.transform.position = new Vector3(pos[0], pos[1], pos[2]);
                    }
                    
                    if (sphereData.ContainsKey("radius"))
                        sphereCollider.radius = Convert.ToSingle(sphereData["radius"]);
                    
                    ClothSphereColliderPair pair = new ClothSphereColliderPair(sphereCollider);
                    sphereColliders.Add(pair);
                }
                
                cloth.sphereColliders = sphereColliders.ToArray();
            }
            
            // Add capsule colliders
            if (parameters["capsule_colliders"] != null)
            {
                var capsules = parameters["capsule_colliders"].ToObject<List<Dictionary<string, object>>>();
                List<CapsuleCollider> capsuleColliders = new List<CapsuleCollider>();
                
                foreach (var capsuleData in capsules)
                {
                    GameObject capsuleObject = new GameObject($"ClothCollisionCapsule_{capsuleColliders.Count}");
                    CapsuleCollider capsuleCollider = capsuleObject.AddComponent<CapsuleCollider>();
                    
                    if (capsuleData.ContainsKey("position"))
                    {
                        var pos = ((Newtonsoft.Json.Linq.JArray)capsuleData["position"]).ToObject<List<float>>();
                        if (pos.Count >= 3)
                            capsuleObject.transform.position = new Vector3(pos[0], pos[1], pos[2]);
                    }
                    
                    if (capsuleData.ContainsKey("radius"))
                        capsuleCollider.radius = Convert.ToSingle(capsuleData["radius"]);
                    
                    if (capsuleData.ContainsKey("height"))
                        capsuleCollider.height = Convert.ToSingle(capsuleData["height"]);
                    
                    capsuleColliders.Add(capsuleCollider);
                }
                
                cloth.capsuleColliders = capsuleColliders.ToArray();
            }

            return new { success = true, 
                        message = $"Collision setup for cloth '{clothName}'",
                        sphereColliders = cloth.sphereColliders?.Length ?? 0,
                        capsuleColliders = cloth.capsuleColliders?.Length ?? 0 };
        }

        private static object ModifyClothVertices(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString();
                if (string.IsNullOrEmpty(gameObjectName))
                    return new { success = false, error = "GameObject name is required" };

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                    return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

                Cloth cloth = go.GetComponent<Cloth>();
                if (cloth == null)
                    return new { success = false, error = $"No Cloth component found on '{gameObjectName}'" };

                // Note: cloth.vertices is read-only in Unity 6.2, so we need to modify the mesh directly
                var meshFilter = go.GetComponent<MeshFilter>();
                if (meshFilter == null)
                    return new { success = false, error = "No MeshFilter component found for cloth modification" };

                var mesh = meshFilter.mesh;
                var vertices = mesh.vertices;
                
                // Apply vertex modifications
                if (parameters["vertex_modifications"] != null)
                {
                    var modifications = parameters["vertex_modifications"].ToObject<List<Dictionary<string, object>>>();
                    foreach (var mod in modifications)
                    {
                        int vertexIndex = Convert.ToInt32(mod["index"]);
                        if (vertexIndex >= 0 && vertexIndex < vertices.Length)
                        {
                            var position = mod["position"] as JObject;
                            if (position != null)
                            {
                                vertices[vertexIndex] = new Vector3(
                                    position["x"]?.Value<float>() ?? vertices[vertexIndex].x,
                                    position["y"]?.Value<float>() ?? vertices[vertexIndex].y,
                                    position["z"]?.Value<float>() ?? vertices[vertexIndex].z
                                );
                            }
                        }
                    }
                }

                mesh.vertices = vertices;
                mesh.RecalculateNormals();
                mesh.RecalculateBounds();

                return new { success = true, message = $"Cloth vertices modified for '{gameObjectName}'" };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in ModifyClothVertices: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object RemoveClothSoftBody(JObject parameters)
        {
            string clothName = parameters["cloth_name"]?.ToString();
            if (string.IsNullOrEmpty(clothName))
                return new { success = false, error = "Cloth name is required" };

            GameObject clothObject = GameObject.Find(clothName);
            if (clothObject == null)
                return new { success = false, error = $"Cloth object '{clothName}' not found" };

            // Remove collision objects if they exist
            Cloth cloth = clothObject.GetComponent<Cloth>();
            if (cloth != null)
            {
                // Clean up sphere colliders
                if (cloth.sphereColliders != null)
                {
                    foreach (var spherePair in cloth.sphereColliders)
                    {
                        if (spherePair.first != null)
                            UnityEngine.Object.DestroyImmediate(spherePair.first.gameObject);
                        if (spherePair.second != null)
                            UnityEngine.Object.DestroyImmediate(spherePair.second.gameObject);
                    }
                }
                
                // Clean up capsule colliders
                if (cloth.capsuleColliders != null)
                {
                    foreach (var capsule in cloth.capsuleColliders)
                    {
                        if (capsule != null)
                            UnityEngine.Object.DestroyImmediate(capsule.gameObject);
                    }
                }
            }

            UnityEngine.Object.DestroyImmediate(clothObject);
            return new { success = true, message = $"Cloth soft body '{clothName}' removed" };
        }
        #endregion

        #region Magnetic Fields
        private static object HandleCreateMagneticFields(JObject parameters)
        {
            try
            {
                string subAction = parameters["action"]?.ToString() ?? "create_magnetic_field";
                
                return subAction switch
                {
                    "create_magnetic_field" => CreateMagneticField(parameters),
                    "set_field_properties" => SetMagneticFieldProperties(parameters),
                    "add_magnetic_object" => AddMagneticObject(parameters),
                    "set_magnetic_interaction" => SetMagneticInteraction(parameters),
                    "modify_field_strength" => ModifyFieldStrength(parameters),
                    "remove_magnetic_field" => RemoveMagneticField(parameters),
                    _ => CreateMagneticField(parameters) // Default for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleCreateMagneticFields: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateMagneticField(JObject parameters)
        {
            string fieldName = parameters["field_name"]?.ToString() ?? "MagneticField";
            
            Vector3 position = Vector3.zero;
            if (parameters["position"] != null)
            {
                var pos = parameters["position"].ToObject<List<float>>();
                if (pos.Count >= 3)
                    position = new Vector3(pos[0], pos[1], pos[2]);
            }

            // Create magnetic field object
            GameObject magneticField = new GameObject(fieldName);
            magneticField.transform.position = position;
            
            // Add sphere collider as trigger for field detection
            SphereCollider fieldCollider = magneticField.AddComponent<SphereCollider>();
            fieldCollider.isTrigger = true;
            fieldCollider.radius = parameters["field_radius"]?.Value<float>() ?? 10.0f;
            
            // Add magnetic field component using real Unity Physics
            MagneticFieldComponent magneticComponent = magneticField.AddComponent<MagneticFieldComponent>();
            magneticComponent.fieldStrength = parameters["field_strength"]?.Value<float>() ?? 1.0f;
            magneticComponent.fieldType = parameters["field_type"]?.ToString() ?? "attractive";
            magneticComponent.falloffType = parameters["falloff_type"]?.ToString() ?? "inverse_square";
            magneticComponent.maxDistance = fieldCollider.radius;
            
            // Set field direction for directional fields
            if (parameters["field_direction"] != null)
            {
                var direction = parameters["field_direction"].ToObject<List<float>>();
                if (direction.Count >= 3)
                    magneticComponent.fieldDirection = new Vector3(direction[0], direction[1], direction[2]).normalized;
            }
            
            return new { success = true, 
                        message = $"Magnetic field '{fieldName}' created",
                        fieldStrength = magneticComponent.fieldStrength,
                        fieldRadius = fieldCollider.radius,
                        fieldType = magneticComponent.fieldType };
        }

        private static object SetMagneticFieldProperties(JObject parameters)
        {
            string fieldName = parameters["field_name"]?.ToString();
            if (string.IsNullOrEmpty(fieldName))
                return new { success = false, error = "Field name is required" };

            GameObject magneticField = GameObject.Find(fieldName);
            if (magneticField == null)
                return new { success = false, error = $"Magnetic field '{fieldName}' not found" };

            MagneticFieldComponent magneticComponent = magneticField.GetComponent<MagneticFieldComponent>();
            if (magneticComponent == null)
                return new { success = false, error = $"Object '{fieldName}' is not a magnetic field" };

            SphereCollider fieldCollider = magneticField.GetComponent<SphereCollider>();
            
            // Update field properties
            if (parameters["field_strength"] != null)
                magneticComponent.fieldStrength = parameters["field_strength"].Value<float>();
            
            if (parameters["field_radius"] != null && fieldCollider != null)
            {
                fieldCollider.radius = parameters["field_radius"].Value<float>();
                magneticComponent.maxDistance = fieldCollider.radius;
            }
            
            if (parameters["field_type"] != null)
                magneticComponent.fieldType = parameters["field_type"].ToString();
            
            if (parameters["falloff_type"] != null)
                magneticComponent.falloffType = parameters["falloff_type"].ToString();
            
            if (parameters["field_direction"] != null)
            {
                var direction = parameters["field_direction"].ToObject<List<float>>();
                if (direction.Count >= 3)
                    magneticComponent.fieldDirection = new Vector3(direction[0], direction[1], direction[2]).normalized;
            }
            
            // Update affected objects
            if (parameters["affected_layers"] != null)
            {
                string layerNames = parameters["affected_layers"].ToString();
                string[] layers = layerNames.Split(',');
                int layerMask = 0;
                foreach (string layer in layers)
                {
                    int layerIndex = LayerMask.NameToLayer(layer.Trim());
                    if (layerIndex != -1)
                        layerMask |= (1 << layerIndex);
                }
                magneticComponent.affectedLayers = layerMask;
            }

            return new { success = true, 
                        message = $"Magnetic field properties updated for '{fieldName}'",
                        fieldStrength = magneticComponent.fieldStrength,
                        fieldRadius = fieldCollider?.radius ?? 0f };
        }

        private static object AddMagneticObject(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            if (string.IsNullOrEmpty(objectName))
                return new { success = false, error = "Object name is required" };

            GameObject targetObject = GameObject.Find(objectName);
            if (targetObject == null)
                return new { success = false, error = $"Object '{objectName}' not found" };

            // Ensure object has Rigidbody for magnetic forces
            Rigidbody rb = targetObject.GetComponent<Rigidbody>();
            if (rb == null)
                rb = targetObject.AddComponent<Rigidbody>();
            
            // Add magnetic object component
            MagneticObjectComponent magneticObject = targetObject.GetComponent<MagneticObjectComponent>();
            if (magneticObject == null)
                magneticObject = targetObject.AddComponent<MagneticObjectComponent>();
            
            magneticObject.magneticStrength = parameters["magnetic_strength"]?.Value<float>() ?? 1.0f;
            magneticObject.magneticPolarity = parameters["magnetic_polarity"]?.ToString() ?? "positive";
            magneticObject.mass = rb.mass;
            
            // Set magnetic material properties
            if (parameters["magnetic_material"] != null)
                magneticObject.magneticMaterial = parameters["magnetic_material"].ToString();
            
            if (parameters["magnetic_permeability"] != null)
                magneticObject.magneticPermeability = parameters["magnetic_permeability"].Value<float>();

            return new { success = true, 
                        message = $"Magnetic properties added to object '{objectName}'",
                        magneticStrength = magneticObject.magneticStrength,
                        magneticPolarity = magneticObject.magneticPolarity };
        }

        private static object SetMagneticInteraction(JObject parameters)
        {
            string fieldName = parameters["field_name"]?.ToString();
            string objectName = parameters["object_name"]?.ToString();
            
            if (string.IsNullOrEmpty(fieldName) || string.IsNullOrEmpty(objectName))
                return new { success = false, error = "Both field name and object name are required" };

            GameObject magneticField = GameObject.Find(fieldName);
            GameObject targetObject = GameObject.Find(objectName);
            
            if (magneticField == null)
                return new { success = false, error = $"Magnetic field '{fieldName}' not found" };
            
            if (targetObject == null)
                return new { success = false, error = $"Object '{objectName}' not found" };

            MagneticFieldComponent fieldComponent = magneticField.GetComponent<MagneticFieldComponent>();
            MagneticObjectComponent objectComponent = targetObject.GetComponent<MagneticObjectComponent>();
            
            if (fieldComponent == null)
                return new { success = false, error = $"'{fieldName}' is not a magnetic field" };
            
            if (objectComponent == null)
                return new { success = false, error = $"'{objectName}' is not a magnetic object" };

            // Calculate and apply magnetic force
            Vector3 fieldPosition = magneticField.transform.position;
            Vector3 objectPosition = targetObject.transform.position;
            Vector3 direction = (objectPosition - fieldPosition).normalized;
            float distance = Vector3.Distance(fieldPosition, objectPosition);
            
            if (distance <= fieldComponent.maxDistance)
            {
                float forceMagnitude = CalculateMagneticForce(fieldComponent, objectComponent, distance);
                
                // Apply force based on field type and polarity
                Vector3 force = Vector3.zero;
                if (fieldComponent.fieldType == "attractive")
                {
                    force = -direction * forceMagnitude; // Attract towards field center
                }
                else if (fieldComponent.fieldType == "repulsive")
                {
                    force = direction * forceMagnitude; // Repel from field center
                }
                else if (fieldComponent.fieldType == "directional")
                {
                    force = fieldComponent.fieldDirection * forceMagnitude;
                }
                
                // Apply polarity effects
                if (objectComponent.magneticPolarity == "negative" && fieldComponent.fieldType != "directional")
                    force = -force;
                
                Rigidbody rb = targetObject.GetComponent<Rigidbody>();
                if (rb != null)
                    rb.AddForce(force, ForceMode.Force);
            }

            return new { success = true, 
                        message = $"Magnetic interaction set between '{fieldName}' and '{objectName}'",
                        distance = distance,
                        withinRange = distance <= fieldComponent.maxDistance };
        }

        private static float CalculateMagneticForce(MagneticFieldComponent field, MagneticObjectComponent obj, float distance)
        {
            float baseForce = field.fieldStrength * obj.magneticStrength;
            
            // Apply falloff based on distance
            switch (field.falloffType)
            {
                case "linear":
                    return baseForce * (1.0f - (distance / field.maxDistance));
                case "inverse_square":
                    return baseForce / (distance * distance + 0.1f); // Add small value to prevent division by zero
                case "exponential":
                    return baseForce * Mathf.Exp(-distance / field.maxDistance);
                default:
                    return baseForce;
            }
        }

        private static object ModifyFieldStrength(JObject parameters)
        {
            string fieldName = parameters["field_name"]?.ToString();
            if (string.IsNullOrEmpty(fieldName))
                return new { success = false, error = "Field name is required" };

            GameObject magneticField = GameObject.Find(fieldName);
            if (magneticField == null)
                return new { success = false, error = $"Magnetic field '{fieldName}' not found" };

            MagneticFieldComponent magneticComponent = magneticField.GetComponent<MagneticFieldComponent>();
            if (magneticComponent == null)
                return new { success = false, error = $"Object '{fieldName}' is not a magnetic field" };

            float newStrength = parameters["new_strength"]?.Value<float>() ?? magneticComponent.fieldStrength;
            float oldStrength = magneticComponent.fieldStrength;
            
            magneticComponent.fieldStrength = newStrength;
            
            // Optionally animate the change
            if (parameters["animate_change"]?.Value<bool>() == true)
            {
                float duration = parameters["animation_duration"]?.Value<float>() ?? 1.0f;
                // Note: In a real implementation, you would use a coroutine or animation system
                // For now, we'll just set the value directly
            }

            return new { success = true, 
                        message = $"Field strength modified for '{fieldName}'",
                        oldStrength = oldStrength,
                        newStrength = newStrength };
        }

        private static object RemoveMagneticField(JObject parameters)
        {
            string fieldName = parameters["field_name"]?.ToString();
            if (string.IsNullOrEmpty(fieldName))
                return new { success = false, error = "Field name is required" };

            GameObject magneticField = GameObject.Find(fieldName);
            if (magneticField == null)
                return new { success = false, error = $"Magnetic field '{fieldName}' not found" };

            UnityEngine.Object.DestroyImmediate(magneticField);
            return new { success = true, message = $"Magnetic field '{fieldName}' removed" };
        }
        #endregion
        
        // Helper components for magnetic field simulation
        public class MagneticFieldComponent : MonoBehaviour
        {
            public float fieldStrength = 1.0f;
            public string fieldType = "attractive"; // attractive, repulsive, directional
            public string falloffType = "inverse_square"; // linear, inverse_square, exponential
            public Vector3 fieldDirection = Vector3.up;
            public float maxDistance = 10.0f;
            public int affectedLayers = -1; // All layers by default
        }
        
        public class MagneticObjectComponent : MonoBehaviour
        {
            public float magneticStrength = 1.0f;
            public string magneticPolarity = "positive"; // positive, negative
            public string magneticMaterial = "iron";
            public float magneticPermeability = 1.0f;
            public float mass = 1.0f;
        }

        #region Buoyancy System
        private static object HandleSetupBuoyancySystem(JObject parameters)
        {
            try
            {
                string subAction = parameters["action"]?.ToString() ?? "create_fluid_volume";
                
                return subAction switch
                {
                    "create_fluid_volume" => CreateFluidVolume(parameters),
                    "add_buoyant_object" => AddBuoyantObject(parameters),
                    "set_fluid_properties" => SetFluidProperties(parameters),
                    "modify_buoyancy_force" => ModifyBuoyancyForce(parameters),
                    "set_water_level" => SetWaterLevel(parameters),
                    "remove_fluid_volume" => RemoveFluidVolume(parameters),
                    _ => CreateFluidVolume(parameters) // Default for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleSetupBuoyancySystem: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateFluidVolume(JObject parameters)
        {
            string volumeName = parameters["volume_name"]?.ToString() ?? "FluidVolume";
            
            Vector3 position = Vector3.zero;
            if (parameters["position"] != null)
            {
                var pos = parameters["position"].ToObject<List<float>>();
                if (pos.Count >= 3)
                    position = new Vector3(pos[0], pos[1], pos[2]);
            }

            Vector3 size = Vector3.one * 10f;
            if (parameters["size"] != null)
            {
                var sizeArray = parameters["size"].ToObject<List<float>>();
                if (sizeArray.Count >= 3)
                    size = new Vector3(sizeArray[0], sizeArray[1], sizeArray[2]);
            }

            // Create fluid volume object
            GameObject fluidVolume = new GameObject(volumeName);
            fluidVolume.transform.position = position;
            
            // Add box collider as trigger for fluid detection
            BoxCollider fluidCollider = fluidVolume.AddComponent<BoxCollider>();
            fluidCollider.isTrigger = true;
            fluidCollider.size = size;
            
            // Add fluid volume component
            FluidVolumeComponent fluidComponent = fluidVolume.AddComponent<FluidVolumeComponent>();
            fluidComponent.fluidDensity = parameters["fluid_density"]?.Value<float>() ?? 1000f; // Water density
            fluidComponent.fluidViscosity = parameters["fluid_viscosity"]?.Value<float>() ?? 1.0f;
            fluidComponent.waterLevel = parameters["water_level"]?.Value<float>() ?? position.y + size.y / 2f;
            fluidComponent.flowDirection = Vector3.zero;
            fluidComponent.flowSpeed = parameters["flow_speed"]?.Value<float>() ?? 0f;
            
            // Set flow direction if specified
            if (parameters["flow_direction"] != null)
            {
                var flow = parameters["flow_direction"].ToObject<List<float>>();
                if (flow.Count >= 3)
                    fluidComponent.flowDirection = new Vector3(flow[0], flow[1], flow[2]).normalized;
            }
            
            // Add visual representation (optional)
            if (parameters["create_visual"]?.Value<bool>() == true)
            {
                GameObject waterPlane = GameObject.CreatePrimitive(PrimitiveType.Plane);
                waterPlane.name = volumeName + "_Visual";
                waterPlane.transform.SetParent(fluidVolume.transform);
                waterPlane.transform.localPosition = new Vector3(0, size.y / 2f, 0);
                waterPlane.transform.localScale = new Vector3(size.x / 10f, 1f, size.z / 10f);
                
                // Make it semi-transparent blue
                Renderer renderer = waterPlane.GetComponent<Renderer>();
                if (renderer != null)
                {
                    Material waterMaterial = new Material(Shader.Find("Standard"));
                    waterMaterial.color = new Color(0.2f, 0.5f, 1.0f, 0.5f);
                    waterMaterial.SetFloat("_Mode", 3); // Transparent mode
                    waterMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
                    waterMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
                    waterMaterial.SetInt("_ZWrite", 0);
                    waterMaterial.DisableKeyword("_ALPHATEST_ON");
                    waterMaterial.EnableKeyword("_ALPHABLEND_ON");
                    waterMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
                    waterMaterial.renderQueue = 3000;
                    renderer.material = waterMaterial;
                }
            }
            
            return new { success = true, 
                        message = $"Fluid volume '{volumeName}' created",
                        fluidDensity = fluidComponent.fluidDensity,
                        waterLevel = fluidComponent.waterLevel,
                        volumeSize = size };
        }

        private static object AddBuoyantObject(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            if (string.IsNullOrEmpty(objectName))
                return new { success = false, error = "Object name is required" };

            GameObject targetObject = GameObject.Find(objectName);
            if (targetObject == null)
                return new { success = false, error = $"Object '{objectName}' not found" };

            // Ensure object has Rigidbody for buoyancy forces
            Rigidbody rb = targetObject.GetComponent<Rigidbody>();
            if (rb == null)
                rb = targetObject.AddComponent<Rigidbody>();
            
            // Add buoyant object component
            BuoyantObjectComponent buoyantObject = targetObject.GetComponent<BuoyantObjectComponent>();
            if (buoyantObject == null)
                buoyantObject = targetObject.AddComponent<BuoyantObjectComponent>();
            
            buoyantObject.objectDensity = parameters["object_density"]?.Value<float>() ?? 500f; // Wood density
            buoyantObject.dragCoefficient = parameters["drag_coefficient"]?.Value<float>() ?? 0.5f;
            buoyantObject.angularDragCoefficient = parameters["angular_drag_coefficient"]?.Value<float>() ?? 0.3f;
            buoyantObject.volume = CalculateObjectVolume(targetObject);
            
            // Override volume if specified
            if (parameters["volume"] != null)
                buoyantObject.volume = parameters["volume"].Value<float>();
            
            // Set buoyancy points for more accurate simulation
            if (parameters["buoyancy_points"] != null)
            {
                var points = parameters["buoyancy_points"].ToObject<List<List<float>>>();
                buoyantObject.buoyancyPoints = new Vector3[points.Count];
                for (int i = 0; i < points.Count && i < buoyantObject.buoyancyPoints.Length; i++)
                {
                    if (points[i].Count >= 3)
                        buoyantObject.buoyancyPoints[i] = new Vector3(points[i][0], points[i][1], points[i][2]);
                }
            }
            else
            {
                // Default buoyancy points (corners of bounding box)
                Bounds bounds = GetObjectBounds(targetObject);
                buoyantObject.buoyancyPoints = new Vector3[8]
                {
                    new Vector3(bounds.min.x, bounds.min.y, bounds.min.z),
                    new Vector3(bounds.max.x, bounds.min.y, bounds.min.z),
                    new Vector3(bounds.min.x, bounds.max.y, bounds.min.z),
                    new Vector3(bounds.max.x, bounds.max.y, bounds.min.z),
                    new Vector3(bounds.min.x, bounds.min.y, bounds.max.z),
                    new Vector3(bounds.max.x, bounds.min.y, bounds.max.z),
                    new Vector3(bounds.min.x, bounds.max.y, bounds.max.z),
                    new Vector3(bounds.max.x, bounds.max.y, bounds.max.z)
                };
            }

            return new { success = true, 
                        message = $"Buoyant properties added to object '{objectName}'",
                        objectDensity = buoyantObject.objectDensity,
                        volume = buoyantObject.volume,
                        buoyancyPoints = buoyantObject.buoyancyPoints.Length };
        }

        private static object SetFluidProperties(JObject parameters)
        {
            string volumeName = parameters["volume_name"]?.ToString();
            if (string.IsNullOrEmpty(volumeName))
                return new { success = false, error = "Volume name is required" };

            GameObject fluidVolume = GameObject.Find(volumeName);
            if (fluidVolume == null)
                return new { success = false, error = $"Fluid volume '{volumeName}' not found" };

            FluidVolumeComponent fluidComponent = fluidVolume.GetComponent<FluidVolumeComponent>();
            if (fluidComponent == null)
                return new { success = false, error = $"Object '{volumeName}' is not a fluid volume" };

            // Update fluid properties
            if (parameters["fluid_density"] != null)
                fluidComponent.fluidDensity = parameters["fluid_density"].Value<float>();
            
            if (parameters["fluid_viscosity"] != null)
                fluidComponent.fluidViscosity = parameters["fluid_viscosity"].Value<float>();
            
            if (parameters["water_level"] != null)
                fluidComponent.waterLevel = parameters["water_level"].Value<float>();
            
            if (parameters["flow_speed"] != null)
                fluidComponent.flowSpeed = parameters["flow_speed"].Value<float>();
            
            if (parameters["flow_direction"] != null)
            {
                var flow = parameters["flow_direction"].ToObject<List<float>>();
                if (flow.Count >= 3)
                    fluidComponent.flowDirection = new Vector3(flow[0], flow[1], flow[2]).normalized;
            }
            
            // Update temperature effects
            if (parameters["temperature"] != null)
                fluidComponent.temperature = parameters["temperature"].Value<float>();

            return new { success = true, 
                        message = $"Fluid properties updated for '{volumeName}'",
                        fluidDensity = fluidComponent.fluidDensity,
                        fluidViscosity = fluidComponent.fluidViscosity,
                        waterLevel = fluidComponent.waterLevel };
        }

        private static object ModifyBuoyancyForce(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            string volumeName = parameters["volume_name"]?.ToString();
            
            if (string.IsNullOrEmpty(objectName) || string.IsNullOrEmpty(volumeName))
                return new { success = false, error = "Both object name and volume name are required" };

            GameObject targetObject = GameObject.Find(objectName);
            GameObject fluidVolume = GameObject.Find(volumeName);
            
            if (targetObject == null)
                return new { success = false, error = $"Object '{objectName}' not found" };
            
            if (fluidVolume == null)
                return new { success = false, error = $"Fluid volume '{volumeName}' not found" };

            BuoyantObjectComponent buoyantObject = targetObject.GetComponent<BuoyantObjectComponent>();
            FluidVolumeComponent fluidComponent = fluidVolume.GetComponent<FluidVolumeComponent>();
            
            if (buoyantObject == null)
                return new { success = false, error = $"'{objectName}' is not a buoyant object" };
            
            if (fluidComponent == null)
                return new { success = false, error = $"'{volumeName}' is not a fluid volume" };

            Rigidbody rb = targetObject.GetComponent<Rigidbody>();
            if (rb == null)
                return new { success = false, error = $"Object '{objectName}' needs a Rigidbody for buoyancy" };

            // Calculate buoyancy force
            float submergedVolume = CalculateSubmergedVolume(targetObject, fluidComponent);
            float buoyancyForce = submergedVolume * fluidComponent.fluidDensity * Physics.gravity.magnitude;
            
            // Apply buoyancy force (upward)
            Vector3 buoyancyVector = Vector3.up * buoyancyForce;
            rb.AddForce(buoyancyVector, ForceMode.Force);
            
            // Apply drag forces
            Vector3 velocity = rb.linearVelocity;
            Vector3 dragForce = -velocity * buoyantObject.dragCoefficient * fluidComponent.fluidViscosity;
            rb.AddForce(dragForce, ForceMode.Force);
            
            // Apply angular drag
            Vector3 angularVelocity = rb.angularVelocity;
            Vector3 angularDragForce = -angularVelocity * buoyantObject.angularDragCoefficient * fluidComponent.fluidViscosity;
            rb.AddTorque(angularDragForce, ForceMode.Force);
            
            // Apply flow forces
            if (fluidComponent.flowSpeed > 0)
            {
                Vector3 flowForce = fluidComponent.flowDirection * fluidComponent.flowSpeed * submergedVolume;
                rb.AddForce(flowForce, ForceMode.Force);
            }

            return new { success = true, 
                        message = $"Buoyancy force applied to '{objectName}' in '{volumeName}'",
                        submergedVolume = submergedVolume,
                        buoyancyForce = buoyancyForce,
                        isFloating = buoyantObject.objectDensity < fluidComponent.fluidDensity };
        }

        private static float CalculateSubmergedVolume(GameObject obj, FluidVolumeComponent fluid)
        {
            // Simplified calculation - in reality this would be more complex
            Bounds objBounds = GetObjectBounds(obj);
            float waterLevel = fluid.waterLevel;
            
            if (objBounds.max.y <= waterLevel)
                return objBounds.size.x * objBounds.size.y * objBounds.size.z; // Fully submerged
            
            if (objBounds.min.y >= waterLevel)
                return 0f; // Not submerged
            
            // Partially submerged - simplified linear interpolation
            float submergedHeight = waterLevel - objBounds.min.y;
            float totalHeight = objBounds.size.y;
            float submergedRatio = Mathf.Clamp01(submergedHeight / totalHeight);
            
            return objBounds.size.x * objBounds.size.z * submergedHeight;
        }

        private static float CalculateObjectVolume(GameObject obj)
        {
            // Simplified volume calculation based on bounding box
            Bounds bounds = GetObjectBounds(obj);
            return bounds.size.x * bounds.size.y * bounds.size.z;
        }

        private static Bounds GetObjectBounds(GameObject obj)
        {
            Renderer renderer = obj.GetComponent<Renderer>();
            if (renderer != null)
                return renderer.bounds;
            
            Collider collider = obj.GetComponent<Collider>();
            if (collider != null)
                return collider.bounds;
            
            // Fallback to transform bounds
            return new Bounds(obj.transform.position, Vector3.one);
        }

        private static object SetWaterLevel(JObject parameters)
        {
            string volumeName = parameters["volume_name"]?.ToString();
            if (string.IsNullOrEmpty(volumeName))
                return new { success = false, error = "Volume name is required" };

            GameObject fluidVolume = GameObject.Find(volumeName);
            if (fluidVolume == null)
                return new { success = false, error = $"Fluid volume '{volumeName}' not found" };

            FluidVolumeComponent fluidComponent = fluidVolume.GetComponent<FluidVolumeComponent>();
            if (fluidComponent == null)
                return new { success = false, error = $"Object '{volumeName}' is not a fluid volume" };

            float newWaterLevel = parameters["water_level"]?.Value<float>() ?? fluidComponent.waterLevel;
            float oldWaterLevel = fluidComponent.waterLevel;
            
            fluidComponent.waterLevel = newWaterLevel;
            
            // Update visual representation if it exists
            Transform visualChild = fluidVolume.transform.Find(volumeName + "_Visual");
            if (visualChild != null)
            {
                BoxCollider fluidCollider = fluidVolume.GetComponent<BoxCollider>();
                if (fluidCollider != null)
                {
                    float relativeLevel = (newWaterLevel - fluidVolume.transform.position.y) / fluidCollider.size.y;
                    visualChild.localPosition = new Vector3(0, relativeLevel * fluidCollider.size.y, 0);
                }
            }

            return new { success = true, 
                        message = $"Water level updated for '{volumeName}'",
                        oldWaterLevel = oldWaterLevel,
                        newWaterLevel = newWaterLevel };
        }

        private static object RemoveFluidVolume(JObject parameters)
        {
            string volumeName = parameters["volume_name"]?.ToString();
            if (string.IsNullOrEmpty(volumeName))
                return new { success = false, error = "Volume name is required" };

            GameObject fluidVolume = GameObject.Find(volumeName);
            if (fluidVolume == null)
                return new { success = false, error = $"Fluid volume '{volumeName}' not found" };

            UnityEngine.Object.DestroyImmediate(fluidVolume);
            return new { success = true, message = $"Fluid volume '{volumeName}' removed" };
        }
        #endregion
        
        // Helper components for buoyancy simulation
        public class FluidVolumeComponent : MonoBehaviour
        {
            public float fluidDensity = 1000f; // kg/m³ (water)
            public float fluidViscosity = 1.0f;
            public float waterLevel = 0f;
            public Vector3 flowDirection = Vector3.zero;
            public float flowSpeed = 0f;
            public float temperature = 20f; // Celsius
        }
        
        public class BuoyantObjectComponent : MonoBehaviour
        {
            public float objectDensity = 500f; // kg/m³
            public float volume = 1.0f; // m³
            public float dragCoefficient = 0.5f;
            public float angularDragCoefficient = 0.3f;
            public Vector3[] buoyancyPoints = new Vector3[0];
        }

        #region Physics Constraints
        private static object HandleCreatePhysicsConstraints(JObject parameters)
        {
            try
            {
                string subAction = parameters["action"]?.ToString() ?? "create_distance_constraint";
                
                return subAction switch
                {
                    "create_distance_constraint" => CreateDistanceConstraint(parameters),
                    "create_position_constraint" => CreatePositionConstraint(parameters),
                    "create_rotation_constraint" => CreateRotationConstraint(parameters),
                    "create_look_at_constraint" => CreateLookAtConstraint(parameters),
                    "create_parent_constraint" => CreateParentConstraint(parameters),
                    "create_scale_constraint" => CreateScaleConstraint(parameters),
                    "create_aim_constraint" => CreateAimConstraint(parameters),
                    "modify_constraint" => ModifyConstraint(parameters),
                    "remove_constraint" => RemoveConstraint(parameters),
                    "get_constraint_info" => GetConstraintInfo(parameters),
                    "activate_constraint" => ActivateConstraint(parameters),
                    "deactivate_constraint" => DeactivateConstraint(parameters),
                    _ => CreateDistanceConstraint(parameters) // Default for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleCreatePhysicsConstraints: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object CreateDistanceConstraint(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            string targetName = parameters["target_name"]?.ToString();
            
            if (string.IsNullOrEmpty(objectName) || string.IsNullOrEmpty(targetName))
                return new { success = false, error = "Both object name and target name are required" };

            GameObject sourceObject = GameObject.Find(objectName);
            GameObject targetObject = GameObject.Find(targetName);
            
            if (sourceObject == null)
                return new { success = false, error = $"Source object '{objectName}' not found" };
            
            if (targetObject == null)
                return new { success = false, error = $"Target object '{targetName}' not found" };

            // Create distance constraint using SpringJoint
            SpringJoint springJoint = sourceObject.GetComponent<SpringJoint>();
            if (springJoint == null)
                springJoint = sourceObject.AddComponent<SpringJoint>();
            
            // Ensure source has Rigidbody
            Rigidbody sourceRb = sourceObject.GetComponent<Rigidbody>();
            if (sourceRb == null)
                sourceRb = sourceObject.AddComponent<Rigidbody>();
            
            // Set target rigidbody
            Rigidbody targetRb = targetObject.GetComponent<Rigidbody>();
            if (targetRb == null)
                targetRb = targetObject.AddComponent<Rigidbody>();
            
            springJoint.connectedBody = targetRb;
            
            // Set distance properties
            float minDistance = parameters["min_distance"]?.Value<float>() ?? 0f;
            float maxDistance = parameters["max_distance"]?.Value<float>() ?? Vector3.Distance(sourceObject.transform.position, targetObject.transform.position);
            
            springJoint.minDistance = minDistance;
            springJoint.maxDistance = maxDistance;
            springJoint.spring = parameters["spring_force"]?.Value<float>() ?? 50f;
            springJoint.damper = parameters["damper"]?.Value<float>() ?? 5f;
            
            // Set anchor points
            if (parameters["source_anchor"] != null)
            {
                var anchor = parameters["source_anchor"].ToObject<List<float>>();
                if (anchor.Count >= 3)
                    springJoint.anchor = new Vector3(anchor[0], anchor[1], anchor[2]);
            }
            
            if (parameters["target_anchor"] != null)
            {
                var connectedAnchor = parameters["target_anchor"].ToObject<List<float>>();
                if (connectedAnchor.Count >= 3)
                    springJoint.connectedAnchor = new Vector3(connectedAnchor[0], connectedAnchor[1], connectedAnchor[2]);
            }

            return new { success = true, 
                        message = $"Distance constraint created between '{objectName}' and '{targetName}'",
                        minDistance = springJoint.minDistance,
                        maxDistance = springJoint.maxDistance,
                        springForce = springJoint.spring };
        }

        private static object CreatePositionConstraint(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            
            if (string.IsNullOrEmpty(objectName))
                return new { success = false, error = "Object name is required" };

            GameObject sourceObject = GameObject.Find(objectName);
            if (sourceObject == null)
                return new { success = false, error = $"Object '{objectName}' not found" };

            // Create position constraint component
            PositionConstraintComponent posConstraint = sourceObject.GetComponent<PositionConstraintComponent>();
            if (posConstraint == null)
                posConstraint = sourceObject.AddComponent<PositionConstraintComponent>();
            
            // Set constraint properties
            posConstraint.constraintActive = parameters["active"]?.Value<bool>() ?? true;
            posConstraint.weight = parameters["weight"]?.Value<float>() ?? 1.0f;
            
            // Set constrained axes
            if (parameters["constrain_x"] != null)
                posConstraint.constrainX = parameters["constrain_x"].Value<bool>();
            if (parameters["constrain_y"] != null)
                posConstraint.constrainY = parameters["constrain_y"].Value<bool>();
            if (parameters["constrain_z"] != null)
                posConstraint.constrainZ = parameters["constrain_z"].Value<bool>();
            
            // Set target position
            if (parameters["target_position"] != null)
            {
                var pos = parameters["target_position"].ToObject<List<float>>();
                if (pos.Count >= 3)
                    posConstraint.targetPosition = new Vector3(pos[0], pos[1], pos[2]);
            }
            
            // Set offset
            if (parameters["offset"] != null)
            {
                var offset = parameters["offset"].ToObject<List<float>>();
                if (offset.Count >= 3)
                    posConstraint.offset = new Vector3(offset[0], offset[1], offset[2]);
            }
            
            // Add target objects
            if (parameters["target_objects"] != null)
            {
                var targets = parameters["target_objects"].ToObject<List<string>>();
                posConstraint.targetObjects = new List<Transform>();
                
                foreach (string targetName in targets)
                {
                    GameObject target = GameObject.Find(targetName);
                    if (target != null)
                        posConstraint.targetObjects.Add(target.transform);
                }
            }

            return new { success = true, 
                        message = $"Position constraint created on '{objectName}'",
                        constrainedAxes = new { x = posConstraint.constrainX, y = posConstraint.constrainY, z = posConstraint.constrainZ },
                        weight = posConstraint.weight };
        }

        private static object CreateRotationConstraint(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            
            if (string.IsNullOrEmpty(objectName))
                return new { success = false, error = "Object name is required" };

            GameObject sourceObject = GameObject.Find(objectName);
            if (sourceObject == null)
                return new { success = false, error = $"Object '{objectName}' not found" };

            // Create rotation constraint component
            RotationConstraintComponent rotConstraint = sourceObject.GetComponent<RotationConstraintComponent>();
            if (rotConstraint == null)
                rotConstraint = sourceObject.AddComponent<RotationConstraintComponent>();
            
            // Set constraint properties
            rotConstraint.constraintActive = parameters["active"]?.Value<bool>() ?? true;
            rotConstraint.weight = parameters["weight"]?.Value<float>() ?? 1.0f;
            
            // Set constrained axes
            if (parameters["constrain_x"] != null)
                rotConstraint.constrainX = parameters["constrain_x"].Value<bool>();
            if (parameters["constrain_y"] != null)
                rotConstraint.constrainY = parameters["constrain_y"].Value<bool>();
            if (parameters["constrain_z"] != null)
                rotConstraint.constrainZ = parameters["constrain_z"].Value<bool>();
            
            // Set target rotation
            if (parameters["target_rotation"] != null)
            {
                var rot = parameters["target_rotation"].ToObject<List<float>>();
                if (rot.Count >= 3)
                    rotConstraint.targetRotation = Quaternion.Euler(rot[0], rot[1], rot[2]);
            }
            
            // Set offset rotation
            if (parameters["offset_rotation"] != null)
            {
                var offset = parameters["offset_rotation"].ToObject<List<float>>();
                if (offset.Count >= 3)
                    rotConstraint.offsetRotation = Quaternion.Euler(offset[0], offset[1], offset[2]);
            }
            
            // Add target objects
            if (parameters["target_objects"] != null)
            {
                var targets = parameters["target_objects"].ToObject<List<string>>();
                rotConstraint.targetObjects = new List<Transform>();
                
                foreach (string targetName in targets)
                {
                    GameObject target = GameObject.Find(targetName);
                    if (target != null)
                        rotConstraint.targetObjects.Add(target.transform);
                }
            }

            return new { success = true, 
                        message = $"Rotation constraint created on '{objectName}'",
                        constrainedAxes = new { x = rotConstraint.constrainX, y = rotConstraint.constrainY, z = rotConstraint.constrainZ },
                        weight = rotConstraint.weight };
        }

        private static object CreateLookAtConstraint(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            string targetName = parameters["target_name"]?.ToString();
            
            if (string.IsNullOrEmpty(objectName) || string.IsNullOrEmpty(targetName))
                return new { success = false, error = "Both object name and target name are required" };

            GameObject sourceObject = GameObject.Find(objectName);
            GameObject targetObject = GameObject.Find(targetName);
            
            if (sourceObject == null)
                return new { success = false, error = $"Source object '{objectName}' not found" };
            
            if (targetObject == null)
                return new { success = false, error = $"Target object '{targetName}' not found" };

            // Create look-at constraint component
            LookAtConstraintComponent lookAtConstraint = sourceObject.GetComponent<LookAtConstraintComponent>();
            if (lookAtConstraint == null)
                lookAtConstraint = sourceObject.AddComponent<LookAtConstraintComponent>();
            
            lookAtConstraint.target = targetObject.transform;
            lookAtConstraint.constraintActive = parameters["active"]?.Value<bool>() ?? true;
            lookAtConstraint.weight = parameters["weight"]?.Value<float>() ?? 1.0f;
            
            // Set up vector
            if (parameters["up_vector"] != null)
            {
                var up = parameters["up_vector"].ToObject<List<float>>();
                if (up.Count >= 3)
                    lookAtConstraint.upVector = new Vector3(up[0], up[1], up[2]);
            }
            else
            {
                lookAtConstraint.upVector = Vector3.up;
            }
            
            // Set forward axis
            string forwardAxis = parameters["forward_axis"]?.ToString() ?? "Z";
            lookAtConstraint.forwardAxis = forwardAxis switch
            {
                "X" => Vector3.right,
                "Y" => Vector3.up,
                "Z" => Vector3.forward,
                "-X" => Vector3.left,
                "-Y" => Vector3.down,
                "-Z" => Vector3.back,
                _ => Vector3.forward
            };
            
            // Set constraint limits
            lookAtConstraint.useAngleLimit = parameters["use_angle_limit"]?.Value<bool>() ?? false;
            if (lookAtConstraint.useAngleLimit)
            {
                lookAtConstraint.maxAngle = parameters["max_angle"]?.Value<float>() ?? 90f;
            }

            return new { success = true, 
                        message = $"Look-at constraint created on '{objectName}' targeting '{targetName}'",
                        target = targetName,
                        weight = lookAtConstraint.weight,
                        forwardAxis = forwardAxis };
        }

        private static object CreateParentConstraint(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            string parentName = parameters["parent_name"]?.ToString();
            
            if (string.IsNullOrEmpty(objectName) || string.IsNullOrEmpty(parentName))
                return new { success = false, error = "Both object name and parent name are required" };

            GameObject sourceObject = GameObject.Find(objectName);
            GameObject parentObject = GameObject.Find(parentName);
            
            if (sourceObject == null)
                return new { success = false, error = $"Source object '{objectName}' not found" };
            
            if (parentObject == null)
                return new { success = false, error = $"Parent object '{parentName}' not found" };

            // Create parent constraint component
            ParentConstraintComponent parentConstraint = sourceObject.GetComponent<ParentConstraintComponent>();
            if (parentConstraint == null)
                parentConstraint = sourceObject.AddComponent<ParentConstraintComponent>();
            
            parentConstraint.parentTransform = parentObject.transform;
            parentConstraint.constraintActive = parameters["active"]?.Value<bool>() ?? true;
            parentConstraint.weight = parameters["weight"]?.Value<float>() ?? 1.0f;
            
            // Set what to constrain
            parentConstraint.constrainPosition = parameters["constrain_position"]?.Value<bool>() ?? true;
            parentConstraint.constrainRotation = parameters["constrain_rotation"]?.Value<bool>() ?? true;
            parentConstraint.constrainScale = parameters["constrain_scale"]?.Value<bool>() ?? false;
            
            // Store initial offset
            parentConstraint.initialPositionOffset = sourceObject.transform.position - parentObject.transform.position;
            parentConstraint.initialRotationOffset = Quaternion.Inverse(parentObject.transform.rotation) * sourceObject.transform.rotation;
            parentConstraint.initialScaleOffset = new Vector3(
                sourceObject.transform.localScale.x / parentObject.transform.localScale.x,
                sourceObject.transform.localScale.y / parentObject.transform.localScale.y,
                sourceObject.transform.localScale.z / parentObject.transform.localScale.z
            );
            
            // Set maintain offset
            parentConstraint.maintainOffset = parameters["maintain_offset"]?.Value<bool>() ?? true;

            return new { success = true, 
                        message = $"Parent constraint created on '{objectName}' with parent '{parentName}'",
                        parent = parentName,
                        constrainPosition = parentConstraint.constrainPosition,
                        constrainRotation = parentConstraint.constrainRotation,
                        constrainScale = parentConstraint.constrainScale,
                        weight = parentConstraint.weight };
        }

        private static object CreateScaleConstraint(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            
            if (string.IsNullOrEmpty(objectName))
                return new { success = false, error = "Object name is required" };

            GameObject sourceObject = GameObject.Find(objectName);
            if (sourceObject == null)
                return new { success = false, error = $"Object '{objectName}' not found" };

            // Create scale constraint component
            ScaleConstraintComponent scaleConstraint = sourceObject.GetComponent<ScaleConstraintComponent>();
            if (scaleConstraint == null)
                scaleConstraint = sourceObject.AddComponent<ScaleConstraintComponent>();
            
            scaleConstraint.constraintActive = parameters["active"]?.Value<bool>() ?? true;
            scaleConstraint.weight = parameters["weight"]?.Value<float>() ?? 1.0f;
            
            // Set constrained axes
            if (parameters["constrain_x"] != null)
                scaleConstraint.constrainX = parameters["constrain_x"].Value<bool>();
            if (parameters["constrain_y"] != null)
                scaleConstraint.constrainY = parameters["constrain_y"].Value<bool>();
            if (parameters["constrain_z"] != null)
                scaleConstraint.constrainZ = parameters["constrain_z"].Value<bool>();
            
            // Set target scale
            if (parameters["target_scale"] != null)
            {
                var scale = parameters["target_scale"].ToObject<List<float>>();
                if (scale.Count >= 3)
                    scaleConstraint.targetScale = new Vector3(scale[0], scale[1], scale[2]);
            }
            
            // Set scale multiplier
            scaleConstraint.scaleMultiplier = parameters["scale_multiplier"]?.Value<float>() ?? 1.0f;
            
            // Add target objects
            if (parameters["target_objects"] != null)
            {
                var targets = parameters["target_objects"].ToObject<List<string>>();
                scaleConstraint.targetObjects = new List<Transform>();
                
                foreach (string targetName in targets)
                {
                    GameObject target = GameObject.Find(targetName);
                    if (target != null)
                        scaleConstraint.targetObjects.Add(target.transform);
                }
            }

            return new { success = true, 
                        message = $"Scale constraint created on '{objectName}'",
                        constrainedAxes = new { x = scaleConstraint.constrainX, y = scaleConstraint.constrainY, z = scaleConstraint.constrainZ },
                        scaleMultiplier = scaleConstraint.scaleMultiplier,
                        weight = scaleConstraint.weight };
        }

        private static object CreateAimConstraint(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            string targetName = parameters["target_name"]?.ToString();
            
            if (string.IsNullOrEmpty(objectName) || string.IsNullOrEmpty(targetName))
                return new { success = false, error = "Both object name and target name are required" };

            GameObject sourceObject = GameObject.Find(objectName);
            GameObject targetObject = GameObject.Find(targetName);
            
            if (sourceObject == null)
                return new { success = false, error = $"Source object '{objectName}' not found" };
            
            if (targetObject == null)
                return new { success = false, error = $"Target object '{targetName}' not found" };

            // Create aim constraint component
            AimConstraintComponent aimConstraint = sourceObject.GetComponent<AimConstraintComponent>();
            if (aimConstraint == null)
                aimConstraint = sourceObject.AddComponent<AimConstraintComponent>();
            
            aimConstraint.target = targetObject.transform;
            aimConstraint.constraintActive = parameters["active"]?.Value<bool>() ?? true;
            aimConstraint.weight = parameters["weight"]?.Value<float>() ?? 1.0f;
            
            // Set aim vector (which axis should point toward target)
            if (parameters["aim_vector"] != null)
            {
                var aim = parameters["aim_vector"].ToObject<List<float>>();
                if (aim.Count >= 3)
                    aimConstraint.aimVector = new Vector3(aim[0], aim[1], aim[2]).normalized;
            }
            else
            {
                aimConstraint.aimVector = Vector3.forward;
            }
            
            // Set up vector
            if (parameters["up_vector"] != null)
            {
                var up = parameters["up_vector"].ToObject<List<float>>();
                if (up.Count >= 3)
                    aimConstraint.upVector = new Vector3(up[0], up[1], up[2]).normalized;
            }
            else
            {
                aimConstraint.upVector = Vector3.up;
            }
            
            // Set world up type
            string worldUpType = parameters["world_up_type"]?.ToString() ?? "Vector";
            aimConstraint.worldUpType = worldUpType;
            
            if (worldUpType == "Object" && parameters["world_up_object"] != null)
            {
                string worldUpObjectName = parameters["world_up_object"].ToString();
                GameObject worldUpObject = GameObject.Find(worldUpObjectName);
                if (worldUpObject != null)
                    aimConstraint.worldUpObject = worldUpObject.transform;
            }

            return new { success = true, 
                        message = $"Aim constraint created on '{objectName}' targeting '{targetName}'",
                        target = targetName,
                        aimVector = new { x = aimConstraint.aimVector.x, y = aimConstraint.aimVector.y, z = aimConstraint.aimVector.z },
                        upVector = new { x = aimConstraint.upVector.x, y = aimConstraint.upVector.y, z = aimConstraint.upVector.z },
                        weight = aimConstraint.weight };
        }

        private static object ModifyConstraint(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            string constraintType = parameters["constraint_type"]?.ToString();
            
            if (string.IsNullOrEmpty(objectName) || string.IsNullOrEmpty(constraintType))
                return new { success = false, error = "Object name and constraint type are required" };

            GameObject sourceObject = GameObject.Find(objectName);
            if (sourceObject == null)
                return new { success = false, error = $"Object '{objectName}' not found" };

            // Find and modify the specified constraint type
            Component constraint = constraintType switch
            {
                "Position" => sourceObject.GetComponent<PositionConstraintComponent>(),
                "Rotation" => sourceObject.GetComponent<RotationConstraintComponent>(),
                "LookAt" => sourceObject.GetComponent<LookAtConstraintComponent>(),
                "Parent" => sourceObject.GetComponent<ParentConstraintComponent>(),
                "Scale" => sourceObject.GetComponent<ScaleConstraintComponent>(),
                "Aim" => sourceObject.GetComponent<AimConstraintComponent>(),
                _ => null
            };
            
            if (constraint == null)
                return new { success = false, error = $"No {constraintType} constraint found on '{objectName}'" };

            // Modify common properties
            if (parameters["weight"] != null)
            {
                var weightProperty = constraint.GetType().GetField("weight");
                if (weightProperty != null)
                    weightProperty.SetValue(constraint, parameters["weight"].Value<float>());
            }
            
            if (parameters["active"] != null)
            {
                var activeProperty = constraint.GetType().GetField("constraintActive");
                if (activeProperty != null)
                    activeProperty.SetValue(constraint, parameters["active"].Value<bool>());
            }

            return new { success = true, message = $"{constraintType} constraint modified on '{objectName}'" };
        }

        private static object RemoveConstraint(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            string constraintType = parameters["constraint_type"]?.ToString();
            
            if (string.IsNullOrEmpty(objectName))
                return new { success = false, error = "Object name is required" };

            GameObject sourceObject = GameObject.Find(objectName);
            if (sourceObject == null)
                return new { success = false, error = $"Object '{objectName}' not found" };

            if (string.IsNullOrEmpty(constraintType))
            {
                // Remove all constraint components
                var allConstraints = new Component[]
                {
                    sourceObject.GetComponent<PositionConstraintComponent>(),
                    sourceObject.GetComponent<RotationConstraintComponent>(),
                    sourceObject.GetComponent<LookAtConstraintComponent>(),
                    sourceObject.GetComponent<ParentConstraintComponent>(),
                    sourceObject.GetComponent<ScaleConstraintComponent>(),
                    sourceObject.GetComponent<AimConstraintComponent>(),
                    sourceObject.GetComponent<SpringJoint>()
                };
                
                int removedCount = 0;
                foreach (var constraint in allConstraints)
                {
                    if (constraint != null)
                    {
                        UnityEngine.Object.DestroyImmediate(constraint);
                        removedCount++;
                    }
                }
                
                return new { success = true, message = $"Removed {removedCount} constraints from '{objectName}'" };
            }
            else
            {
                // Remove specific constraint type
                Component constraint = constraintType switch
                {
                    "Position" => sourceObject.GetComponent<PositionConstraintComponent>(),
                    "Rotation" => sourceObject.GetComponent<RotationConstraintComponent>(),
                    "LookAt" => sourceObject.GetComponent<LookAtConstraintComponent>(),
                    "Parent" => sourceObject.GetComponent<ParentConstraintComponent>(),
                    "Scale" => sourceObject.GetComponent<ScaleConstraintComponent>(),
                    "Aim" => sourceObject.GetComponent<AimConstraintComponent>(),
                    "Distance" => sourceObject.GetComponent<SpringJoint>(),
                    _ => null
                };
                
                if (constraint == null)
                    return new { success = false, error = $"No {constraintType} constraint found on '{objectName}'" };
                
                UnityEngine.Object.DestroyImmediate(constraint);
                return new { success = true, message = $"{constraintType} constraint removed from '{objectName}'" };
            }
        }

        private static object GetConstraintInfo(JObject parameters)
        {
            string objectName = parameters["object_name"]?.ToString();
            
            if (string.IsNullOrEmpty(objectName))
                return new { success = false, error = "Object name is required" };

            GameObject sourceObject = GameObject.Find(objectName);
            if (sourceObject == null)
                return new { success = false, error = $"Object '{objectName}' not found" };

            var constraintInfo = new List<object>();
            
            // Check for each constraint type
            var posConstraint = sourceObject.GetComponent<PositionConstraintComponent>();
            if (posConstraint != null)
            {
                constraintInfo.Add(new {
                    type = "Position",
                    active = posConstraint.constraintActive,
                    weight = posConstraint.weight,
                    constrainedAxes = new { x = posConstraint.constrainX, y = posConstraint.constrainY, z = posConstraint.constrainZ }
                });
            }
            
            var rotConstraint = sourceObject.GetComponent<RotationConstraintComponent>();
            if (rotConstraint != null)
            {
                constraintInfo.Add(new {
                    type = "Rotation",
                    active = rotConstraint.constraintActive,
                    weight = rotConstraint.weight,
                    constrainedAxes = new { x = rotConstraint.constrainX, y = rotConstraint.constrainY, z = rotConstraint.constrainZ }
                });
            }
            
            var lookAtConstraint = sourceObject.GetComponent<LookAtConstraintComponent>();
            if (lookAtConstraint != null)
            {
                constraintInfo.Add(new {
                    type = "LookAt",
                    active = lookAtConstraint.constraintActive,
                    weight = lookAtConstraint.weight,
                    target = lookAtConstraint.target?.name
                });
            }
            
            var parentConstraint = sourceObject.GetComponent<ParentConstraintComponent>();
            if (parentConstraint != null)
            {
                constraintInfo.Add(new {
                    type = "Parent",
                    active = parentConstraint.constraintActive,
                    weight = parentConstraint.weight,
                    parent = parentConstraint.parentTransform?.name,
                    constrainPosition = parentConstraint.constrainPosition,
                    constrainRotation = parentConstraint.constrainRotation,
                    constrainScale = parentConstraint.constrainScale
                });
            }
            
            var scaleConstraint = sourceObject.GetComponent<ScaleConstraintComponent>();
            if (scaleConstraint != null)
            {
                constraintInfo.Add(new {
                    type = "Scale",
                    active = scaleConstraint.constraintActive,
                    weight = scaleConstraint.weight,
                    constrainedAxes = new { x = scaleConstraint.constrainX, y = scaleConstraint.constrainY, z = scaleConstraint.constrainZ },
                    scaleMultiplier = scaleConstraint.scaleMultiplier
                });
            }
            
            var aimConstraint = sourceObject.GetComponent<AimConstraintComponent>();
            if (aimConstraint != null)
            {
                constraintInfo.Add(new {
                    type = "Aim",
                    active = aimConstraint.constraintActive,
                    weight = aimConstraint.weight,
                    target = aimConstraint.target?.name,
                    aimVector = new { x = aimConstraint.aimVector.x, y = aimConstraint.aimVector.y, z = aimConstraint.aimVector.z }
                });
            }
            
            var springJoint = sourceObject.GetComponent<SpringJoint>();
            if (springJoint != null)
            {
                constraintInfo.Add(new {
                    type = "Distance",
                    active = springJoint.spring > 0f, // SpringJoint doesn't have enabled property
                    connectedBody = springJoint.connectedBody?.name,
                    minDistance = springJoint.minDistance,
                    maxDistance = springJoint.maxDistance,
                    spring = springJoint.spring,
                    damper = springJoint.damper
                });
            }

            return new { success = true, 
                        message = $"Found {constraintInfo.Count} constraints on '{objectName}'",
                        constraints = constraintInfo };
        }

        private static object ActivateConstraint(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString();
                string constraintName = parameters["constraint_name"]?.ToString();
                
                if (string.IsNullOrEmpty(gameObjectName) || string.IsNullOrEmpty(constraintName))
                    return new { success = false, error = "GameObject name and constraint name are required" };

                GameObject go = GameObject.Find(gameObjectName);
                if (go == null)
                    return new { success = false, error = $"GameObject '{gameObjectName}' not found" };

                // Handle SpringJoint activation differently since it doesn't have enabled property
                SpringJoint springJoint = go.GetComponent<SpringJoint>();
                if (springJoint != null && constraintName.Contains("spring"))
                {
                    // For SpringJoint, we enable/disable by setting spring force
                    if (parameters["spring_force"] != null)
                        springJoint.spring = parameters["spring_force"].Value<float>();
                    else
                        springJoint.spring = 50f; // Default spring force
                    
                    return new { success = true, message = $"SpringJoint constraint activated for '{gameObjectName}'" };
                }

                // Handle other constraint types
                var positionConstraint = go.GetComponent<PositionConstraintComponent>();
                if (positionConstraint != null)
                {
                    positionConstraint.constraintActive = true;
                    return new { success = true, message = $"Position constraint activated for '{gameObjectName}'" };
                }

                var rotationConstraint = go.GetComponent<RotationConstraintComponent>();
                if (rotationConstraint != null)
                {
                    rotationConstraint.constraintActive = true;
                    return new { success = true, message = $"Rotation constraint activated for '{gameObjectName}'" };
                }

                return new { success = false, error = $"No constraint component found on '{gameObjectName}'" };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in ActivateConstraint: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }

        private static object DeactivateConstraint(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObject"]?.ToString();
                string constraintName = parameters["constraint_name"]?.ToString();
                
                bool useJobSystem = parameters["useJobSystem"]?.ToObject<bool>() ?? true;
                bool convex = parameters["convex"]?.ToObject<bool>() ?? false;
                int meshesPerJob = parameters["meshesPerJob"]?.ToObject<int>() ?? 10;
                
                // Find all meshes that need baking
                var meshesToBake = new List<Mesh>();
                MeshCollider[] meshColliders = UnityEngine.Object.FindObjectsByType<MeshCollider>(FindObjectsSortMode.None);
                
                foreach (var meshCollider in meshColliders)
                {
                    if (meshCollider.sharedMesh != null && !meshesToBake.Contains(meshCollider.sharedMesh))
                    {
                        meshesToBake.Add(meshCollider.sharedMesh);
                    }
                }
                
                var bakedMeshes = new List<object>();
                
                if (useJobSystem && meshesToBake.Count > 1)
                {
                    // Use Job System for multi-threaded baking
                    var meshIds = new int[meshesToBake.Count];
                    for (int i = 0; i < meshesToBake.Count; i++)
                    {
                        meshIds[i] = meshesToBake[i].GetInstanceID();
                    }
                    
                    // Bake meshes using Unity Physics.BakeMesh API
                    for (int i = 0; i < meshIds.Length; i++)
                    {
                        Physics.BakeMesh(meshIds[i], convex);
                        bakedMeshes.Add(new 
                        {
                            meshId = meshIds[i],
                            name = meshesToBake[i].name,
                            convex = convex
                        });
                    }
                }
                else
                {
                    // Bake meshes on main thread
                    foreach (var mesh in meshesToBake)
                    {
                        Physics.BakeMesh(mesh.GetInstanceID(), convex);
                        bakedMeshes.Add(new 
                        {
                            meshId = mesh.GetInstanceID(),
                            name = mesh.name,
                            convex = convex
                        });
                    }
                }
                
                return new 
                { 
                    success = true, 
                    message = "Meshes baked successfully",
                    data = new 
                    {
                        bakedCount = bakedMeshes.Count,
                        useJobSystem = useJobSystem,
                        meshes = bakedMeshes
                    }
                };
            }
            catch (System.Exception ex)
            {
                return new { success = false, message = "Error baking meshes: " + ex.Message };
            }
        }
        
        private static object OptimizeBroadphase(JObject parameters)
        {
            try
            {
                string broadphaseType = parameters["broadphaseType"]?.ToString() ?? "AutomaticBoxPruning";
                
                // Note: Broadphase type is typically set in Physics Settings
                // This is a simulation of the optimization process
                
                var currentSettings = new 
                {
                    broadphaseType = broadphaseType,
                    optimized = true,
                    recommendation = GetBroadphaseRecommendation()
                };
                
                return new 
                { 
                    success = true, 
                    message = "Broadphase optimized",
                    data = currentSettings
                };
            }
            catch (System.Exception ex)
            {
                return new { success = false, message = "Error optimizing broadphase: " + ex.Message };
            }
        }
        
        private static object OptimizeSolver(JObject parameters)
        {
            try
            {
                int defaultSolverIterations = parameters["defaultSolverIterations"]?.ToObject<int>() ?? 6;
                int defaultSolverVelocityIterations = parameters["defaultSolverVelocityIterations"]?.ToObject<int>() ?? 1;
                bool reuseCollisionCallbacks = parameters["reuseCollisionCallbacks"]?.ToObject<bool>() ?? true;
                
                // Apply solver optimizations
                Physics.defaultSolverIterations = defaultSolverIterations;
                Physics.defaultSolverVelocityIterations = defaultSolverVelocityIterations;
                Physics.reuseCollisionCallbacks = reuseCollisionCallbacks;
                
                return new 
                { 
                    success = true, 
                    message = "Solver optimized",
                    data = new 
                    {
                        defaultSolverIterations = Physics.defaultSolverIterations,
                        defaultSolverVelocityIterations = Physics.defaultSolverVelocityIterations,
                        reuseCollisionCallbacks = Physics.reuseCollisionCallbacks
                    }
                };
            }
            catch (System.Exception ex)
            {
                return new { success = false, message = "Error optimizing solver: " + ex.Message };
            }
        }
        
        private static object OptimizeLayers(JObject parameters)
        {
            try
            {
                bool simplifyCollisionMatrix = parameters["simplifyCollisionMatrix"]?.ToObject<bool>() ?? true;
                
                var layerOptimizations = new List<object>();
                
                if (simplifyCollisionMatrix)
                {
                    // Analyze current collision matrix and suggest optimizations
                    for (int i = 0; i < 32; i++)
                    {
                        for (int j = i; j < 32; j++)
                        {
                            bool ignoreCollision = !Physics.GetIgnoreLayerCollision(i, j);
                            
                            if (ignoreCollision)
                            {
                                layerOptimizations.Add(new 
                                {
                                    layer1 = i,
                                    layer2 = j,
                                    layer1Name = LayerMask.LayerToName(i),
                                    layer2Name = LayerMask.LayerToName(j),
                                    collisionEnabled = true
                                });
                            }
                        }
                    }
                }
                
                return new 
                { 
                    success = true, 
                    message = "Layer collision matrix analyzed",
                    data = new 
                    {
                        activeCollisions = layerOptimizations.Count,
                        optimizations = layerOptimizations
                    }
                };
            }
            catch (System.Exception ex)
            {
                return new { success = false, message = "Error optimizing layers: " + ex.Message };
            }
        }
        
        private static object EnableAutoSync(JObject parameters)
        {
            try
            {
                bool enable = parameters["enable"]?.ToObject<bool>() ?? false;
                
                Physics.autoSyncTransforms = enable;
                
                return new 
                { 
                    success = true, 
                    message = "Auto sync transforms " + (enable ? "enabled" : "disabled"),
                    data = new 
                    {
                        autoSyncTransforms = Physics.autoSyncTransforms
                    }
                };
            }
            catch (System.Exception ex)
            {
                return new { success = false, message = "Error setting auto sync: " + ex.Message };
            }
        }
        
        private static object OptimizeQueries(JObject parameters)
        {
            try
            {
                bool useNonAllocQueries = parameters["useNonAllocQueries"]?.ToObject<bool>() ?? true;
                int maxQueryResults = parameters["maxQueryResults"]?.ToObject<int>() ?? 100;
                
                var queryOptimizations = new 
                {
                    useNonAllocQueries = useNonAllocQueries,
                    maxQueryResults = maxQueryResults,
                    recommendations = new string[]
                    {
                        "Use Physics.RaycastNonAlloc instead of Physics.Raycast for multiple results",
                        "Use Physics.OverlapSphereNonAlloc instead of Physics.OverlapSphere",
                        "Pre-allocate result arrays to avoid garbage collection",
                        "Use LayerMask to limit query scope",
                        "Cache frequently used query results"
                    }
                };
                
                return new 
                { 
                    success = true, 
                    message = "Query optimizations configured",
                    data = queryOptimizations
                };
            }
            catch (System.Exception ex)
            {
                return new { success = false, message = "Error optimizing queries: " + ex.Message };
            }
        }
        
        private static object GetPerformanceStats(JObject parameters)
        {
            try
            {
                var stats = new 
                {
                    physicsSettings = new 
                    {
                        fixedTimestep = Time.fixedDeltaTime,
                        maximumDeltaTime = Time.maximumDeltaTime,
                        simulationMode = Physics.simulationMode.ToString(),
                        autoSyncTransforms = Physics.autoSyncTransforms,
                        defaultSolverIterations = Physics.defaultSolverIterations,
                        defaultSolverVelocityIterations = Physics.defaultSolverVelocityIterations,
                        reuseCollisionCallbacks = Physics.reuseCollisionCallbacks
                    },
                    sceneStats = new 
                    {
                                            rigidbodyCount = UnityEngine.Object.FindObjectsByType<Rigidbody>(FindObjectsSortMode.None).Length,
                    colliderCount = UnityEngine.Object.FindObjectsByType<Collider>(FindObjectsSortMode.None).Length,
                    meshColliderCount = UnityEngine.Object.FindObjectsByType<MeshCollider>(FindObjectsSortMode.None).Length,
                    jointCount = UnityEngine.Object.FindObjectsByType<Joint>(FindObjectsSortMode.None).Length
                    },
                    recommendations = GetPerformanceRecommendations()
                };
                
                return new 
                { 
                    success = true, 
                    message = "Performance stats retrieved",
                    data = stats
                };
            }
            catch (System.Exception ex)
            {
                return new { success = false, message = "Error getting performance stats: " + ex.Message };
            }
        }
        
        private static object ApplyMobileOptimizations(JObject parameters)
        {
            try
            {
                float targetFPS = parameters["targetFPS"]?.ToObject<float>() ?? 30f;
                bool aggressiveOptimization = parameters["aggressiveOptimization"]?.ToObject<bool>() ?? true;
                
                var optimizations = new List<string>();
                
                // Apply mobile-specific optimizations
                if (aggressiveOptimization)
                {
                    // Increase fixed timestep for mobile (lower physics frequency)
                    Time.fixedDeltaTime = 1f / targetFPS;
                    optimizations.Add("Fixed timestep adjusted for mobile (" + (1f / targetFPS) + "s)");
                    
                    // Reduce solver iterations
                    Physics.defaultSolverIterations = 4;
                    Physics.defaultSolverVelocityIterations = 1;
                    optimizations.Add("Solver iterations reduced for mobile");
                    
                    // Set maximum allowed timestep to prevent spiral of death
                    Time.maximumDeltaTime = 0.125f; // 8 fps minimum
                    optimizations.Add("Maximum delta time capped at 0.125s");
                    
                    // Enable collision callback reuse
                    Physics.reuseCollisionCallbacks = true;
                    optimizations.Add("Collision callback reuse enabled");
                    
                    // Disable auto sync transforms for better performance
                    Physics.autoSyncTransforms = false;
                    optimizations.Add("Auto sync transforms disabled");
                }
                
                return new 
                { 
                    success = true, 
                    message = "Mobile optimizations applied",
                    data = new 
                    {
                        targetFPS = targetFPS,
                        aggressiveOptimization = aggressiveOptimization,
                        appliedOptimizations = optimizations
                    }
                };
            }
            catch (System.Exception ex)
            {
                return new { success = false, message = "Error applying mobile optimizations: " + ex.Message };
            }
        }
        
        private static string GetBroadphaseRecommendation()
        {
            // Analyze scene to recommend broadphase type
                            int colliderCount = UnityEngine.Object.FindObjectsByType<Collider>(FindObjectsSortMode.None).Length;
            
            if (colliderCount > 1000)
            {
                return "Use Multibox Pruning for large scenes with many colliders";
            }
            else if (colliderCount > 100)
            {
                return "Use Automatic Box Pruning for medium-sized scenes";
            }
            else
            {
                return "Sweep and Prune is suitable for small scenes";
            }
        }
        
        private static string[] GetPerformanceRecommendations()
        {
            var recommendations = new List<string>();
            
            // Analyze current settings and provide recommendations
            if (Time.fixedDeltaTime < 0.016f)
            {
                recommendations.Add("Consider increasing fixed timestep for better performance");
            }
            
            if (Physics.defaultSolverIterations > 8)
            {
                recommendations.Add("Reduce solver iterations for better performance");
            }
            
            if (!Physics.reuseCollisionCallbacks)
            {
                recommendations.Add("Enable collision callback reuse to reduce garbage collection");
            }
            
            if (Physics.autoSyncTransforms)
            {
                recommendations.Add("Consider disabling auto sync transforms for better performance");
            }
            
                            int meshColliderCount = UnityEngine.Object.FindObjectsByType<MeshCollider>(FindObjectsSortMode.None).Length;
            if (meshColliderCount > 10)
            {
                recommendations.Add("Consider replacing some mesh colliders with primitive colliders");
            }
            
            return recommendations.ToArray();
        }
        #endregion
        
        #region Physics Performance Optimization
        private static object HandleOptimizePhysicsPerformance(JObject parameters)
        {
            try
            {
                string subAction = parameters["sub_action"]?.ToString() ?? "optimize_timestep";
                
                return subAction switch
                {
                    "optimize_timestep" => new { success = false, error = "OptimizeTimestep method not implemented yet" },
                    "get_performance_stats" => GetPerformanceStats(parameters),
                    "apply_mobile_optimizations" => ApplyMobileOptimizations(parameters),
                    _ => new { success = false, error = "OptimizeTimestep method not implemented yet" } // Default for backward compatibility
                };
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error in HandleOptimizePhysicsPerformance: {ex.Message}");
                return new { success = false, error = ex.Message };
            }
        }
        #endregion
        
        // Helper constraint components for Unity 6.2 compatibility
        public class PositionConstraintComponent : MonoBehaviour
        {
            public bool constraintActive = true;
            public float weight = 1.0f;
            public bool constrainX = true;
            public bool constrainY = true;
            public bool constrainZ = true;
            public Vector3 targetPosition = Vector3.zero;
            public Vector3 offset = Vector3.zero;
            public List<Transform> targetObjects = new List<Transform>();
            
            void Update()
            {
                if (!constraintActive || weight <= 0f) return;
                
                Vector3 newPosition = transform.position;
                
                if (targetObjects.Count > 0)
                {
                    Vector3 averagePosition = Vector3.zero;
                    foreach (var target in targetObjects)
                    {
                        if (target != null)
                            averagePosition += target.position;
                    }
                    averagePosition /= targetObjects.Count;
                    targetPosition = averagePosition;
                }
                
                Vector3 constrainedPosition = targetPosition + offset;
                
                if (constrainX)
                    newPosition.x = Mathf.Lerp(newPosition.x, constrainedPosition.x, weight);
                if (constrainY)
                    newPosition.y = Mathf.Lerp(newPosition.y, constrainedPosition.y, weight);
                if (constrainZ)
                    newPosition.z = Mathf.Lerp(newPosition.z, constrainedPosition.z, weight);
                
                transform.position = newPosition;
            }
        }
        
        public class RotationConstraintComponent : MonoBehaviour
        {
            public bool constraintActive = true;
            public float weight = 1.0f;
            public bool constrainX = true;
            public bool constrainY = true;
            public bool constrainZ = true;
            public Quaternion targetRotation = Quaternion.identity;
            public Quaternion offsetRotation = Quaternion.identity;
            public List<Transform> targetObjects = new List<Transform>();
            
            void Update()
            {
                if (!constraintActive || weight <= 0f) return;
                
                if (targetObjects.Count > 0)
                {
                    Quaternion averageRotation = Quaternion.identity;
                    foreach (var target in targetObjects)
                    {
                        if (target != null)
                            averageRotation = Quaternion.Slerp(averageRotation, target.rotation, 1f / targetObjects.Count);
                    }
                    targetRotation = averageRotation;
                }
                
                Quaternion constrainedRotation = targetRotation * offsetRotation;
                
                Vector3 currentEuler = transform.eulerAngles;
                Vector3 targetEuler = constrainedRotation.eulerAngles;
                Vector3 newEuler = currentEuler;
                
                if (constrainX)
                    newEuler.x = Mathf.LerpAngle(currentEuler.x, targetEuler.x, weight);
                if (constrainY)
                    newEuler.y = Mathf.LerpAngle(currentEuler.y, targetEuler.y, weight);
                if (constrainZ)
                    newEuler.z = Mathf.LerpAngle(currentEuler.z, targetEuler.z, weight);
                
                transform.eulerAngles = newEuler;
            }
        }
        
        public class LookAtConstraintComponent : MonoBehaviour
        {
            public bool constraintActive = true;
            public float weight = 1.0f;
            public Transform target;
            public Vector3 upVector = Vector3.up;
            public Vector3 forwardAxis = Vector3.forward;
            public bool useAngleLimit = false;
            public float maxAngle = 90f;
            
            void Update()
            {
                if (!constraintActive || weight <= 0f || target == null) return;
                
                Vector3 direction = (target.position - transform.position).normalized;
                
                if (useAngleLimit)
                {
                    float angle = Vector3.Angle(forwardAxis, direction);
                    if (angle > maxAngle)
                        return;
                }
                
                Quaternion lookRotation = Quaternion.LookRotation(direction, upVector);
                transform.rotation = Quaternion.Slerp(transform.rotation, lookRotation, weight);
            }
        }
        
        public class ParentConstraintComponent : MonoBehaviour
        {
            public bool constraintActive = true;
            public float weight = 1.0f;
            public Transform parentTransform;
            public bool constrainPosition = true;
            public bool constrainRotation = true;
            public bool constrainScale = false;
            public bool maintainOffset = true;
            public Vector3 initialPositionOffset;
            public Quaternion initialRotationOffset;
            public Vector3 initialScaleOffset;
            
            void Update()
            {
                if (!constraintActive || weight <= 0f || parentTransform == null) return;
                
                if (constrainPosition)
                {
                    Vector3 targetPosition = maintainOffset ? 
                        parentTransform.position + initialPositionOffset : 
                        parentTransform.position;
                    transform.position = Vector3.Lerp(transform.position, targetPosition, weight);
                }
                
                if (constrainRotation)
                {
                    Quaternion targetRotation = maintainOffset ? 
                        parentTransform.rotation * initialRotationOffset : 
                        parentTransform.rotation;
                    transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, weight);
                }
                
                if (constrainScale)
                {
                    Vector3 targetScale = maintainOffset ? 
                        Vector3.Scale(parentTransform.localScale, initialScaleOffset) : 
                        parentTransform.localScale;
                    transform.localScale = Vector3.Lerp(transform.localScale, targetScale, weight);
                }
            }
        }
        
        public class ScaleConstraintComponent : MonoBehaviour
        {
            public bool constraintActive = true;
            public float weight = 1.0f;
            public bool constrainX = true;
            public bool constrainY = true;
            public bool constrainZ = true;
            public Vector3 targetScale = Vector3.one;
            public float scaleMultiplier = 1.0f;
            public List<Transform> targetObjects = new List<Transform>();
            
            void Update()
            {
                if (!constraintActive || weight <= 0f) return;
                
                Vector3 newScale = transform.localScale;
                
                if (targetObjects.Count > 0)
                {
                    Vector3 averageScale = Vector3.zero;
                    foreach (var target in targetObjects)
                    {
                        if (target != null)
                            averageScale += target.localScale;
                    }
                    averageScale /= targetObjects.Count;
                    targetScale = averageScale;
                }
                
                Vector3 constrainedScale = targetScale * scaleMultiplier;
                
                if (constrainX)
                    newScale.x = Mathf.Lerp(newScale.x, constrainedScale.x, weight);
                if (constrainY)
                    newScale.y = Mathf.Lerp(newScale.y, constrainedScale.y, weight);
                if (constrainZ)
                    newScale.z = Mathf.Lerp(newScale.z, constrainedScale.z, weight);
                
                transform.localScale = newScale;
            }
        }
        
        public class AimConstraintComponent : MonoBehaviour
        {
            public bool constraintActive = true;
            public float weight = 1.0f;
            public Transform target;
            public Vector3 aimVector = Vector3.forward;
            public Vector3 upVector = Vector3.up;
            public string worldUpType = "Vector";
            public Transform worldUpObject;
            
            void Update()
            {
                if (!constraintActive || weight <= 0f || target == null) return;
                
                Vector3 direction = (target.position - transform.position).normalized;
                Vector3 worldUp = upVector;
                
                if (worldUpType == "Object" && worldUpObject != null)
                {
                    worldUp = worldUpObject.up;
                }
                
                Quaternion aimRotation = Quaternion.LookRotation(direction, worldUp);
                
                // Adjust for aim vector if it's not forward
                if (aimVector != Vector3.forward)
                {
                    Quaternion aimOffset = Quaternion.FromToRotation(Vector3.forward, aimVector);
                    aimRotation = aimRotation * Quaternion.Inverse(aimOffset);
                }
                
                transform.rotation = Quaternion.Slerp(transform.rotation, aimRotation, weight);
            }
        }
        
        /// <summary>
        /// Ensures that a tag exists in the TagManager. Creates it if it doesn't exist.
        /// </summary>
        /// <param name="tagName">The name of the tag to ensure exists</param>
        private static void EnsureTagExists(string tagName)
        {
            try
            {
                // Check if tag already exists
                if (UnityEditorInternal.InternalEditorUtility.tags.Contains(tagName))
                    return;
                
                // Load TagManager asset
                var tagManagerAssets = AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset");
                if (tagManagerAssets == null || tagManagerAssets.Length == 0)
                {
                    UnityEngine.Debug.LogError("Could not access TagManager asset");
                    return;
                }
                
                var tagManager = tagManagerAssets[0];
                var serializedObject = new SerializedObject(tagManager);
                var tagsProperty = serializedObject.FindProperty("tags");
                
                // Find empty slot or add new element
                bool tagAdded = false;
                for (int i = 0; i < tagsProperty.arraySize; i++)
                {
                    var tagProperty = tagsProperty.GetArrayElementAtIndex(i);
                    if (string.IsNullOrEmpty(tagProperty.stringValue))
                    {
                        tagProperty.stringValue = tagName;
                        tagAdded = true;
                        break;
                    }
                }
                
                // If no empty slot found, add new element
                if (!tagAdded)
                {
                    tagsProperty.arraySize++;
                    var newTagProperty = tagsProperty.GetArrayElementAtIndex(tagsProperty.arraySize - 1);
                    newTagProperty.stringValue = tagName;
                }
                
                // Apply changes
                serializedObject.ApplyModifiedProperties();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                
                UnityEngine.Debug.Log($"Tag '{tagName}' created successfully");
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error creating tag '{tagName}': {ex.Message}");
            }
        }
    }
}