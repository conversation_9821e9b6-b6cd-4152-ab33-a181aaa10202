using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles operations for lighting and rendering tools.
    /// </summary>
    // Force recompilation - updated for Unity 6.2 LightingSettings - v3
    public static class LightingRendering
    {
        /// <summary>
        /// Main handler for lighting and rendering actions.
        /// </summary>
        public static object HandleCommand(JObject @params)
        {
            UnityEngine.Debug.LogError($"[CRITICAL DEBUG] HandleCommand called with params: {@params}");
            string toolType = @params["tool_type"]?.ToString()?.ToLower();
            string action = @params["action"]?.ToString()?.ToLower();
            
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            try
            {
                return toolType switch
                {
                    "light_probes" => HandleLightProbes(@params),
                    "reflection_probes" => HandleReflectionProbes(@params),
                    "lightmaps" => HandleLightmaps(@params),
                    "realtime_gi" => HandleRealtimeGI(@params),
                    "volumetric_lighting" => HandleVolumetricLighting(@params),
                    "shadow_cascades" => HandleShadowCascades(@params),
                    "light_cookies" => HandleLightCookies(@params),
                    "hdr_pipeline" => HandleHDRPipeline(@params),
                    "post_processing" => HandlePostProcessing(@params),
                    "bloom_effects" => HandleBloomEffects(@params),
                    "color_grading" => HandleColorGrading(@params),
                    "depth_of_field" => HandleDepthOfField(@params),
                    "motion_blur" => HandleMotionBlur(@params),
                    "screen_space_reflections" => HandleScreenSpaceReflections(@params),
                    "ambient_occlusion" => HandleAmbientOcclusion(@params),
                    "fog_effects" => HandleFogEffects(@params),
                    "caustics" => HandleCaustics(@params),
                    "light_shafts" => HandleLightShafts(@params),
                    "optimize_rendering_performance" => HandleOptimizeRenderingPerformance(@params),
                    _ => HandleGlobalIllumination(@params) // Default to GI for backward compatibility
                };
            }
            catch (Exception e)
            {
                Debug.LogError($"Error in LightingRendering: {e.Message}");
                return Response.Error($"Error executing lighting/rendering operation: {e.Message}");
            }
        }

        private static object HandleGlobalIllumination(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "setup":
                        return SetupGlobalIllumination(@params);
                    case "bake":
                        return BakeGlobalIllumination(@params);
                    case "clear":
                        return ClearGlobalIllumination();
                    default:
                        return Response.Error($"Unknown GI action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Global Illumination {action}: {e.Message}");
            }
        }

        private static object SetupGlobalIllumination(JObject @params)
        {
            string giMode = @params["gi_mode"]?.ToString() ?? "baked";
            int bounces = @params["bounces"]?.ToObject<int>() ?? 2;
            int resolution = @params["resolution"]?.ToObject<int>() ?? 40;
            bool enableAO = @params["enable_ao"]?.ToObject<bool>() ?? true;
            float aoDistance = @params["ao_distance"]?.ToObject<float>() ?? 1.0f;
            bool compressLightmaps = @params["compress_lightmaps"]?.ToObject<bool>() ?? true;

            // Configure Lightmap Settings using LightingSettings (Unity 6.2 approach)
            UnityEngine.Debug.Log("[DEBUG] Starting SetupLightmaps - checking lightingSettings");
            var lightingSettings = Lightmapping.lightingSettings;
            UnityEngine.Debug.Log($"[DEBUG] Current lightingSettings: {(lightingSettings == null ? "NULL" : "EXISTS")}");
            if (lightingSettings == null)
            {
                UnityEngine.Debug.Log("[DEBUG] Creating new LightingSettings object");
                lightingSettings = new LightingSettings();
                Lightmapping.lightingSettings = lightingSettings;
                UnityEngine.Debug.Log("[DEBUG] Assigned new LightingSettings to Lightmapping.lightingSettings");
            }
            
            lightingSettings.maxBounces = bounces;
            lightingSettings.indirectResolution = resolution;
            lightingSettings.lightmapResolution = resolution;
            lightingSettings.ao = enableAO;
            lightingSettings.aoMaxDistance = aoDistance;
            lightingSettings.lightmapCompression = compressLightmaps ? LightmapCompression.LowQuality : LightmapCompression.None;

            // Set GI mode
            switch (giMode)
            {
                case "baked":
                    lightingSettings.bakedGI = true;
                    lightingSettings.realtimeGI = false;
                    break;
                case "realtime":
                    lightingSettings.realtimeGI = true;
                    lightingSettings.bakedGI = false;
                    break;
                case "mixed":
                    lightingSettings.realtimeGI = true;
                    lightingSettings.bakedGI = true;
                    break;
            }

            var data = new
            {
                gi_mode = giMode,
                bounces = bounces,
                resolution = resolution,
                enable_ao = enableAO,
                ao_distance = aoDistance,
                compress_lightmaps = compressLightmaps
            };

            return Response.Success("Global Illumination configured successfully.", data);
        }

        private static object BakeGlobalIllumination(JObject @params)
        {
            try
            {
                Lightmapping.BakeAsync();
                return Response.Success("Global Illumination baking started.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to start GI baking: {e.Message}");
            }
        }

        private static object ClearGlobalIllumination()
        {
            try
            {
                Lightmapping.Clear();
                return Response.Success("Global Illumination cleared successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to clear GI: {e.Message}");
            }
        }

        private static object HandleLightProbes(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "create":
                        return CreateLightProbes(@params);
                    case "generate_grid":
                        return GenerateLightProbeGrid(@params);
                    case "delete":
                        return DeleteLightProbes(@params);
                    case "bake":
                        return BakeLightProbes();
                    default:
                        return Response.Error($"Unknown light probes action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Light Probes {action}: {e.Message}");
            }
        }

        private static object CreateLightProbes(JObject @params)
        {
            string probeGroupName = @params["probe_group_name"]?.ToString() ?? "LightProbeGroup";
            var positions = @params["positions"]?.ToObject<float[][]>();

            GameObject probeGroupObj = new GameObject(probeGroupName);
            LightProbeGroup probeGroup = probeGroupObj.AddComponent<LightProbeGroup>();

            if (positions != null && positions.Length > 0)
            {
                Vector3[] probePositions = new Vector3[positions.Length];
                for (int i = 0; i < positions.Length; i++)
                {
                    if (positions[i].Length >= 3)
                    {
                        probePositions[i] = new Vector3(positions[i][0], positions[i][1], positions[i][2]);
                    }
                }
                probeGroup.probePositions = probePositions;
            }

            var data = new
            {
                name = probeGroupName,
                probe_count = probeGroup.probePositions.Length,
                positions = positions
            };

            return Response.Success($"Light probe group '{probeGroupName}' created with {probeGroup.probePositions.Length} probes.", data);
        }

        private static object GenerateLightProbeGrid(JObject @params)
        {
            string probeGroupName = @params["probe_group_name"]?.ToString() ?? "LightProbeGrid";
            float spacing = @params["spacing"]?.ToObject<float>() ?? 5.0f;
            float height = @params["height"]?.ToObject<float>() ?? 1.8f;
            var boundsMin = @params["bounds_min"]?.ToObject<float[]>();
            var boundsMax = @params["bounds_max"]?.ToObject<float[]>();

            if (boundsMin == null || boundsMax == null || boundsMin.Length < 3 || boundsMax.Length < 3)
            {
                return Response.Error("bounds_min and bounds_max are required for grid generation");
            }

            Vector3 min = new Vector3(boundsMin[0], boundsMin[1], boundsMin[2]);
            Vector3 max = new Vector3(boundsMax[0], boundsMax[1], boundsMax[2]);

            List<Vector3> probePositions = new List<Vector3>();

            for (float x = min.x; x <= max.x; x += spacing)
            {
                for (float z = min.z; z <= max.z; z += spacing)
                {
                    probePositions.Add(new Vector3(x, height, z));
                }
            }

            GameObject probeGroupObj = new GameObject(probeGroupName);
            LightProbeGroup probeGroup = probeGroupObj.AddComponent<LightProbeGroup>();
            probeGroup.probePositions = probePositions.ToArray();

            var data = new
            {
                name = probeGroupName,
                probe_count = probePositions.Count,
                spacing = spacing,
                height = height,
                bounds_min = boundsMin,
                bounds_max = boundsMax
            };

            return Response.Success($"Light probe grid '{probeGroupName}' generated with {probePositions.Count} probes.", data);
        }

        private static object DeleteLightProbes(JObject @params)
        {
            string probeGroupName = @params["probe_group_name"]?.ToString();
            
            if (string.IsNullOrEmpty(probeGroupName))
            {
                // Delete all light probe groups
                LightProbeGroup[] allProbeGroups = UnityEngine.Object.FindObjectsByType<LightProbeGroup>(FindObjectsSortMode.None);
                foreach (var group in allProbeGroups)
                {
                    UnityEngine.Object.DestroyImmediate(group.gameObject);
                }
                return Response.Success($"Deleted {allProbeGroups.Length} light probe groups.");
            }
            else
            {
                // Delete specific light probe group
                GameObject probeGroupObj = GameObject.Find(probeGroupName);
                if (probeGroupObj != null && probeGroupObj.GetComponent<LightProbeGroup>() != null)
                {
                    UnityEngine.Object.DestroyImmediate(probeGroupObj);
                    return Response.Success($"Light probe group '{probeGroupName}' deleted successfully.");
                }
                else
                {
                    return Response.Error($"Light probe group '{probeGroupName}' not found.");
                }
            }
        }

        private static object BakeLightProbes()
        {
            try
            {
                Lightmapping.BakeAsync();
                return Response.Success("Light probe baking started.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to bake light probes: {e.Message}");
            }
        }

        private static object HandleReflectionProbes(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "create":
                        return CreateReflectionProbe(@params);
                    case "bake":
                        return BakeReflectionProbes();
                    case "delete":
                        return DeleteReflectionProbes(@params);
                    case "configure":
                        return ConfigureReflectionProbe(@params);
                    default:
                        return Response.Error($"Unknown reflection probes action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Reflection Probes {action}: {e.Message}");
            }
        }

        private static object CreateReflectionProbe(JObject @params)
        {
            var position = @params["position"]?.ToObject<float[]>();
            var size = @params["size"]?.ToObject<float[]>();
            int resolution = @params["resolution"]?.ToObject<int>() ?? 128;
            bool hdr = @params["hdr"]?.ToObject<bool>() ?? true;
            bool boxProjection = @params["box_projection"]?.ToObject<bool>() ?? true;

            GameObject probeObj = new GameObject("ReflectionProbe");
            ReflectionProbe probe = probeObj.AddComponent<ReflectionProbe>();

            if (position != null && position.Length >= 3)
            {
                probeObj.transform.position = new Vector3(position[0], position[1], position[2]);
            }

            if (size != null && size.Length >= 3)
            {
                probe.size = new Vector3(size[0], size[1], size[2]);
            }

            probe.resolution = resolution;
            probe.hdr = hdr;
            probe.boxProjection = boxProjection;

            var data = new
            {
                name = probeObj.name,
                position = position,
                size = size,
                resolution = resolution,
                hdr = hdr,
                box_projection = boxProjection
            };

            return Response.Success("Reflection probe created successfully.", data);
        }

        private static object BakeReflectionProbes()
        {
            try
            {
                Lightmapping.BakeAsync();
                return Response.Success("Reflection probe baking started.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to bake reflection probes: {e.Message}");
            }
        }

        private static object DeleteReflectionProbes(JObject @params)
        {
            ReflectionProbe[] allProbes = UnityEngine.Object.FindObjectsByType<ReflectionProbe>(FindObjectsSortMode.None);
            foreach (var probe in allProbes)
            {
                UnityEngine.Object.DestroyImmediate(probe.gameObject);
            }
            return Response.Success($"Deleted {allProbes.Length} reflection probes.");
        }

        /// <summary>
        /// [UNITY 6.2] - Configura Reflection Probes usando APIs oficiais.
        /// </summary>
        private static object ConfigureReflectionProbe(JObject @params)
        {
            try
            {
                string action = @params["action"]?.ToString()?.ToLower() ?? "configure";
                string probeName = @params["probe_name"]?.ToString() ?? "ReflectionProbe";
                Vector3 position = ParseVector3(@params["position"]) ?? Vector3.zero;
                Vector3 size = ParseVector3(@params["size"]) ?? Vector3.one * 10f;
                int resolution = @params["resolution"]?.ToObject<int>() ?? 128;
                ReflectionProbeMode mode = ParseReflectionProbeMode(@params["mode"]?.ToString()) ?? ReflectionProbeMode.Baked;
                ReflectionProbeRefreshMode refreshMode = ParseRefreshMode(@params["refresh_mode"]?.ToString()) ?? ReflectionProbeRefreshMode.OnAwake;
                bool hdr = @params["hdr"]?.ToObject<bool>() ?? true;
                float intensity = @params["intensity"]?.ToObject<float>() ?? 1.0f;

                switch (action)
                {
                    case "create":
                        return CreateReflectionProbe(probeName, position, size, resolution, mode, refreshMode, hdr, intensity);
                    case "configure":
                        return ConfigureExistingReflectionProbe(probeName, @params);
                    case "bake":
                        return BakeReflectionProbe(probeName);
                    case "optimize":
                        return OptimizeReflectionProbes(@params);
                    default:
                        return Response.Error($"Unknown reflection probe action: {action}");
                }
            }
            catch (Exception e)
            {
                LogOperation("ConfigureReflectionProbe", $"Error: {e.Message}", true);
                return Response.Error($"Failed to configure reflection probe: {e.Message}");
            }
        }

        /// <summary>
        /// [HELPER] - Cria novo Reflection Probe.
        /// </summary>
        private static object CreateReflectionProbe(string name, Vector3 position, Vector3 size, int resolution,
            ReflectionProbeMode mode, ReflectionProbeRefreshMode refreshMode, bool hdr, float intensity)
        {
            GameObject probeObject = new GameObject(name);
            probeObject.transform.position = position;

            ReflectionProbe probe = probeObject.AddComponent<ReflectionProbe>();
            probe.size = size;
            probe.resolution = resolution;
            probe.mode = mode;
            probe.refreshMode = refreshMode;
            probe.hdr = hdr;
            probe.intensity = intensity;
            probe.boxProjection = true;
            probe.blendDistance = size.magnitude * 0.1f;

            // Configurações Unity 6.2
            probe.timeSlicingMode = ReflectionProbeTimeSlicingMode.AllFacesAtOnce;

            LogOperation("CreateReflectionProbe", $"Created reflection probe: {name}");

            return Response.Success($"Reflection probe created successfully", new
            {
                probeName = name,
                position = position,
                size = size,
                resolution = resolution,
                mode = mode.ToString(),
                refreshMode = refreshMode.ToString(),
                hdr = hdr,
                intensity = intensity
            });
        }

        /// <summary>
        /// [HELPER] - Configura Reflection Probe existente.
        /// </summary>
        private static object ConfigureExistingReflectionProbe(string probeName, JObject @params)
        {
            ReflectionProbe probe = FindReflectionProbeByName(probeName);
            if (probe == null)
            {
                return Response.Error($"Reflection probe not found: {probeName}");
            }

            // Aplicar configurações
            if (@params["resolution"] != null)
                probe.resolution = @params["resolution"].ToObject<int>();
            if (@params["intensity"] != null)
                probe.intensity = @params["intensity"].ToObject<float>();
            if (@params["hdr"] != null)
                probe.hdr = @params["hdr"].ToObject<bool>();
            if (@params["size"] != null)
                probe.size = ParseVector3(@params["size"]) ?? probe.size;

            LogOperation("ConfigureExistingReflectionProbe", $"Configured reflection probe: {probeName}");

            return Response.Success($"Reflection probe configured successfully", new
            {
                probeName = probeName,
                resolution = probe.resolution,
                intensity = probe.intensity,
                hdr = probe.hdr,
                size = probe.size
            });
        }

        /// <summary>
        /// [HELPER] - Faz bake de Reflection Probe.
        /// </summary>
        private static object BakeReflectionProbe(string probeName)
        {
            ReflectionProbe probe = FindReflectionProbeByName(probeName);
            if (probe == null)
            {
                return Response.Error($"Reflection probe not found: {probeName}");
            }

            if (probe.mode != ReflectionProbeMode.Baked)
            {
                return Response.Error($"Reflection probe must be in Baked mode for baking: {probeName}");
            }

            // Fazer bake usando Unity 6.2 API
            probe.RenderProbe();

            LogOperation("BakeReflectionProbe", $"Baked reflection probe: {probeName}");

            return Response.Success($"Reflection probe baked successfully", new
            {
                probeName = probeName,
                mode = probe.mode.ToString(),
                resolution = probe.resolution
            });
        }

        /// <summary>
        /// [HELPER] - Encontra Reflection Probe por nome.
        /// </summary>
        private static ReflectionProbe FindReflectionProbeByName(string name)
        {
            ReflectionProbe[] probes = UnityEngine.Object.FindObjectsByType<ReflectionProbe>(FindObjectsSortMode.None);
            return probes.FirstOrDefault(p => p.gameObject.name == name);
        }

        /// <summary>
        /// [HELPER] - Parse ReflectionProbeMode.
        /// </summary>
        private static ReflectionProbeMode? ParseReflectionProbeMode(string mode)
        {
            if (string.IsNullOrEmpty(mode)) return null;

            return mode.ToLower() switch
            {
                "baked" => ReflectionProbeMode.Baked,
                "realtime" => ReflectionProbeMode.Realtime,
                "custom" => ReflectionProbeMode.Custom,
                _ => null
            };
        }

        /// <summary>
        /// [HELPER] - Parse ReflectionProbeRefreshMode.
        /// </summary>
        private static ReflectionProbeRefreshMode? ParseRefreshMode(string mode)
        {
            if (string.IsNullOrEmpty(mode)) return null;

            return mode.ToLower() switch
            {
                "onawake" => ReflectionProbeRefreshMode.OnAwake,
                "everyframe" => ReflectionProbeRefreshMode.EveryFrame,
                "viascripting" => ReflectionProbeRefreshMode.ViaScripting,
                _ => null
            };
        }

        /// <summary>
        /// [HELPER] - Otimiza Reflection Probes na cena.
        /// </summary>
        private static object OptimizeReflectionProbes(JObject @params)
        {
            ReflectionProbe[] probes = UnityEngine.Object.FindObjectsByType<ReflectionProbe>(FindObjectsSortMode.None);
            var optimizations = new List<string>();

            foreach (var probe in probes)
            {
                // Otimizações Unity 6.2
                if (probe.resolution > 512)
                {
                    probe.resolution = 512;
                    optimizations.Add($"Reduced resolution for {probe.name}");
                }

                if (probe.refreshMode == ReflectionProbeRefreshMode.EveryFrame)
                {
                    probe.refreshMode = ReflectionProbeRefreshMode.OnAwake;
                    optimizations.Add($"Changed refresh mode for {probe.name}");
                }

                probe.timeSlicingMode = ReflectionProbeTimeSlicingMode.IndividualFaces;
                optimizations.Add($"Enabled time slicing for {probe.name}");
            }

            Debug.Log($"[LightingRendering] OptimizeReflectionProbes: Optimized {probes.Length} reflection probes");

            return Response.Success($"Reflection probes optimized", new
            {
                probesOptimized = probes.Length,
                optimizations = optimizations.ToArray()
            });
        }

        /// <summary>
        /// [UNITY 6.2] - Advanced lighting methods using real Unity APIs.
        /// </summary>
        private static object HandleLightmaps(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "setup":
                        return SetupLightmaps(@params);
                    case "bake":
                        return BakeLightmaps(@params);
                    case "clear":
                        return ClearLightmaps();
                    case "configure":
                        return ConfigureLightmaps(@params);
                    default:
                        return Response.Error($"Unknown lightmaps action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Lightmaps {action}: {e.Message}");
            }
        }

        private static object SetupLightmaps(JObject @params)
        {
            UnityEngine.Debug.LogError("[CRITICAL DEBUG] SetupLightmaps method called!");
            int resolution = @params["resolution"]?.ToObject<int>() ?? 1024;
            int padding = @params["padding"]?.ToObject<int>() ?? 2;
            bool compressLightmaps = @params["compress_lightmaps"]?.ToObject<bool>() ?? true;
            bool aoEnabled = @params["ao_enabled"]?.ToObject<bool>() ?? true;
            float aoMaxDistance = @params["ao_max_distance"]?.ToObject<float>() ?? 1.0f;
            int lightmapSize = @params["lightmap_size"]?.ToObject<int>() ?? 1024;
            string directionalMode = @params["directional_mode"]?.ToString() ?? "directional";

            // Skip LightingSettings for now and configure lightmapping directly
            UnityEngine.Debug.LogError("[DEBUG] SetupLightmaps: Configuring lightmapping without LightingSettings");
            
            // Configure lightmapping settings directly using LightingSettings
            var lightingSettings = Lightmapping.lightingSettings;
            if (lightingSettings == null)
            {
                lightingSettings = new LightingSettings();
                Lightmapping.lightingSettings = lightingSettings;
            }
            
            lightingSettings.indirectResolution = resolution;
            lightingSettings.lightmapPadding = padding;
            lightingSettings.ao = aoEnabled;
            lightingSettings.aoMaxDistance = aoMaxDistance;
            lightingSettings.lightmapMaxSize = lightmapSize;
            
            UnityEngine.Debug.LogError("[DEBUG] SetupLightmaps: Basic lightmap settings configured successfully");
            
            return Response.Success("Lightmap settings configured successfully (without LightingSettings)", new
            {
                resolution = resolution,
                padding = padding,
                aoEnabled = aoEnabled,
                aoMaxDistance = aoMaxDistance,
                lightmapSize = lightmapSize
            });
        }

        private static object BakeLightmaps(JObject @params)
        {
            try
            {
                // Ensure baked GI is enabled
                var lightingSettings = Lightmapping.lightingSettings;
                if (lightingSettings == null)
                {
                    // Create a new LightingSettings object if none exists
                    lightingSettings = new LightingSettings();
                    Lightmapping.lightingSettings = lightingSettings;
                }
                
                lightingSettings.bakedGI = true;

                Lightmapping.BakeAsync();
                return Response.Success("Lightmap baking started successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to start lightmap baking: {e.Message}");
            }
        }

        private static object ClearLightmaps()
        {
            try
            {
                Lightmapping.Clear();
                return Response.Success("Lightmaps cleared successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to clear lightmaps: {e.Message}");
            }
        }

        private static object ConfigureLightmaps(JObject @params)
        {
            try
            {
                var lightingSettings = Lightmapping.lightingSettings;
                if (lightingSettings == null)
                {
                    // Create a new LightingSettings object if none exists
                    lightingSettings = new LightingSettings();
                    Lightmapping.lightingSettings = lightingSettings;
                }

                var data = new
                {
                    lightmap_resolution = lightingSettings.lightmapResolution,
                    lightmap_padding = lightingSettings.lightmapPadding,
                    lightmap_compression = lightingSettings.lightmapCompression.ToString(),
                    ao_enabled = lightingSettings.ao,
                    ao_max_distance = lightingSettings.aoMaxDistance,
                    lightmap_max_size = lightingSettings.lightmapMaxSize,
                    directionality_mode = lightingSettings.directionalityMode.ToString(),
                    baked_gi = lightingSettings.bakedGI,
                    realtime_gi = lightingSettings.realtimeGI
                };

                return Response.Success("Lightmap configuration retrieved successfully.", data);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to retrieve lightmap configuration: {e.Message}");
            }
        }

        private static object HandleRealtimeGI(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "enable":
                        return EnableRealtimeGI(@params);
                    case "disable":
                        return DisableRealtimeGI();
                    case "configure":
                        return ConfigureRealtimeGI(@params);
                    default:
                        return Response.Error($"Unknown realtime GI action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Realtime GI {action}: {e.Message}");
            }
        }

        private static object EnableRealtimeGI(JObject @params)
        {
            bool enabled = @params["enabled"]?.ToObject<bool>() ?? true;
            int cpuUsage = @params["cpu_usage"]?.ToObject<int>() ?? 25;
            float resolution = @params["resolution"]?.ToObject<float>() ?? 2.0f;
            float updateThreshold = @params["update_threshold"]?.ToObject<float>() ?? 1.0f;
            bool temporalCoherence = @params["temporal_coherence"]?.ToObject<bool>() ?? true;

            // Get or create LightingSettings
            var lightingSettings = Lightmapping.lightingSettings;
            if (lightingSettings == null)
            {
                lightingSettings = new LightingSettings();
                Lightmapping.lightingSettings = lightingSettings;
            }

            // Configure Realtime GI settings
            lightingSettings.realtimeGI = enabled;
            lightingSettings.indirectResolution = resolution;

            // Configure CPU usage and other settings
            Lightmapping.realtimeGI = enabled;

            var data = new
            {
                enabled = enabled,
                cpu_usage = cpuUsage,
                resolution = resolution,
                update_threshold = updateThreshold,
                temporal_coherence = temporalCoherence
            };

            return Response.Success($"Realtime GI {(enabled ? "enabled" : "disabled")} successfully.", data);
        }

        private static object DisableRealtimeGI()
        {
            var lightingSettings = Lightmapping.lightingSettings;
            if (lightingSettings == null)
            {
                // Create a new LightingSettings object if none exists
                lightingSettings = new LightingSettings();
                Lightmapping.lightingSettings = lightingSettings;
            }
            
            lightingSettings.realtimeGI = false;
            Lightmapping.realtimeGI = false;

            return Response.Success("Realtime GI disabled successfully.");
        }

        private static object ConfigureRealtimeGI(JObject @params)
        {
            try
            {
                var lightingSettings = Lightmapping.lightingSettings;
                if (lightingSettings == null)
                {
                    // Create a new LightingSettings object if none exists
                    lightingSettings = new LightingSettings();
                    Lightmapping.lightingSettings = lightingSettings;
                }

                var data = new
                {
                    realtime_gi_enabled = lightingSettings.realtimeGI,
                    indirect_resolution = lightingSettings.indirectResolution,
                    environment_lighting = lightingSettings.realtimeEnvironmentLighting.ToString()
                };

                return Response.Success("Realtime GI configuration retrieved successfully.", data);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to retrieve realtime GI configuration: {e.Message}");
            }
        }

        private static object HandleVolumetricLighting(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "create":
                        return CreateVolumetricLight(@params);
                    case "configure":
                        return ConfigureVolumetricLight(@params);
                    case "delete":
                        return DeleteVolumetricLight(@params);
                    default:
                        return Response.Error($"Unknown volumetric lighting action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Volumetric Lighting {action}: {e.Message}");
            }
        }

        private static object CreateVolumetricLight(JObject @params)
        {
            string lightName = @params["light_name"]?.ToString() ?? "VolumetricLight";
            var position = @params["position"]?.ToObject<float[]>();
            float intensity = @params["intensity"]?.ToObject<float>() ?? 1.0f;
            float rangeDistance = @params["range_distance"]?.ToObject<float>() ?? 10.0f;
            var color = @params["color"]?.ToObject<float[]>();
            string lightType = @params["light_type"]?.ToString()?.ToLower() ?? "point";
            
            // For spot lights
            float spotAngle = @params["spot_angle"]?.ToObject<float>() ?? 30.0f;
            float innerSpotAngle = @params["inner_spot_angle"]?.ToObject<float>() ?? 21.8f;

            GameObject lightObj = new GameObject(lightName);
            Light light = lightObj.AddComponent<Light>();

            // Configure light type
            switch (lightType)
            {
                case "spot":
                    light.type = LightType.Spot;
                    light.spotAngle = spotAngle;
                    light.innerSpotAngle = innerSpotAngle;
                    break;
                case "directional":
                    light.type = LightType.Directional;
                    break;
                case "area":
                    light.type = LightType.Rectangle;
                    break;
                case "point":
                default:
                    light.type = LightType.Point;
                    break;
            }

            // Configure light properties
            light.intensity = intensity;
            if (light.type != LightType.Directional)
            {
                light.range = rangeDistance;
            }
            
            light.shadows = LightShadows.Soft;

            if (position != null && position.Length >= 3)
            {
                lightObj.transform.position = new Vector3(position[0], position[1], position[2]);
            }

            if (color != null && color.Length >= 3)
            {
                light.color = new Color(color[0], color[1], color[2], color.Length >= 4 ? color[3] : 1.0f);
            }

            // Add additional data component for volumetric settings
            var volumetricData = lightObj.AddComponent<VolumetricLightData>();
            volumetricData.volumetricMultiplier = @params["volumetric_multiplier"]?.ToObject<float>() ?? 1.0f;
            volumetricData.shadowDimmer = @params["shadow_dimmer"]?.ToObject<float>() ?? 1.0f;
            volumetricData.volumetricShadowDimmer = @params["volumetric_shadow_dimmer"]?.ToObject<float>() ?? 1.0f;

            var data = new
            {
                name = lightName,
                light_type = lightType,
                position = position,
                intensity = intensity,
                range_distance = rangeDistance,
                color = color,
                spot_angle = light.type == LightType.Spot ? spotAngle : (float?)null,
                inner_spot_angle = light.type == LightType.Spot ? innerSpotAngle : (float?)null,
                volumetric_multiplier = volumetricData.volumetricMultiplier,
                shadow_dimmer = volumetricData.shadowDimmer,
                volumetric_shadow_dimmer = volumetricData.volumetricShadowDimmer
            };

            return Response.Success($"Volumetric light '{lightName}' created successfully.", data);
        }

        private static object ConfigureVolumetricLight(JObject @params)
        {
            string lightName = @params["light_name"]?.ToString();
            
            if (string.IsNullOrEmpty(lightName))
            {
                return Response.Error("Light name is required for configuration.");
            }

            GameObject lightObj = GameObject.Find(lightName);
            if (lightObj == null)
            {
                return Response.Error($"Light '{lightName}' not found.");
            }

            Light light = lightObj.GetComponent<Light>();
            VolumetricLightData volumetricData = lightObj.GetComponent<VolumetricLightData>();
            
            if (light == null)
            {
                return Response.Error($"Light component not found on '{lightName}'.");
            }

            // Update light properties if provided
            if (@params["intensity"] != null)
                light.intensity = @params["intensity"].ToObject<float>();
            if (@params["range_distance"] != null && light.type != LightType.Directional)
                light.range = @params["range_distance"].ToObject<float>();
            if (@params["spot_angle"] != null && light.type == LightType.Spot)
                light.spotAngle = @params["spot_angle"].ToObject<float>();
            if (@params["inner_spot_angle"] != null && light.type == LightType.Spot)
                light.innerSpotAngle = @params["inner_spot_angle"].ToObject<float>();
            
            if (@params["color"] != null)
            {
                var color = @params["color"].ToObject<float[]>();
                if (color != null && color.Length >= 3)
                {
                    light.color = new Color(color[0], color[1], color[2], color.Length >= 4 ? color[3] : 1.0f);
                }
            }

            // Update volumetric properties if component exists
            if (volumetricData != null)
            {
                if (@params["volumetric_multiplier"] != null)
                    volumetricData.volumetricMultiplier = @params["volumetric_multiplier"].ToObject<float>();
                if (@params["shadow_dimmer"] != null)
                    volumetricData.shadowDimmer = @params["shadow_dimmer"].ToObject<float>();
                if (@params["volumetric_shadow_dimmer"] != null)
                    volumetricData.volumetricShadowDimmer = @params["volumetric_shadow_dimmer"].ToObject<float>();
            }

            var data = new
            {
                name = lightName,
                light_type = light.type.ToString(),
                intensity = light.intensity,
                range_distance = light.type != LightType.Directional ? light.range : (float?)null,
                color = new float[] { light.color.r, light.color.g, light.color.b, light.color.a },
                spot_angle = light.type == LightType.Spot ? light.spotAngle : (float?)null,
                inner_spot_angle = light.type == LightType.Spot ? light.innerSpotAngle : (float?)null,
                volumetric_multiplier = volumetricData?.volumetricMultiplier ?? 1.0f,
                shadow_dimmer = volumetricData?.shadowDimmer ?? 1.0f,
                volumetric_shadow_dimmer = volumetricData?.volumetricShadowDimmer ?? 1.0f
            };

            return Response.Success($"Volumetric light '{lightName}' configured successfully.", data);
        }

        private static object DeleteVolumetricLight(JObject @params)
        {
            string lightName = @params["light_name"]?.ToString();
            
            if (string.IsNullOrEmpty(lightName))
            {
                // Delete all volumetric lights
                VolumetricLightData[] allVolumetricLights = UnityEngine.Object.FindObjectsByType<VolumetricLightData>(FindObjectsSortMode.None);
                foreach (var volumetricLight in allVolumetricLights)
                {
                    UnityEngine.Object.DestroyImmediate(volumetricLight.gameObject);
                }
                return Response.Success($"Deleted {allVolumetricLights.Length} volumetric lights.");
            }
            else
            {
                GameObject lightObj = GameObject.Find(lightName);
                if (lightObj != null && lightObj.GetComponent<VolumetricLightData>() != null)
                {
                    UnityEngine.Object.DestroyImmediate(lightObj);
                    return Response.Success($"Volumetric light '{lightName}' deleted successfully.");
                }
                else
                {
                    return Response.Error($"Volumetric light '{lightName}' not found.");
                }
            }
        }

        private static object HandleShadowCascades(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "configure":
                        return ConfigureShadowCascades(@params);
                    case "optimize":
                        return OptimizeShadowCascades(@params);
                    case "reset":
                        return ResetShadowCascades();
                    default:
                        return Response.Error($"Unknown shadow cascades action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Shadow Cascades {action}: {e.Message}");
            }
        }

        private static object ConfigureShadowCascades(JObject @params)
        {
            int cascadeCount = @params["cascade_count"]?.ToObject<int>() ?? 4;
            var cascadeSplits = @params["cascade_splits"]?.ToObject<float[]>();
            float maxDistance = @params["max_distance"]?.ToObject<float>() ?? 1000.0f;
            int resolution = @params["resolution"]?.ToObject<int>() ?? 2048;
            float shadowBias = @params["shadow_bias"]?.ToObject<float>() ?? 0.05f;
            float shadowNormalBias = @params["shadow_normal_bias"]?.ToObject<float>() ?? 0.4f;

            // Configure shadow settings using QualitySettings
            QualitySettings.shadowCascades = cascadeCount;
            QualitySettings.shadowDistance = maxDistance;
            QualitySettings.shadowResolution = GetLegacyShadowResolution(resolution);

            // Configure cascade splits if provided
            if (cascadeSplits != null && cascadeSplits.Length > 0)
            {
                switch (cascadeCount)
                {
                    case 2:
                        if (cascadeSplits.Length >= 1)
                            QualitySettings.shadowCascade2Split = cascadeSplits[0];
                        break;
                    case 4:
                        if (cascadeSplits.Length >= 3)
                        {
                            QualitySettings.shadowCascade4Split = new Vector3(
                                cascadeSplits[0], 
                                cascadeSplits[1], 
                                cascadeSplits[2]
                            );
                        }
                        break;
                }
            }

            // Configure shadow bias for all directional lights
            Light[] directionalLights = UnityEngine.Object.FindObjectsByType<Light>(FindObjectsSortMode.None)
                .Where(light => light.type == LightType.Directional).ToArray();

            foreach (var light in directionalLights)
            {
                light.shadowBias = shadowBias;
                light.shadowNormalBias = shadowNormalBias;
            }

            var data = new
            {
                cascade_count = cascadeCount,
                cascade_splits = cascadeSplits,
                max_distance = maxDistance,
                resolution = resolution,
                shadow_bias = shadowBias,
                shadow_normal_bias = shadowNormalBias,
                configured_lights = directionalLights.Length
            };

            return Response.Success("Shadow cascades configured successfully.", data);
        }

        private static object OptimizeShadowCascades(JObject @params)
        {
            int targetFPS = @params["target_fps"]?.ToObject<int>() ?? 60;

            // Optimize shadow settings based on target FPS
            if (targetFPS >= 60)
            {
                QualitySettings.shadowCascades = 4;
                QualitySettings.shadowResolution = UnityEngine.ShadowResolution.High;
                QualitySettings.shadowDistance = 150f;
            }
            else if (targetFPS >= 30)
            {
                QualitySettings.shadowCascades = 2;
                QualitySettings.shadowResolution = UnityEngine.ShadowResolution.Medium;
                QualitySettings.shadowDistance = 100f;
            }
            else
            {
                QualitySettings.shadowCascades = 1;
                QualitySettings.shadowResolution = UnityEngine.ShadowResolution.Low;
                QualitySettings.shadowDistance = 50f;
            }

            var data = new
            {
                target_fps = targetFPS,
                optimized_cascades = QualitySettings.shadowCascades,
                optimized_resolution = QualitySettings.shadowResolution.ToString(),
                optimized_distance = QualitySettings.shadowDistance
            };

            return Response.Success($"Shadow cascades optimized for {targetFPS} FPS target.", data);
        }

        private static object ResetShadowCascades()
        {
            // Reset to Unity defaults
            QualitySettings.shadowCascades = 4;
            QualitySettings.shadowResolution = UnityEngine.ShadowResolution.Medium;
            QualitySettings.shadowDistance = 150f;
            QualitySettings.shadowCascade4Split = new Vector3(0.0666f, 0.2f, 0.4666f);

            var data = new
            {
                cascade_count = QualitySettings.shadowCascades,
                resolution = QualitySettings.shadowResolution.ToString(),
                distance = QualitySettings.shadowDistance,
                cascade_split = QualitySettings.shadowCascade4Split
            };

            return Response.Success("Shadow cascades reset to default values.", data);
        }

        private static UnityEngine.Rendering.Universal.ShadowResolution GetShadowResolution(int resolution)
        {
            return resolution switch
            {
                <= 256 => UnityEngine.Rendering.Universal.ShadowResolution._256,
                <= 512 => UnityEngine.Rendering.Universal.ShadowResolution._512,
                <= 1024 => UnityEngine.Rendering.Universal.ShadowResolution._1024,
                <= 2048 => UnityEngine.Rendering.Universal.ShadowResolution._2048,
                _ => UnityEngine.Rendering.Universal.ShadowResolution._4096
            };
        }

        private static UnityEngine.ShadowResolution GetLegacyShadowResolution(int resolution)
        {
            return resolution switch
            {
                <= 256 => UnityEngine.ShadowResolution.Low,
                <= 512 => UnityEngine.ShadowResolution.Medium,
                <= 1024 => UnityEngine.ShadowResolution.High,
                _ => UnityEngine.ShadowResolution.VeryHigh
            };
        }

        private static object HandleLightCookies(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "create":
                        return CreateLightWithCookie(@params);
                    case "apply":
                        return ApplyLightCookie(@params);
                    case "remove":
                        return RemoveLightCookie(@params);
                    default:
                        return Response.Error($"Unknown light cookies action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Light Cookies {action}: {e.Message}");
            }
        }

        private static object CreateLightWithCookie(JObject @params)
        {
            string lightName = @params["light_name"]?.ToString() ?? "CookieLight";
            string cookieTexturePath = @params["cookie_texture_path"]?.ToString();
            float cookieSize = @params["cookie_size"]?.ToObject<float>() ?? 1.0f;
            var position = @params["position"]?.ToObject<float[]>();
            var rotation = @params["rotation"]?.ToObject<float[]>();
            float intensity = @params["intensity"]?.ToObject<float>() ?? 1.0f;

            GameObject lightObj = new GameObject(lightName);
            Light light = lightObj.AddComponent<Light>();

            // Configure light
            light.type = LightType.Spot;
            light.intensity = intensity;
            light.cookieSize = cookieSize;

            if (position != null && position.Length >= 3)
            {
                lightObj.transform.position = new Vector3(position[0], position[1], position[2]);
            }

            if (rotation != null && rotation.Length >= 3)
            {
                lightObj.transform.rotation = Quaternion.Euler(rotation[0], rotation[1], rotation[2]);
            }

            // Load and apply cookie texture if provided
            Texture2D cookieTexture = null;
            if (!string.IsNullOrEmpty(cookieTexturePath))
            {
                cookieTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(cookieTexturePath);
                if (cookieTexture != null)
                {
                    light.cookie = cookieTexture;
                }
                else
                {
                    Debug.LogWarning($"Cookie texture not found at path: {cookieTexturePath}");
                }
            }

            var data = new
            {
                name = lightName,
                cookie_texture_path = cookieTexturePath,
                cookie_size = cookieSize,
                position = position,
                rotation = rotation,
                intensity = intensity,
                cookie_applied = cookieTexture != null
            };

            return Response.Success($"Light with cookie '{lightName}' created successfully.", data);
        }

        private static object ApplyLightCookie(JObject @params)
        {
            string lightName = @params["light_name"]?.ToString();
            string cookieTexturePath = @params["cookie_texture_path"]?.ToString();
            float cookieSize = @params["cookie_size"]?.ToObject<float>() ?? 1.0f;

            if (string.IsNullOrEmpty(lightName))
            {
                return Response.Error("Light name is required.");
            }

            if (string.IsNullOrEmpty(cookieTexturePath))
            {
                return Response.Error("Cookie texture path is required.");
            }

            GameObject lightObj = GameObject.Find(lightName);
            if (lightObj == null)
            {
                return Response.Error($"Light '{lightName}' not found.");
            }

            Light light = lightObj.GetComponent<Light>();
            if (light == null)
            {
                return Response.Error($"Light component not found on '{lightName}'.");
            }

            // Load and apply cookie texture
            Texture2D cookieTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(cookieTexturePath);
            if (cookieTexture == null)
            {
                return Response.Error($"Cookie texture not found at path: {cookieTexturePath}");
            }

            light.cookie = cookieTexture;
            light.cookieSize = cookieSize;

            var data = new
            {
                light_name = lightName,
                cookie_texture_path = cookieTexturePath,
                cookie_size = cookieSize,
                texture_dimensions = $"{cookieTexture.width}x{cookieTexture.height}"
            };

            return Response.Success($"Cookie applied to light '{lightName}' successfully.", data);
        }

        private static object RemoveLightCookie(JObject @params)
        {
            string lightName = @params["light_name"]?.ToString();

            if (string.IsNullOrEmpty(lightName))
            {
                return Response.Error("Light name is required.");
            }

            GameObject lightObj = GameObject.Find(lightName);
            if (lightObj == null)
            {
                return Response.Error($"Light '{lightName}' not found.");
            }

            Light light = lightObj.GetComponent<Light>();
            if (light == null)
            {
                return Response.Error($"Light component not found on '{lightName}'.");
            }

            light.cookie = null;

            return Response.Success($"Cookie removed from light '{lightName}' successfully.");
        }

        private static object HandleHDRPipeline(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "configure":
                        return ConfigureHDRPipeline(@params);
                    case "enable":
                        return EnableHDR(@params);
                    case "disable":
                        return DisableHDR();
                    default:
                        return Response.Error($"Unknown HDR pipeline action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in HDR Pipeline {action}: {e.Message}");
            }
        }

        private static object ConfigureHDRPipeline(JObject @params)
        {
            string pipelineType = @params["pipeline_type"]?.ToString() ?? "urp";
            bool hdrEnabled = @params["hdr_enabled"]?.ToObject<bool>() ?? true;
            int msaaSamples = @params["msaa_samples"]?.ToObject<int>() ?? 4;
            float renderScale = @params["render_scale"]?.ToObject<float>() ?? 1.0f;
            int depthBits = @params["depth_bits"]?.ToObject<int>() ?? 24;
            string opaqueDownsampling = @params["opaque_downsampling"]?.ToString() ?? "none";

            // Configure camera settings for HDR
            Camera[] cameras = UnityEngine.Object.FindObjectsByType<Camera>(FindObjectsSortMode.None);
            foreach (var camera in cameras)
            {
                camera.allowHDR = hdrEnabled;
                camera.allowMSAA = msaaSamples > 1;
                
                // Configure depth buffer
                camera.depthTextureMode = depthBits switch
                {
                    16 => DepthTextureMode.Depth,
                    24 => DepthTextureMode.Depth,
                    32 => DepthTextureMode.Depth | DepthTextureMode.DepthNormals,
                    _ => DepthTextureMode.Depth
                };
            }

            // Configure quality settings
            QualitySettings.antiAliasing = msaaSamples;

            var data = new
            {
                pipeline_type = pipelineType,
                hdr_enabled = hdrEnabled,
                msaa_samples = msaaSamples,
                render_scale = renderScale,
                depth_bits = depthBits,
                opaque_downsampling = opaqueDownsampling,
                configured_cameras = cameras.Length
            };

            return Response.Success("HDR pipeline configured successfully.", data);
        }

        private static object EnableHDR(JObject @params)
        {
            bool hdrEnabled = @params["hdr_enabled"]?.ToObject<bool>() ?? true;

            // Enable HDR on all cameras
            Camera[] cameras = UnityEngine.Object.FindObjectsByType<Camera>(FindObjectsSortMode.None);
            foreach (var camera in cameras)
            {
                camera.allowHDR = hdrEnabled;
            }

            // Configure player settings for HDR
            PlayerSettings.useHDRDisplay = hdrEnabled;

            var data = new
            {
                hdr_enabled = hdrEnabled,
                configured_cameras = cameras.Length,
                hdr_display = PlayerSettings.useHDRDisplay
            };

            return Response.Success($"HDR {(hdrEnabled ? "enabled" : "disabled")} successfully.", data);
        }

        private static object DisableHDR()
        {
            // Disable HDR on all cameras
            Camera[] cameras = UnityEngine.Object.FindObjectsByType<Camera>(FindObjectsSortMode.None);
            foreach (var camera in cameras)
            {
                camera.allowHDR = false;
            }

            PlayerSettings.useHDRDisplay = false;

            var data = new
            {
                hdr_enabled = false,
                configured_cameras = cameras.Length
            };

            return Response.Success("HDR disabled successfully.", data);
        }

        private static object HandlePostProcessing(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "create":
                        return CreatePostProcessVolume(@params);
                    case "configure":
                        return ConfigurePostProcessVolume(@params);
                    case "delete":
                        return DeletePostProcessVolume(@params);
                    default:
                        return Response.Error($"Unknown post-processing action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Post-Processing {action}: {e.Message}");
            }
        }

        private static object CreatePostProcessVolume(JObject @params)
        {
            string volumeName = @params["volume_name"]?.ToString() ?? "PostProcessVolume";
            string profileName = @params["profile_name"]?.ToString() ?? "PostProcessProfile";
            float priority = @params["priority"]?.ToObject<float>() ?? 0.0f;
            bool isGlobal = @params["is_global"]?.ToObject<bool>() ?? true;
            float weight = @params["weight"]?.ToObject<float>() ?? 1.0f;

            // Create volume GameObject
            GameObject volumeObj = new GameObject(volumeName);
            
            // Add Volume component
            Volume volume = volumeObj.AddComponent<Volume>();
            volume.priority = priority;
            volume.isGlobal = isGlobal;
            volume.weight = weight;

            // Create or load Volume Profile
            VolumeProfile volumeProfile = null;
            string profilePath = $"Assets/VolumeProfiles/{profileName}.asset";
            
            // Try to load existing profile
            volumeProfile = AssetDatabase.LoadAssetAtPath<VolumeProfile>(profilePath);
            
            if (volumeProfile == null)
            {
                // Create new profile
                volumeProfile = ScriptableObject.CreateInstance<VolumeProfile>();
                
                // Ensure directory exists
                string directory = System.IO.Path.GetDirectoryName(profilePath);
                if (!System.IO.Directory.Exists(directory))
                {
                    System.IO.Directory.CreateDirectory(directory);
                }
                
                AssetDatabase.CreateAsset(volumeProfile, profilePath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }
            
            volume.profile = volumeProfile;

            var data = new
            {
                volume_name = volumeName,
                profile_name = profileName,
                priority = priority,
                is_global = isGlobal,
                weight = weight,
                profile_path = profilePath
            };

            return Response.Success($"Post-process volume '{volumeName}' created successfully.", data);
        }

        private static object ConfigurePostProcessVolume(JObject @params)
        {
            string volumeName = @params["volume_name"]?.ToString();
            
            if (string.IsNullOrEmpty(volumeName))
            {
                return Response.Error("Volume name is required for configuration.");
            }

            GameObject volumeObj = GameObject.Find(volumeName);
            if (volumeObj == null)
            {
                return Response.Error($"Volume '{volumeName}' not found.");
            }

            Volume volume = volumeObj.GetComponent<Volume>();
            if (volume == null)
            {
                return Response.Error($"Volume component not found on '{volumeName}'.");
            }

            // Update volume properties if provided
            if (@params["priority"] != null)
                volume.priority = @params["priority"].ToObject<float>();
            if (@params["is_global"] != null)
                volume.isGlobal = @params["is_global"].ToObject<bool>();
            if (@params["weight"] != null)
                volume.weight = @params["weight"].ToObject<float>();

            var data = new
            {
                volume_name = volumeName,
                priority = volume.priority,
                is_global = volume.isGlobal,
                weight = volume.weight,
                profile_name = volume.profile != null ? volume.profile.name : "None"
            };

            return Response.Success($"Post-process volume '{volumeName}' configured successfully.", data);
        }

        private static object DeletePostProcessVolume(JObject @params)
        {
            string volumeName = @params["volume_name"]?.ToString();
            
            if (string.IsNullOrEmpty(volumeName))
            {
                // Delete all post-process volumes
                Volume[] allVolumes = UnityEngine.Object.FindObjectsByType<Volume>(FindObjectsSortMode.None);
                foreach (var volume in allVolumes)
                {
                    UnityEngine.Object.DestroyImmediate(volume.gameObject);
                }
                return Response.Success($"Deleted {allVolumes.Length} post-process volumes.");
            }
            else
            {
                GameObject volumeObj = GameObject.Find(volumeName);
                if (volumeObj != null && volumeObj.GetComponent<Volume>() != null)
                {
                    UnityEngine.Object.DestroyImmediate(volumeObj);
                    return Response.Success($"Post-process volume '{volumeName}' deleted successfully.");
                }
                else
                {
                    return Response.Error($"Post-process volume '{volumeName}' not found.");
                }
            }
        }

        private static object HandleBloomEffects(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "enable":
                        return EnableBloomEffect(@params);
                    case "configure":
                        return ConfigureBloomEffect(@params);
                    case "disable":
                        return DisableBloomEffect(@params);
                    default:
                        return Response.Error($"Unknown bloom effects action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Bloom Effects {action}: {e.Message}");
            }
        }

        private static object EnableBloomEffect(JObject @params)
        {
            string volumeName = @params["volume_name"]?.ToString() ?? "BloomVolume";
            float intensity = @params["intensity"]?.ToObject<float>() ?? 0.5f;
            float threshold = @params["threshold"]?.ToObject<float>() ?? 1.0f;
            float scatter = @params["scatter"]?.ToObject<float>() ?? 0.7f;
            var tint = @params["tint"]?.ToObject<float[]>();
            float clamp = @params["clamp"]?.ToObject<float>() ?? 65472f;
            bool highQualityFiltering = @params["high_quality_filtering"]?.ToObject<bool>() ?? false;

            // Find or create volume
            GameObject volumeObj = GameObject.Find(volumeName);
            Volume volume = null;
            
            if (volumeObj == null)
            {
                volumeObj = new GameObject(volumeName);
                volume = volumeObj.AddComponent<Volume>();
                volume.isGlobal = true;
                volume.priority = 1;
                
                // Create a new volume profile
                VolumeProfile profile = ScriptableObject.CreateInstance<VolumeProfile>();
                volume.profile = profile;
            }
            else
            {
                volume = volumeObj.GetComponent<Volume>();
                if (volume == null)
                {
                    volume = volumeObj.AddComponent<Volume>();
                    volume.isGlobal = true;
                    volume.priority = 1;
                }
                
                if (volume.profile == null)
                {
                    volume.profile = ScriptableObject.CreateInstance<VolumeProfile>();
                }
            }

            // Get or add Bloom component
            if (!volume.profile.TryGet<Bloom>(out var bloom))
            {
                bloom = volume.profile.Add<Bloom>(false);
            }

            // Configure Bloom settings
            bloom.intensity.overrideState = true;
            bloom.intensity.value = intensity;
            
            bloom.threshold.overrideState = true;
            bloom.threshold.value = threshold;
            
            bloom.scatter.overrideState = true;
            bloom.scatter.value = scatter;
            
            if (tint != null && tint.Length >= 3)
            {
                bloom.tint.overrideState = true;
                bloom.tint.value = new Color(tint[0], tint[1], tint[2], tint.Length >= 4 ? tint[3] : 1.0f);
            }
            
            bloom.clamp.overrideState = true;
            bloom.clamp.value = clamp;
            
            bloom.highQualityFiltering.overrideState = true;
            bloom.highQualityFiltering.value = highQualityFiltering;

            var data = new
            {
                volume_name = volumeName,
                enabled = true,
                intensity = intensity,
                threshold = threshold,
                scatter = scatter,
                tint = tint,
                clamp = clamp,
                high_quality_filtering = highQualityFiltering
            };

            return Response.Success("Bloom effect enabled successfully.", data);
        }

        private static object ConfigureBloomEffect(JObject @params)
        {
            string volumeName = @params["volume_name"]?.ToString() ?? "BloomVolume";
            
            GameObject volumeObj = GameObject.Find(volumeName);
            if (volumeObj == null)
            {
                return Response.Error($"Bloom volume '{volumeName}' not found. Please enable it first.");
            }

            Volume volume = volumeObj.GetComponent<Volume>();
            if (volume == null || volume.profile == null)
            {
                return Response.Error("Volume or volume profile not found.");
            }

            // Get Bloom component
            if (!volume.profile.TryGet<Bloom>(out var bloom))
            {
                return Response.Error("Bloom effect not found in volume profile.");
            }

            // Update properties if provided
            if (@params["intensity"] != null)
            {
                bloom.intensity.overrideState = true;
                bloom.intensity.value = @params["intensity"].ToObject<float>();
            }
            
            if (@params["threshold"] != null)
            {
                bloom.threshold.overrideState = true;
                bloom.threshold.value = @params["threshold"].ToObject<float>();
            }
            
            if (@params["scatter"] != null)
            {
                bloom.scatter.overrideState = true;
                bloom.scatter.value = @params["scatter"].ToObject<float>();
            }
            
            if (@params["tint"] != null)
            {
                var tint = @params["tint"].ToObject<float[]>();
                if (tint != null && tint.Length >= 3)
                {
                    bloom.tint.overrideState = true;
                    bloom.tint.value = new Color(tint[0], tint[1], tint[2], tint.Length >= 4 ? tint[3] : 1.0f);
                }
            }
            
            if (@params["clamp"] != null)
            {
                bloom.clamp.overrideState = true;
                bloom.clamp.value = @params["clamp"].ToObject<float>();
            }
            
            if (@params["high_quality_filtering"] != null)
            {
                bloom.highQualityFiltering.overrideState = true;
                bloom.highQualityFiltering.value = @params["high_quality_filtering"].ToObject<bool>();
            }

            var data = new
            {
                volume_name = volumeName,
                intensity = bloom.intensity.value,
                threshold = bloom.threshold.value,
                scatter = bloom.scatter.value,
                tint = new float[] { bloom.tint.value.r, bloom.tint.value.g, bloom.tint.value.b, bloom.tint.value.a },
                clamp = bloom.clamp.value,
                high_quality_filtering = bloom.highQualityFiltering.value
            };

            return Response.Success("Bloom effect configured successfully.", data);
        }

        private static object DisableBloomEffect(JObject @params)
        {
            string volumeName = @params["volume_name"]?.ToString() ?? "BloomVolume";
            
            GameObject volumeObj = GameObject.Find(volumeName);
            if (volumeObj != null)
            {
                Volume volume = volumeObj.GetComponent<Volume>();
                if (volume != null && volume.profile != null)
                {
                    if (volume.profile.TryGet<Bloom>(out var bloom))
                    {
                        bloom.intensity.overrideState = false;
                        bloom.threshold.overrideState = false;
                        bloom.scatter.overrideState = false;
                        bloom.tint.overrideState = false;
                        bloom.clamp.overrideState = false;
                        bloom.highQualityFiltering.overrideState = false;
                    }
                }
            }

            return Response.Success($"Bloom effect disabled on volume '{volumeName}'.");
        }

        private static object HandleColorGrading(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "configure":
                        return ConfigureColorGrading(@params);
                    case "apply":
                        return ApplyColorGrading(@params);
                    case "reset":
                        return ResetColorGrading(@params);
                    default:
                        return Response.Error($"Unknown color grading action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Color Grading {action}: {e.Message}");
            }
        }

        private static object ConfigureColorGrading(JObject @params)
        {
            string volumeName = @params["volume_name"]?.ToString() ?? "ColorGradingVolume";
            float temperature = @params["temperature"]?.ToObject<float>() ?? 0.0f;
            float tint = @params["tint"]?.ToObject<float>() ?? 0.0f;
            float postExposure = @params["post_exposure"]?.ToObject<float>() ?? 0.0f;
            float contrast = @params["contrast"]?.ToObject<float>() ?? 0.0f;
            float hueShift = @params["hue_shift"]?.ToObject<float>() ?? 0.0f;
            float saturation = @params["saturation"]?.ToObject<float>() ?? 0.0f;
            
            // Color curves
            var shadows = @params["shadows"]?.ToObject<float[]>();
            var midtones = @params["midtones"]?.ToObject<float[]>();
            var highlights = @params["highlights"]?.ToObject<float[]>();
            
            // Channel mixer
            var mixerRed = @params["mixer_red"]?.ToObject<float[]>();
            var mixerGreen = @params["mixer_green"]?.ToObject<float[]>();
            var mixerBlue = @params["mixer_blue"]?.ToObject<float[]>();

            // Find or create volume
            GameObject volumeObj = GameObject.Find(volumeName);
            Volume volume = null;
            
            if (volumeObj == null)
            {
                volumeObj = new GameObject(volumeName);
                volume = volumeObj.AddComponent<Volume>();
                volume.isGlobal = true;
                volume.priority = 1;
                
                // Create a new volume profile
                VolumeProfile profile = ScriptableObject.CreateInstance<VolumeProfile>();
                volume.profile = profile;
            }
            else
            {
                volume = volumeObj.GetComponent<Volume>();
                if (volume == null)
                {
                    volume = volumeObj.AddComponent<Volume>();
                    volume.isGlobal = true;
                    volume.priority = 1;
                }
                
                if (volume.profile == null)
                {
                    volume.profile = ScriptableObject.CreateInstance<VolumeProfile>();
                }
            }

            // Configure White Balance
            if (!volume.profile.TryGet<WhiteBalance>(out var whiteBalance))
            {
                whiteBalance = volume.profile.Add<WhiteBalance>(false);
            }
            
            whiteBalance.temperature.overrideState = true;
            whiteBalance.temperature.value = temperature;
            
            whiteBalance.tint.overrideState = true;
            whiteBalance.tint.value = tint;

            // Configure Color Adjustments
            if (!volume.profile.TryGet<ColorAdjustments>(out var colorAdj))
            {
                colorAdj = volume.profile.Add<ColorAdjustments>(false);
            }
            
            colorAdj.postExposure.overrideState = true;
            colorAdj.postExposure.value = postExposure;
            
            colorAdj.contrast.overrideState = true;
            colorAdj.contrast.value = contrast;
            
            colorAdj.hueShift.overrideState = true;
            colorAdj.hueShift.value = hueShift;
            
            colorAdj.saturation.overrideState = true;
            colorAdj.saturation.value = saturation;

            // Configure Shadow Midtone Highlight
            if (shadows != null || midtones != null || highlights != null)
            {
                if (!volume.profile.TryGet<ShadowsMidtonesHighlights>(out var smh))
                {
                    smh = volume.profile.Add<ShadowsMidtonesHighlights>(false);
                }
                
                if (shadows != null && shadows.Length >= 4)
                {
                    smh.shadows.overrideState = true;
                    smh.shadows.value = new Vector4(shadows[0], shadows[1], shadows[2], shadows[3]);
                }
                
                if (midtones != null && midtones.Length >= 4)
                {
                    smh.midtones.overrideState = true;
                    smh.midtones.value = new Vector4(midtones[0], midtones[1], midtones[2], midtones[3]);
                }
                
                if (highlights != null && highlights.Length >= 4)
                {
                    smh.highlights.overrideState = true;
                    smh.highlights.value = new Vector4(highlights[0], highlights[1], highlights[2], highlights[3]);
                }
            }

            // Configure Channel Mixer
            if (mixerRed != null || mixerGreen != null || mixerBlue != null)
            {
                if (!volume.profile.TryGet<ChannelMixer>(out var mixer))
                {
                    mixer = volume.profile.Add<ChannelMixer>(false);
                }
                
                if (mixerRed != null && mixerRed.Length >= 3)
                {
                    mixer.redOutRedIn.overrideState = true;
                    mixer.redOutRedIn.value = mixerRed[0];
                    mixer.redOutGreenIn.overrideState = true;
                    mixer.redOutGreenIn.value = mixerRed[1];
                    mixer.redOutBlueIn.overrideState = true;
                    mixer.redOutBlueIn.value = mixerRed[2];
                }
                
                if (mixerGreen != null && mixerGreen.Length >= 3)
                {
                    mixer.greenOutRedIn.overrideState = true;
                    mixer.greenOutRedIn.value = mixerGreen[0];
                    mixer.greenOutGreenIn.overrideState = true;
                    mixer.greenOutGreenIn.value = mixerGreen[1];
                    mixer.greenOutBlueIn.overrideState = true;
                    mixer.greenOutBlueIn.value = mixerGreen[2];
                }
                
                if (mixerBlue != null && mixerBlue.Length >= 3)
                {
                    mixer.blueOutRedIn.overrideState = true;
                    mixer.blueOutRedIn.value = mixerBlue[0];
                    mixer.blueOutGreenIn.overrideState = true;
                    mixer.blueOutGreenIn.value = mixerBlue[1];
                    mixer.blueOutBlueIn.overrideState = true;
                    mixer.blueOutBlueIn.value = mixerBlue[2];
                }
            }

            var data = new
            {
                volume_name = volumeName,
                temperature = temperature,
                tint = tint,
                post_exposure = postExposure,
                contrast = contrast,
                hue_shift = hueShift,
                saturation = saturation,
                shadows = shadows,
                midtones = midtones,
                highlights = highlights,
                mixer_red = mixerRed,
                mixer_green = mixerGreen,
                mixer_blue = mixerBlue
            };

            return Response.Success("Color grading configured successfully.", data);
        }

        private static object ApplyColorGrading(JObject @params)
        {
            string volumeName = @params["volume_name"]?.ToString() ?? "ColorGradingVolume";
            
            GameObject volumeObj = GameObject.Find(volumeName);
            if (volumeObj == null)
            {
                return Response.Error($"Color grading volume '{volumeName}' not found. Please configure it first.");
            }

            Volume volume = volumeObj.GetComponent<Volume>();
            if (volume == null || volume.profile == null)
            {
                return Response.Error("Volume or volume profile not found.");
            }

            // Enable the volume
            volume.enabled = true;
            volume.weight = 1.0f;

            var data = new
            {
                volume_name = volumeName,
                applied = true,
                weight = volume.weight,
                enabled = volume.enabled
            };

            return Response.Success("Color grading applied successfully.", data);
        }

        private static object ResetColorGrading(JObject @params)
        {
            string volumeName = @params["volume_name"]?.ToString() ?? "ColorGradingVolume";
            
            GameObject volumeObj = GameObject.Find(volumeName);
            if (volumeObj != null)
            {
                Volume volume = volumeObj.GetComponent<Volume>();
                if (volume != null && volume.profile != null)
                {
                    // Reset White Balance
                    if (volume.profile.TryGet<WhiteBalance>(out var whiteBalance))
                    {
                        whiteBalance.temperature.overrideState = false;
                        whiteBalance.tint.overrideState = false;
                    }
                    
                    // Reset Color Adjustments
                    if (volume.profile.TryGet<ColorAdjustments>(out var colorAdj))
                    {
                        colorAdj.postExposure.overrideState = false;
                        colorAdj.contrast.overrideState = false;
                        colorAdj.hueShift.overrideState = false;
                        colorAdj.saturation.overrideState = false;
                    }
                    
                    // Reset Shadow Midtone Highlight
                    if (volume.profile.TryGet<ShadowsMidtonesHighlights>(out var smh))
                    {
                        smh.shadows.overrideState = false;
                        smh.midtones.overrideState = false;
                        smh.highlights.overrideState = false;
                    }
                    
                    // Reset Channel Mixer
                    if (volume.profile.TryGet<ChannelMixer>(out var mixer))
                    {
                        mixer.redOutRedIn.overrideState = false;
                        mixer.redOutGreenIn.overrideState = false;
                        mixer.redOutBlueIn.overrideState = false;
                        mixer.greenOutRedIn.overrideState = false;
                        mixer.greenOutGreenIn.overrideState = false;
                        mixer.greenOutBlueIn.overrideState = false;
                        mixer.blueOutRedIn.overrideState = false;
                        mixer.blueOutGreenIn.overrideState = false;
                        mixer.blueOutBlueIn.overrideState = false;
                    }
                }
            }

            return Response.Success($"Color grading reset to default values on volume '{volumeName}'.");
        }

        private static object HandleDepthOfField(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "enable":
                        return EnableDepthOfField(@params);
                    case "configure":
                        return ConfigureDepthOfField(@params);
                    case "disable":
                        return DisableDepthOfField(@params);
                    default:
                        return Response.Error($"Unknown depth of field action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Depth of Field {action}: {e.Message}");
            }
        }

        private static object EnableDepthOfField(JObject @params)
        {
            string volumeName = @params["volume_name"]?.ToString() ?? "DepthOfFieldVolume";
            string mode = @params["mode"]?.ToString()?.ToLower() ?? "bokeh";
            float focusDistance = @params["focus_distance"]?.ToObject<float>() ?? 10.0f;
            float focalLength = @params["focal_length"]?.ToObject<float>() ?? 50.0f;
            float aperture = @params["aperture"]?.ToObject<float>() ?? 5.6f;
            int bladeCount = @params["blade_count"]?.ToObject<int>() ?? 5;
            float bladeCurvature = @params["blade_curvature"]?.ToObject<float>() ?? 1.0f;
            float bladeRotation = @params["blade_rotation"]?.ToObject<float>() ?? 0.0f;
            
            // Gaussian mode specific parameters
            float gaussianStart = @params["gaussian_start"]?.ToObject<float>() ?? 10.0f;
            float gaussianEnd = @params["gaussian_end"]?.ToObject<float>() ?? 30.0f;
            float gaussianMaxRadius = @params["gaussian_max_radius"]?.ToObject<float>() ?? 1.0f;
            bool highQualitySampling = @params["high_quality_sampling"]?.ToObject<bool>() ?? false;

            // Find or create volume
            GameObject volumeObj = GameObject.Find(volumeName);
            Volume volume = null;
            
            if (volumeObj == null)
            {
                volumeObj = new GameObject(volumeName);
                volume = volumeObj.AddComponent<Volume>();
                volume.isGlobal = true;
                volume.priority = 1;
                
                // Create a new volume profile
                VolumeProfile profile = ScriptableObject.CreateInstance<VolumeProfile>();
                volume.profile = profile;
            }
            else
            {
                volume = volumeObj.GetComponent<Volume>();
                if (volume == null)
                {
                    volume = volumeObj.AddComponent<Volume>();
                    volume.isGlobal = true;
                    volume.priority = 1;
                }
                
                if (volume.profile == null)
                {
                    volume.profile = ScriptableObject.CreateInstance<VolumeProfile>();
                }
            }

            // Get or add DepthOfField component
            if (!volume.profile.TryGet<DepthOfField>(out var dof))
            {
                dof = volume.profile.Add<DepthOfField>(false);
            }

            // Configure mode
            dof.mode.overrideState = true;
            switch (mode)
            {
                case "gaussian":
                    dof.mode.value = DepthOfFieldMode.Gaussian;
                    
                    // Configure Gaussian specific settings
                    dof.gaussianStart.overrideState = true;
                    dof.gaussianStart.value = gaussianStart;
                    
                    dof.gaussianEnd.overrideState = true;
                    dof.gaussianEnd.value = gaussianEnd;
                    
                    dof.gaussianMaxRadius.overrideState = true;
                    dof.gaussianMaxRadius.value = gaussianMaxRadius;
                    
                    dof.highQualitySampling.overrideState = true;
                    dof.highQualitySampling.value = highQualitySampling;
                    break;
                    
                case "bokeh":
                default:
                    dof.mode.value = DepthOfFieldMode.Bokeh;
                    
                    // Configure Bokeh specific settings
                    dof.focusDistance.overrideState = true;
                    dof.focusDistance.value = focusDistance;
                    
                    dof.focalLength.overrideState = true;
                    dof.focalLength.value = focalLength;
                    
                    dof.aperture.overrideState = true;
                    dof.aperture.value = aperture;
                    
                    dof.bladeCount.overrideState = true;
                    dof.bladeCount.value = bladeCount;
                    
                    dof.bladeCurvature.overrideState = true;
                    dof.bladeCurvature.value = bladeCurvature;
                    
                    dof.bladeRotation.overrideState = true;
                    dof.bladeRotation.value = bladeRotation;
                    break;
            }

            // Apply depth texture mode to cameras
            Camera[] cameras = UnityEngine.Object.FindObjectsByType<Camera>(FindObjectsSortMode.None);
            foreach (var camera in cameras)
            {
                camera.depthTextureMode |= DepthTextureMode.Depth;
            }

            var data = new
            {
                volume_name = volumeName,
                enabled = true,
                mode = mode,
                focus_distance = focusDistance,
                focal_length = focalLength,
                aperture = aperture,
                blade_count = bladeCount,
                blade_curvature = bladeCurvature,
                blade_rotation = bladeRotation,
                gaussian_start = gaussianStart,
                gaussian_end = gaussianEnd,
                gaussian_max_radius = gaussianMaxRadius,
                high_quality_sampling = highQualitySampling,
                applied_to_cameras = cameras.Length
            };

            return Response.Success("Depth of field enabled successfully.", data);
        }

        private static object ConfigureDepthOfField(JObject @params)
        {
            string volumeName = @params["volume_name"]?.ToString() ?? "DepthOfFieldVolume";
            
            GameObject volumeObj = GameObject.Find(volumeName);
            if (volumeObj == null)
            {
                return Response.Error($"Depth of field volume '{volumeName}' not found. Please enable it first.");
            }

            Volume volume = volumeObj.GetComponent<Volume>();
            if (volume == null || volume.profile == null)
            {
                return Response.Error("Volume or volume profile not found.");
            }

            // Get DepthOfField component
            if (!volume.profile.TryGet<DepthOfField>(out var dof))
            {
                return Response.Error("Depth of field effect not found in volume profile.");
            }

            // Update mode if provided
            if (@params["mode"] != null)
            {
                string mode = @params["mode"].ToString().ToLower();
                dof.mode.overrideState = true;
                dof.mode.value = mode == "gaussian" ? DepthOfFieldMode.Gaussian : DepthOfFieldMode.Bokeh;
            }

            // Update Bokeh properties if provided
            if (@params["focus_distance"] != null)
            {
                dof.focusDistance.overrideState = true;
                dof.focusDistance.value = @params["focus_distance"].ToObject<float>();
            }
            
            if (@params["focal_length"] != null)
            {
                dof.focalLength.overrideState = true;
                dof.focalLength.value = @params["focal_length"].ToObject<float>();
            }
            
            if (@params["aperture"] != null)
            {
                dof.aperture.overrideState = true;
                dof.aperture.value = @params["aperture"].ToObject<float>();
            }
            
            if (@params["blade_count"] != null)
            {
                dof.bladeCount.overrideState = true;
                dof.bladeCount.value = @params["blade_count"].ToObject<int>();
            }
            
            if (@params["blade_curvature"] != null)
            {
                dof.bladeCurvature.overrideState = true;
                dof.bladeCurvature.value = @params["blade_curvature"].ToObject<float>();
            }
            
            if (@params["blade_rotation"] != null)
            {
                dof.bladeRotation.overrideState = true;
                dof.bladeRotation.value = @params["blade_rotation"].ToObject<float>();
            }

            // Update Gaussian properties if provided
            if (@params["gaussian_start"] != null)
            {
                dof.gaussianStart.overrideState = true;
                dof.gaussianStart.value = @params["gaussian_start"].ToObject<float>();
            }
            
            if (@params["gaussian_end"] != null)
            {
                dof.gaussianEnd.overrideState = true;
                dof.gaussianEnd.value = @params["gaussian_end"].ToObject<float>();
            }
            
            if (@params["gaussian_max_radius"] != null)
            {
                dof.gaussianMaxRadius.overrideState = true;
                dof.gaussianMaxRadius.value = @params["gaussian_max_radius"].ToObject<float>();
            }
            
            if (@params["high_quality_sampling"] != null)
            {
                dof.highQualitySampling.overrideState = true;
                dof.highQualitySampling.value = @params["high_quality_sampling"].ToObject<bool>();
            }

            var data = new
            {
                volume_name = volumeName,
                mode = dof.mode.value.ToString(),
                focus_distance = dof.focusDistance.value,
                focal_length = dof.focalLength.value,
                aperture = dof.aperture.value,
                blade_count = dof.bladeCount.value,
                blade_curvature = dof.bladeCurvature.value,
                blade_rotation = dof.bladeRotation.value,
                gaussian_start = dof.gaussianStart.value,
                gaussian_end = dof.gaussianEnd.value,
                gaussian_max_radius = dof.gaussianMaxRadius.value,
                high_quality_sampling = dof.highQualitySampling.value
            };

            return Response.Success("Depth of field configured successfully.", data);
        }

        private static object DisableDepthOfField(JObject @params)
        {
            string volumeName = @params["volume_name"]?.ToString() ?? "DepthOfFieldVolume";
            
            GameObject volumeObj = GameObject.Find(volumeName);
            if (volumeObj != null)
            {
                Volume volume = volumeObj.GetComponent<Volume>();
                if (volume != null && volume.profile != null)
                {
                    if (volume.profile.TryGet<DepthOfField>(out var dof))
                    {
                        dof.mode.overrideState = false;
                        dof.focusDistance.overrideState = false;
                        dof.focalLength.overrideState = false;
                        dof.aperture.overrideState = false;
                        dof.bladeCount.overrideState = false;
                        dof.bladeCurvature.overrideState = false;
                        dof.bladeRotation.overrideState = false;
                        dof.gaussianStart.overrideState = false;
                        dof.gaussianEnd.overrideState = false;
                        dof.gaussianMaxRadius.overrideState = false;
                        dof.highQualitySampling.overrideState = false;
                    }
                }
            }

            return Response.Success($"Depth of field disabled on volume '{volumeName}'.");
        }

        private static object HandleMotionBlur(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "enable":
                        return EnableMotionBlur(@params);
                    case "configure":
                        return ConfigureMotionBlur(@params);
                    case "disable":
                        return DisableMotionBlur();
                    default:
                        return Response.Error($"Unknown motion blur action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Motion Blur {action}: {e.Message}");
            }
        }

        private static object EnableMotionBlur(JObject @params)
        {
            float shutterAngle = @params["shutter_angle"]?.ToObject<float>() ?? 270.0f;
            int sampleCount = @params["sample_count"]?.ToObject<int>() ?? 8;
            float intensity = @params["intensity"]?.ToObject<float>() ?? 1.0f;
            float minimumVelocity = @params["minimum_velocity"]?.ToObject<float>() ?? 2.0f;
            float maximumVelocity = @params["maximum_velocity"]?.ToObject<float>() ?? 200.0f;
            bool cameraRotationBlur = @params["camera_rotation_blur"]?.ToObject<bool>() ?? true;

            // Create or find motion blur object
            GameObject motionBlurObj = GameObject.Find("MotionBlur");
            if (motionBlurObj == null)
            {
                motionBlurObj = new GameObject("MotionBlur");
            }

            var motionBlurData = motionBlurObj.GetComponent<MotionBlurData>();
            if (motionBlurData == null)
            {
                motionBlurData = motionBlurObj.AddComponent<MotionBlurData>();
            }

            motionBlurData.enabled = true;
            motionBlurData.shutterAngle = shutterAngle;
            motionBlurData.sampleCount = sampleCount;
            motionBlurData.intensity = intensity;
            motionBlurData.minimumVelocity = minimumVelocity;
            motionBlurData.maximumVelocity = maximumVelocity;
            motionBlurData.cameraRotationBlur = cameraRotationBlur;

            // Apply to all cameras for motion vector generation
            Camera[] cameras = UnityEngine.Object.FindObjectsByType<Camera>(FindObjectsSortMode.None);
            foreach (var camera in cameras)
            {
                camera.depthTextureMode |= DepthTextureMode.MotionVectors;
            }

            var data = new
            {
                enabled = true,
                shutter_angle = shutterAngle,
                sample_count = sampleCount,
                intensity = intensity,
                minimum_velocity = minimumVelocity,
                maximum_velocity = maximumVelocity,
                camera_rotation_blur = cameraRotationBlur,
                applied_to_cameras = cameras.Length
            };

            return Response.Success("Motion blur enabled successfully.", data);
        }

        private static object ConfigureMotionBlur(JObject @params)
        {
            GameObject motionBlurObj = GameObject.Find("MotionBlur");
            if (motionBlurObj == null)
            {
                return Response.Error("Motion blur not found. Please enable it first.");
            }

            var motionBlurData = motionBlurObj.GetComponent<MotionBlurData>();
            if (motionBlurData == null)
            {
                return Response.Error("Motion blur data not found.");
            }

            // Update properties if provided
            if (@params["shutter_angle"] != null)
                motionBlurData.shutterAngle = @params["shutter_angle"].ToObject<float>();
            if (@params["sample_count"] != null)
                motionBlurData.sampleCount = @params["sample_count"].ToObject<int>();
            if (@params["intensity"] != null)
                motionBlurData.intensity = @params["intensity"].ToObject<float>();
            if (@params["minimum_velocity"] != null)
                motionBlurData.minimumVelocity = @params["minimum_velocity"].ToObject<float>();
            if (@params["maximum_velocity"] != null)
                motionBlurData.maximumVelocity = @params["maximum_velocity"].ToObject<float>();
            if (@params["camera_rotation_blur"] != null)
                motionBlurData.cameraRotationBlur = @params["camera_rotation_blur"].ToObject<bool>();

            var data = new
            {
                enabled = motionBlurData.enabled,
                shutter_angle = motionBlurData.shutterAngle,
                sample_count = motionBlurData.sampleCount,
                intensity = motionBlurData.intensity,
                minimum_velocity = motionBlurData.minimumVelocity,
                maximum_velocity = motionBlurData.maximumVelocity,
                camera_rotation_blur = motionBlurData.cameraRotationBlur
            };

            return Response.Success("Motion blur configured successfully.", data);
        }

        private static object DisableMotionBlur()
        {
            GameObject motionBlurObj = GameObject.Find("MotionBlur");
            if (motionBlurObj != null)
            {
                var motionBlurData = motionBlurObj.GetComponent<MotionBlurData>();
                if (motionBlurData != null)
                {
                    motionBlurData.enabled = false;
                }
            }

            return Response.Success("Motion blur disabled successfully.");
        }

        private static object HandleScreenSpaceReflections(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "enable":
                        return EnableScreenSpaceReflections(@params);
                    case "configure":
                        return ConfigureScreenSpaceReflections(@params);
                    case "disable":
                        return DisableScreenSpaceReflections();
                    default:
                        return Response.Error($"Unknown screen space reflections action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Screen Space Reflections {action}: {e.Message}");
            }
        }

        private static object EnableScreenSpaceReflections(JObject @params)
        {
            float intensity = @params["intensity"]?.ToObject<float>() ?? 1.0f;
            float thickness = @params["thickness"]?.ToObject<float>() ?? 0.5f;
            float maxDistance = @params["max_distance"]?.ToObject<float>() ?? 1000.0f;
            int iterationCount = @params["iteration_count"]?.ToObject<int>() ?? 16;
            int stepCount = @params["step_count"]?.ToObject<int>() ?? 4;
            float smoothnessFadeStart = @params["smoothness_fade_start"]?.ToObject<float>() ?? 0.0f;
            float smoothnessFadeEnd = @params["smoothness_fade_end"]?.ToObject<float>() ?? 0.9f;
            bool enableVignette = @params["enable_vignette"]?.ToObject<bool>() ?? true;

            // Create or find SSR object
            GameObject ssrObj = GameObject.Find("ScreenSpaceReflections");
            if (ssrObj == null)
            {
                ssrObj = new GameObject("ScreenSpaceReflections");
            }

            var ssrData = ssrObj.GetComponent<ScreenSpaceReflectionsData>();
            if (ssrData == null)
            {
                ssrData = ssrObj.AddComponent<ScreenSpaceReflectionsData>();
            }

            ssrData.enabled = true;
            ssrData.intensity = intensity;
            ssrData.thickness = thickness;
            ssrData.maxDistance = maxDistance;
            ssrData.iterationCount = iterationCount;
            ssrData.stepCount = stepCount;
            ssrData.smoothnessFadeStart = smoothnessFadeStart;
            ssrData.smoothnessFadeEnd = smoothnessFadeEnd;
            ssrData.enableVignette = enableVignette;

            // Apply depth texture mode to cameras
            Camera[] cameras = UnityEngine.Object.FindObjectsByType<Camera>(FindObjectsSortMode.None);
            foreach (var camera in cameras)
            {
                camera.depthTextureMode |= DepthTextureMode.Depth | DepthTextureMode.DepthNormals;
            }

            var data = new
            {
                enabled = true,
                intensity = intensity,
                thickness = thickness,
                max_distance = maxDistance,
                iteration_count = iterationCount,
                step_count = stepCount,
                smoothness_fade_start = smoothnessFadeStart,
                smoothness_fade_end = smoothnessFadeEnd,
                enable_vignette = enableVignette,
                applied_to_cameras = cameras.Length
            };

            return Response.Success("Screen space reflections enabled successfully.", data);
        }

        private static object ConfigureScreenSpaceReflections(JObject @params)
        {
            GameObject ssrObj = GameObject.Find("ScreenSpaceReflections");
            if (ssrObj == null)
            {
                return Response.Error("Screen space reflections not found. Please enable them first.");
            }

            var ssrData = ssrObj.GetComponent<ScreenSpaceReflectionsData>();
            if (ssrData == null)
            {
                return Response.Error("Screen space reflections data not found.");
            }

            // Update properties if provided
            if (@params["intensity"] != null)
                ssrData.intensity = @params["intensity"].ToObject<float>();
            if (@params["thickness"] != null)
                ssrData.thickness = @params["thickness"].ToObject<float>();
            if (@params["max_distance"] != null)
                ssrData.maxDistance = @params["max_distance"].ToObject<float>();
            if (@params["iteration_count"] != null)
                ssrData.iterationCount = @params["iteration_count"].ToObject<int>();
            if (@params["step_count"] != null)
                ssrData.stepCount = @params["step_count"].ToObject<int>();
            if (@params["smoothness_fade_start"] != null)
                ssrData.smoothnessFadeStart = @params["smoothness_fade_start"].ToObject<float>();
            if (@params["smoothness_fade_end"] != null)
                ssrData.smoothnessFadeEnd = @params["smoothness_fade_end"].ToObject<float>();
            if (@params["enable_vignette"] != null)
                ssrData.enableVignette = @params["enable_vignette"].ToObject<bool>();

            var data = new
            {
                enabled = ssrData.enabled,
                intensity = ssrData.intensity,
                thickness = ssrData.thickness,
                max_distance = ssrData.maxDistance,
                iteration_count = ssrData.iterationCount,
                step_count = ssrData.stepCount,
                smoothness_fade_start = ssrData.smoothnessFadeStart,
                smoothness_fade_end = ssrData.smoothnessFadeEnd,
                enable_vignette = ssrData.enableVignette
            };

            return Response.Success("Screen space reflections configured successfully.", data);
        }

        private static object DisableScreenSpaceReflections()
        {
            GameObject ssrObj = GameObject.Find("ScreenSpaceReflections");
            if (ssrObj != null)
            {
                var ssrData = ssrObj.GetComponent<ScreenSpaceReflectionsData>();
                if (ssrData != null)
                {
                    ssrData.enabled = false;
                }
            }

            return Response.Success("Screen space reflections disabled successfully.");
        }

        private static object HandleAmbientOcclusion(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "enable":
                        return EnableAmbientOcclusion(@params);
                    case "configure":
                        return ConfigureAmbientOcclusion(@params);
                    case "disable":
                        return DisableAmbientOcclusion();
                    default:
                        return Response.Error($"Unknown ambient occlusion action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Ambient Occlusion {action}: {e.Message}");
            }
        }

        private static object EnableAmbientOcclusion(JObject @params)
        {
            float intensity = @params["intensity"]?.ToObject<float>() ?? 1.0f;
            float radius = @params["radius"]?.ToObject<float>() ?? 0.3f;
            float power = @params["power"]?.ToObject<float>() ?? 0.302f;
            float bias = @params["bias"]?.ToObject<float>() ?? 0.05f;
            float thicknessModifier = @params["thickness_modifier"]?.ToObject<float>() ?? 1.0f;
            float directLightingStrength = @params["direct_lighting_strength"]?.ToObject<float>() ?? 0.25f;
            int sampleCount = @params["sample_count"]?.ToObject<int>() ?? 6;
            bool downsampling = @params["downsampling"]?.ToObject<bool>() ?? false;

            // Create or find AO object
            GameObject aoObj = GameObject.Find("AmbientOcclusion");
            if (aoObj == null)
            {
                aoObj = new GameObject("AmbientOcclusion");
            }

            var aoData = aoObj.GetComponent<AmbientOcclusionData>();
            if (aoData == null)
            {
                aoData = aoObj.AddComponent<AmbientOcclusionData>();
            }

            aoData.enabled = true;
            aoData.intensity = intensity;
            aoData.radius = radius;
            aoData.power = power;
            aoData.bias = bias;
            aoData.thicknessModifier = thicknessModifier;
            aoData.directLightingStrength = directLightingStrength;
            aoData.sampleCount = sampleCount;
            aoData.downsampling = downsampling;

            // Apply depth texture mode to cameras
            Camera[] cameras = UnityEngine.Object.FindObjectsByType<Camera>(FindObjectsSortMode.None);
            foreach (var camera in cameras)
            {
                camera.depthTextureMode |= DepthTextureMode.Depth | DepthTextureMode.DepthNormals;
            }

            var data = new
            {
                enabled = true,
                intensity = intensity,
                radius = radius,
                power = power,
                bias = bias,
                thickness_modifier = thicknessModifier,
                direct_lighting_strength = directLightingStrength,
                sample_count = sampleCount,
                downsampling = downsampling,
                applied_to_cameras = cameras.Length
            };

            return Response.Success("Ambient occlusion enabled successfully.", data);
        }

        private static object ConfigureAmbientOcclusion(JObject @params)
        {
            GameObject aoObj = GameObject.Find("AmbientOcclusion");
            if (aoObj == null)
            {
                return Response.Error("Ambient occlusion not found. Please enable it first.");
            }

            var aoData = aoObj.GetComponent<AmbientOcclusionData>();
            if (aoData == null)
            {
                return Response.Error("Ambient occlusion data not found.");
            }

            // Update properties if provided
            if (@params["intensity"] != null)
                aoData.intensity = @params["intensity"].ToObject<float>();
            if (@params["radius"] != null)
                aoData.radius = @params["radius"].ToObject<float>();
            if (@params["power"] != null)
                aoData.power = @params["power"].ToObject<float>();
            if (@params["bias"] != null)
                aoData.bias = @params["bias"].ToObject<float>();
            if (@params["thickness_modifier"] != null)
                aoData.thicknessModifier = @params["thickness_modifier"].ToObject<float>();
            if (@params["direct_lighting_strength"] != null)
                aoData.directLightingStrength = @params["direct_lighting_strength"].ToObject<float>();
            if (@params["sample_count"] != null)
                aoData.sampleCount = @params["sample_count"].ToObject<int>();
            if (@params["downsampling"] != null)
                aoData.downsampling = @params["downsampling"].ToObject<bool>();

            var data = new
            {
                enabled = aoData.enabled,
                intensity = aoData.intensity,
                radius = aoData.radius,
                power = aoData.power,
                bias = aoData.bias,
                thickness_modifier = aoData.thicknessModifier,
                direct_lighting_strength = aoData.directLightingStrength,
                sample_count = aoData.sampleCount,
                downsampling = aoData.downsampling
            };

            return Response.Success("Ambient occlusion configured successfully.", data);
        }

        private static object DisableAmbientOcclusion()
        {
            GameObject aoObj = GameObject.Find("AmbientOcclusion");
            if (aoObj != null)
            {
                var aoData = aoObj.GetComponent<AmbientOcclusionData>();
                if (aoData != null)
                {
                    aoData.enabled = false;
                }
            }

            return Response.Success("Ambient occlusion disabled successfully.");
        }

        private static object HandleFogEffects(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "enable":
                        return EnableFogEffects(@params);
                    case "configure":
                        return ConfigureFogEffects(@params);
                    case "disable":
                        return DisableFogEffects();
                    default:
                        return Response.Error($"Unknown fog effects action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Fog Effects {action}: {e.Message}");
            }
        }

        private static object EnableFogEffects(JObject @params)
        {
            bool fogEnabled = @params["fog_enabled"]?.ToObject<bool>() ?? true;
            string fogMode = @params["fog_mode"]?.ToString() ?? "exponentialsquared";
            float fogDensity = @params["fog_density"]?.ToObject<float>() ?? 0.01f;
            float linearFogStart = @params["linear_fog_start"]?.ToObject<float>() ?? 0.0f;
            float linearFogEnd = @params["linear_fog_end"]?.ToObject<float>() ?? 300.0f;
            var fogColor = @params["fog_color"]?.ToObject<float[]>();

            // Configure Unity's global fog
            RenderSettings.fog = fogEnabled;
            
            // Set fog mode
            switch (fogMode.ToLower())
            {
                case "linear":
                    RenderSettings.fogMode = FogMode.Linear;
                    RenderSettings.fogStartDistance = linearFogStart;
                    RenderSettings.fogEndDistance = linearFogEnd;
                    break;
                case "exponential":
                    RenderSettings.fogMode = FogMode.Exponential;
                    RenderSettings.fogDensity = fogDensity;
                    break;
                case "exponentialsquared":
                default:
                    RenderSettings.fogMode = FogMode.ExponentialSquared;
                    RenderSettings.fogDensity = fogDensity;
                    break;
            }

            // Set fog color if provided
            if (fogColor != null && fogColor.Length >= 3)
            {
                Color color = new Color(fogColor[0], fogColor[1], fogColor[2], 
                    fogColor.Length >= 4 ? fogColor[3] : 1.0f);
                RenderSettings.fogColor = color;
            }

            // Create fog data object for tracking
            GameObject fogObj = GameObject.Find("FogEffects");
            if (fogObj == null)
            {
                fogObj = new GameObject("FogEffects");
            }

            var fogData = fogObj.GetComponent<FogEffectsData>();
            if (fogData == null)
            {
                fogData = fogObj.AddComponent<FogEffectsData>();
            }

            fogData.enabled = fogEnabled;
            fogData.fogMode = fogMode;
            fogData.fogDensity = fogDensity;
            fogData.linearFogStart = linearFogStart;
            fogData.linearFogEnd = linearFogEnd;
            fogData.fogColor = fogColor;

            var data = new
            {
                fog_enabled = fogEnabled,
                fog_mode = fogMode,
                fog_density = fogDensity,
                linear_fog_start = linearFogStart,
                linear_fog_end = linearFogEnd,
                fog_color = fogColor
            };

            return Response.Success("Fog effects enabled successfully.", data);
        }

        private static object ConfigureFogEffects(JObject @params)
        {
            GameObject fogObj = GameObject.Find("FogEffects");
            if (fogObj == null)
            {
                return Response.Error("Fog effects not found. Please enable them first.");
            }

            var fogData = fogObj.GetComponent<FogEffectsData>();
            if (fogData == null)
            {
                return Response.Error("Fog effects data not found.");
            }

            // Update properties if provided
            if (@params["fog_enabled"] != null)
            {
                bool fogEnabled = @params["fog_enabled"].ToObject<bool>();
                RenderSettings.fog = fogEnabled;
                fogData.enabled = fogEnabled;
            }

            if (@params["fog_mode"] != null)
            {
                string fogMode = @params["fog_mode"].ToString().ToLower();
                switch (fogMode)
                {
                    case "linear":
                        RenderSettings.fogMode = FogMode.Linear;
                        break;
                    case "exponential":
                        RenderSettings.fogMode = FogMode.Exponential;
                        break;
                    case "exponentialsquared":
                        RenderSettings.fogMode = FogMode.ExponentialSquared;
                        break;
                }
                fogData.fogMode = fogMode;
            }

            if (@params["fog_density"] != null)
            {
                float density = @params["fog_density"].ToObject<float>();
                RenderSettings.fogDensity = density;
                fogData.fogDensity = density;
            }

            if (@params["linear_fog_start"] != null)
            {
                float start = @params["linear_fog_start"].ToObject<float>();
                RenderSettings.fogStartDistance = start;
                fogData.linearFogStart = start;
            }

            if (@params["linear_fog_end"] != null)
            {
                float end = @params["linear_fog_end"].ToObject<float>();
                RenderSettings.fogEndDistance = end;
                fogData.linearFogEnd = end;
            }

            if (@params["fog_color"] != null)
            {
                var fogColor = @params["fog_color"].ToObject<float[]>();
                if (fogColor != null && fogColor.Length >= 3)
                {
                    Color color = new Color(fogColor[0], fogColor[1], fogColor[2], 
                        fogColor.Length >= 4 ? fogColor[3] : 1.0f);
                    RenderSettings.fogColor = color;
                    fogData.fogColor = fogColor;
                }
            }

            var data = new
            {
                fog_enabled = fogData.enabled,
                fog_mode = fogData.fogMode,
                fog_density = fogData.fogDensity,
                linear_fog_start = fogData.linearFogStart,
                linear_fog_end = fogData.linearFogEnd,
                fog_color = fogData.fogColor
            };

            return Response.Success("Fog effects configured successfully.", data);
        }

        private static object DisableFogEffects()
        {
            RenderSettings.fog = false;

            GameObject fogObj = GameObject.Find("FogEffects");
            if (fogObj != null)
            {
                var fogData = fogObj.GetComponent<FogEffectsData>();
                if (fogData != null)
                {
                    fogData.enabled = false;
                }
            }

            return Response.Success("Fog effects disabled successfully.");
        }

        private static object HandleCaustics(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "create":
                        return CreateCaustics(@params);
                    case "configure":
                        return ConfigureCaustics(@params);
                    case "delete":
                        return DeleteCaustics(@params);
                    default:
                        return Response.Error($"Unknown caustics action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Caustics {action}: {e.Message}");
            }
        }

        private static object CreateCaustics(JObject @params)
        {
            string causticsName = @params["caustics_name"]?.ToString() ?? "Caustics";
            float intensity = @params["intensity"]?.ToObject<float>() ?? 1.0f;
            float scale = @params["scale"]?.ToObject<float>() ?? 1.0f;
            var position = @params["position"]?.ToObject<float[]>();
            var size = @params["size"]?.ToObject<float[]>();
            float speed = @params["speed"]?.ToObject<float>() ?? 1.0f;
            string causticsTexture = @params["caustics_texture"]?.ToString();
            var lightDirection = @params["light_direction"]?.ToObject<float[]>();

            GameObject causticsObj = new GameObject(causticsName);
            var causticsData = causticsObj.AddComponent<CausticsData>();

            causticsData.intensity = intensity;
            causticsData.scale = scale;
            causticsData.speed = speed;
            causticsData.causticsTexture = causticsTexture;

            if (position != null && position.Length >= 3)
            {
                causticsObj.transform.position = new Vector3(position[0], position[1], position[2]);
            }

            if (size != null && size.Length >= 3)
            {
                causticsData.size = new Vector3(size[0], size[1], size[2]);
            }

            if (lightDirection != null && lightDirection.Length >= 3)
            {
                causticsData.lightDirection = new Vector3(lightDirection[0], lightDirection[1], lightDirection[2]);
            }

            // Load caustics texture if provided
            Texture2D texture = null;
            if (!string.IsNullOrEmpty(causticsTexture))
            {
                texture = AssetDatabase.LoadAssetAtPath<Texture2D>(causticsTexture);
                if (texture == null)
                {
                    Debug.LogWarning($"Caustics texture not found at path: {causticsTexture}");
                }
            }

            var data = new
            {
                name = causticsName,
                intensity = intensity,
                scale = scale,
                position = position,
                size = size,
                speed = speed,
                caustics_texture = causticsTexture,
                light_direction = lightDirection,
                texture_loaded = texture != null
            };

            return Response.Success($"Caustics '{causticsName}' created successfully.", data);
        }

        private static object ConfigureCaustics(JObject @params)
        {
            string causticsName = @params["caustics_name"]?.ToString();
            
            if (string.IsNullOrEmpty(causticsName))
            {
                return Response.Error("Caustics name is required for configuration.");
            }

            GameObject causticsObj = GameObject.Find(causticsName);
            if (causticsObj == null)
            {
                return Response.Error($"Caustics '{causticsName}' not found.");
            }

            var causticsData = causticsObj.GetComponent<CausticsData>();
            if (causticsData == null)
            {
                return Response.Error($"Caustics data not found on '{causticsName}'.");
            }

            // Update properties if provided
            if (@params["intensity"] != null)
                causticsData.intensity = @params["intensity"].ToObject<float>();
            if (@params["scale"] != null)
                causticsData.scale = @params["scale"].ToObject<float>();
            if (@params["speed"] != null)
                causticsData.speed = @params["speed"].ToObject<float>();
            if (@params["caustics_texture"] != null)
                causticsData.causticsTexture = @params["caustics_texture"].ToString();

            if (@params["position"] != null)
            {
                var position = @params["position"].ToObject<float[]>();
                if (position != null && position.Length >= 3)
                {
                    causticsObj.transform.position = new Vector3(position[0], position[1], position[2]);
                }
            }

            if (@params["size"] != null)
            {
                var size = @params["size"].ToObject<float[]>();
                if (size != null && size.Length >= 3)
                {
                    causticsData.size = new Vector3(size[0], size[1], size[2]);
                }
            }

            if (@params["light_direction"] != null)
            {
                var lightDirection = @params["light_direction"].ToObject<float[]>();
                if (lightDirection != null && lightDirection.Length >= 3)
                {
                    causticsData.lightDirection = new Vector3(lightDirection[0], lightDirection[1], lightDirection[2]);
                }
            }

            var data = new
            {
                name = causticsName,
                intensity = causticsData.intensity,
                scale = causticsData.scale,
                speed = causticsData.speed,
                caustics_texture = causticsData.causticsTexture,
                size = new float[] { causticsData.size.x, causticsData.size.y, causticsData.size.z },
                light_direction = new float[] { causticsData.lightDirection.x, causticsData.lightDirection.y, causticsData.lightDirection.z }
            };

            return Response.Success($"Caustics '{causticsName}' configured successfully.", data);
        }

        private static object DeleteCaustics(JObject @params)
        {
            string causticsName = @params["caustics_name"]?.ToString();
            
            if (string.IsNullOrEmpty(causticsName))
            {
                // Delete all caustics
                CausticsData[] allCaustics = UnityEngine.Object.FindObjectsByType<CausticsData>(FindObjectsSortMode.None);
                foreach (var caustics in allCaustics)
                {
                    UnityEngine.Object.DestroyImmediate(caustics.gameObject);
                }
                return Response.Success($"Deleted {allCaustics.Length} caustics.");
            }
            else
            {
                GameObject causticsObj = GameObject.Find(causticsName);
                if (causticsObj != null && causticsObj.GetComponent<CausticsData>() != null)
                {
                    UnityEngine.Object.DestroyImmediate(causticsObj);
                    return Response.Success($"Caustics '{causticsName}' deleted successfully.");
                }
                else
                {
                    return Response.Error($"Caustics '{causticsName}' not found.");
                }
            }
        }

        private static object HandleLightShafts(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "create":
                        return CreateLightShafts(@params);
                    case "configure":
                        return ConfigureLightShafts(@params);
                    case "delete":
                        return DeleteLightShafts(@params);
                    default:
                        return Response.Error($"Unknown light shafts action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Light Shafts {action}: {e.Message}");
            }
        }

        private static object CreateLightShafts(JObject @params)
        {
            string lightShaftsName = @params["light_shafts_name"]?.ToString() ?? "LightShafts";
            string lightName = @params["light_name"]?.ToString();
            float intensity = @params["intensity"]?.ToObject<float>() ?? 1.0f;
            float threshold = @params["threshold"]?.ToObject<float>() ?? 0.75f;
            float sunShaftBlurRadius = @params["sun_shaft_blur_radius"]?.ToObject<float>() ?? 2.5f;
            float sunShaftIntensity = @params["sun_shaft_intensity"]?.ToObject<float>() ?? 1.15f;
            int radialBlurIterations = @params["radial_blur_iterations"]?.ToObject<int>() ?? 2;
            var sunColor = @params["sun_color"]?.ToObject<float[]>();
            bool useDepthTexture = @params["use_depth_texture"]?.ToObject<bool>() ?? true;

            GameObject lightShaftsObj = new GameObject(lightShaftsName);
            var lightShaftsData = lightShaftsObj.AddComponent<LightShaftsData>();

            lightShaftsData.lightName = lightName;
            lightShaftsData.intensity = intensity;
            lightShaftsData.threshold = threshold;
            lightShaftsData.sunShaftBlurRadius = sunShaftBlurRadius;
            lightShaftsData.sunShaftIntensity = sunShaftIntensity;
            lightShaftsData.radialBlurIterations = radialBlurIterations;
            lightShaftsData.useDepthTexture = useDepthTexture;

            if (sunColor != null && sunColor.Length >= 3)
            {
                lightShaftsData.sunColor = new Color(sunColor[0], sunColor[1], sunColor[2], 
                    sunColor.Length >= 4 ? sunColor[3] : 1.0f);
            }

            // Find and link the light if specified
            Light targetLight = null;
            if (!string.IsNullOrEmpty(lightName))
            {
                GameObject lightObj = GameObject.Find(lightName);
                if (lightObj != null)
                {
                    targetLight = lightObj.GetComponent<Light>();
                }
            }

            // Configure camera for depth texture if needed
            if (useDepthTexture)
            {
                Camera[] cameras = UnityEngine.Object.FindObjectsByType<Camera>(FindObjectsSortMode.None);
                foreach (var camera in cameras)
                {
                    camera.depthTextureMode |= DepthTextureMode.Depth;
                }
            }

            var data = new
            {
                name = lightShaftsName,
                light_name = lightName,
                intensity = intensity,
                threshold = threshold,
                sun_shaft_blur_radius = sunShaftBlurRadius,
                sun_shaft_intensity = sunShaftIntensity,
                radial_blur_iterations = radialBlurIterations,
                sun_color = sunColor,
                use_depth_texture = useDepthTexture,
                light_found = targetLight != null
            };

            return Response.Success($"Light shafts '{lightShaftsName}' created successfully.", data);
        }

        private static object ConfigureLightShafts(JObject @params)
        {
            string lightShaftsName = @params["light_shafts_name"]?.ToString();
            
            if (string.IsNullOrEmpty(lightShaftsName))
            {
                return Response.Error("Light shafts name is required for configuration.");
            }

            GameObject lightShaftsObj = GameObject.Find(lightShaftsName);
            if (lightShaftsObj == null)
            {
                return Response.Error($"Light shafts '{lightShaftsName}' not found.");
            }

            var lightShaftsData = lightShaftsObj.GetComponent<LightShaftsData>();
            if (lightShaftsData == null)
            {
                return Response.Error($"Light shafts data not found on '{lightShaftsName}'.");
            }

            // Update properties if provided
            if (@params["light_name"] != null)
                lightShaftsData.lightName = @params["light_name"].ToString();
            if (@params["intensity"] != null)
                lightShaftsData.intensity = @params["intensity"].ToObject<float>();
            if (@params["threshold"] != null)
                lightShaftsData.threshold = @params["threshold"].ToObject<float>();
            if (@params["sun_shaft_blur_radius"] != null)
                lightShaftsData.sunShaftBlurRadius = @params["sun_shaft_blur_radius"].ToObject<float>();
            if (@params["sun_shaft_intensity"] != null)
                lightShaftsData.sunShaftIntensity = @params["sun_shaft_intensity"].ToObject<float>();
            if (@params["radial_blur_iterations"] != null)
                lightShaftsData.radialBlurIterations = @params["radial_blur_iterations"].ToObject<int>();
            if (@params["use_depth_texture"] != null)
                lightShaftsData.useDepthTexture = @params["use_depth_texture"].ToObject<bool>();

            if (@params["sun_color"] != null)
            {
                var sunColor = @params["sun_color"].ToObject<float[]>();
                if (sunColor != null && sunColor.Length >= 3)
                {
                    lightShaftsData.sunColor = new Color(sunColor[0], sunColor[1], sunColor[2], 
                        sunColor.Length >= 4 ? sunColor[3] : 1.0f);
                }
            }

            var data = new
            {
                name = lightShaftsName,
                light_name = lightShaftsData.lightName,
                intensity = lightShaftsData.intensity,
                threshold = lightShaftsData.threshold,
                sun_shaft_blur_radius = lightShaftsData.sunShaftBlurRadius,
                sun_shaft_intensity = lightShaftsData.sunShaftIntensity,
                radial_blur_iterations = lightShaftsData.radialBlurIterations,
                use_depth_texture = lightShaftsData.useDepthTexture,
                sun_color = new float[] { lightShaftsData.sunColor.r, lightShaftsData.sunColor.g, lightShaftsData.sunColor.b, lightShaftsData.sunColor.a }
            };

            return Response.Success($"Light shafts '{lightShaftsName}' configured successfully.", data);
        }

        private static object DeleteLightShafts(JObject @params)
        {
            string lightShaftsName = @params["light_shafts_name"]?.ToString();
            
            if (string.IsNullOrEmpty(lightShaftsName))
            {
                // Delete all light shafts
                LightShaftsData[] allLightShafts = UnityEngine.Object.FindObjectsByType<LightShaftsData>(FindObjectsSortMode.None);
                foreach (var lightShafts in allLightShafts)
                {
                    UnityEngine.Object.DestroyImmediate(lightShafts.gameObject);
                }
                return Response.Success($"Deleted {allLightShafts.Length} light shafts.");
            }
            else
            {
                GameObject lightShaftsObj = GameObject.Find(lightShaftsName);
                if (lightShaftsObj != null && lightShaftsObj.GetComponent<LightShaftsData>() != null)
                {
                    UnityEngine.Object.DestroyImmediate(lightShaftsObj);
                    return Response.Success($"Light shafts '{lightShaftsName}' deleted successfully.");
                }
                else
                {
                    return Response.Error($"Light shafts '{lightShaftsName}' not found.");
                }
            }
        }

        private static object HandleOptimizeRenderingPerformance(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "optimize":
                        return OptimizeRenderingPerformance(@params);
                    case "analyze":
                        return AnalyzeRenderingPerformance();
                    case "reset":
                        return ResetRenderingSettings();
                    default:
                        return Response.Error($"Unknown rendering performance action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error in Rendering Performance {action}: {e.Message}");
            }
        }

        private static object OptimizeRenderingPerformance(JObject @params)
        {
            string targetPlatform = @params["target_platform"]?.ToString() ?? "pc";
            int targetFPS = @params["target_fps"]?.ToObject<int>() ?? 60;
            string qualityLevel = @params["quality_level"]?.ToString() ?? "medium";
            bool optimizeLighting = @params["optimize_lighting"]?.ToObject<bool>() ?? true;
            bool optimizeShadows = @params["optimize_shadows"]?.ToObject<bool>() ?? true;
            bool optimizeTextures = @params["optimize_textures"]?.ToObject<bool>() ?? true;
            bool optimizePostProcessing = @params["optimize_post_processing"]?.ToObject<bool>() ?? true;

            var optimizations = new List<string>();

            // Optimize based on target platform
            switch (targetPlatform.ToLower())
            {
                case "mobile":
                    OptimizeForMobile(targetFPS, optimizations);
                    break;
                case "vr":
                    OptimizeForVR(targetFPS, optimizations);
                    break;
                case "pc":
                default:
                    OptimizeForPC(targetFPS, qualityLevel, optimizations);
                    break;
            }

            // Apply specific optimizations
            if (optimizeLighting)
            {
                OptimizeLightingSettings(optimizations);
            }

            if (optimizeShadows)
            {
                OptimizeShadowSettings(targetFPS, optimizations);
            }

            if (optimizeTextures)
            {
                OptimizeTextureSettings(targetPlatform, optimizations);
            }

            if (optimizePostProcessing)
            {
                OptimizePostProcessingSettings(targetFPS, optimizations);
            }

            var data = new
            {
                target_platform = targetPlatform,
                target_fps = targetFPS,
                quality_level = qualityLevel,
                optimizations_applied = optimizations.ToArray(),
                optimization_count = optimizations.Count
            };

            return Response.Success("Rendering performance optimized successfully.", data);
        }

        private static object AnalyzeRenderingPerformance()
        {
            var analysis = new
            {
                // Quality Settings Analysis
                quality_level = QualitySettings.GetQualityLevel(),
                quality_name = QualitySettings.names[QualitySettings.GetQualityLevel()],
                shadow_cascades = QualitySettings.shadowCascades,
                shadow_distance = QualitySettings.shadowDistance,
                shadow_resolution = QualitySettings.shadowResolution.ToString(),
                antialiasing = QualitySettings.antiAliasing,
                anisotropic_filtering = QualitySettings.anisotropicFiltering.ToString(),
                texture_quality = QualitySettings.globalTextureMipmapLimit,
                
                // Lighting Settings Analysis
                realtime_gi = Lightmapping.realtimeGI,
                baked_gi = Lightmapping.bakedGI,
                
                // Camera Analysis
                camera_count = UnityEngine.Object.FindObjectsByType<Camera>(FindObjectsSortMode.None).Length,
                
                // Light Analysis
                light_count = UnityEngine.Object.FindObjectsByType<Light>(FindObjectsSortMode.None).Length,
                directional_lights = UnityEngine.Object.FindObjectsByType<Light>(FindObjectsSortMode.None).Count(l => l.type == LightType.Directional),
                point_lights = UnityEngine.Object.FindObjectsByType<Light>(FindObjectsSortMode.None).Count(l => l.type == LightType.Point),
                spot_lights = UnityEngine.Object.FindObjectsByType<Light>(FindObjectsSortMode.None).Count(l => l.type == LightType.Spot),
                
                // Performance Recommendations
                recommendations = GeneratePerformanceRecommendations()
            };

            return Response.Success("Rendering performance analysis completed.", analysis);
        }

        private static object ResetRenderingSettings()
        {
            // Reset to Unity defaults
            QualitySettings.SetQualityLevel(QualitySettings.names.Length / 2); // Medium quality
            QualitySettings.shadowCascades = 4;
            QualitySettings.shadowDistance = 150f;
            QualitySettings.shadowResolution = UnityEngine.ShadowResolution.Medium;
            QualitySettings.antiAliasing = 0;
            QualitySettings.anisotropicFiltering = AnisotropicFiltering.Enable;
            QualitySettings.globalTextureMipmapLimit = 0;

            return Response.Success("Rendering settings reset to default values.");
        }

        private static void OptimizeForMobile(int targetFPS, List<string> optimizations)
        {
            QualitySettings.shadowCascades = 1;
            QualitySettings.shadowDistance = 50f;
            QualitySettings.shadowResolution = UnityEngine.ShadowResolution.Low;
            QualitySettings.antiAliasing = 0;
            QualitySettings.globalTextureMipmapLimit = 1;
            optimizations.Add("Configured for mobile: reduced shadows, disabled MSAA, reduced texture quality");
        }

        private static void OptimizeForVR(int targetFPS, List<string> optimizations)
        {
            QualitySettings.shadowCascades = 2;
            QualitySettings.shadowDistance = 100f;
            QualitySettings.shadowResolution = UnityEngine.ShadowResolution.Medium;
            QualitySettings.antiAliasing = 0; // Use FXAA instead for VR
            optimizations.Add("Configured for VR: optimized shadows, disabled MSAA for VR-specific AA");
        }

        private static void OptimizeForPC(int targetFPS, string qualityLevel, List<string> optimizations)
        {
            switch (qualityLevel.ToLower())
            {
                case "low":
                    QualitySettings.shadowCascades = 2;
                    QualitySettings.shadowResolution = UnityEngine.ShadowResolution.Low;
                    QualitySettings.antiAliasing = 0;
                    break;
                case "high":
                    QualitySettings.shadowCascades = 4;
                    QualitySettings.shadowResolution = UnityEngine.ShadowResolution.High;
                    QualitySettings.antiAliasing = 4;
                    break;
                case "medium":
                default:
                    QualitySettings.shadowCascades = 4;
                    QualitySettings.shadowResolution = UnityEngine.ShadowResolution.Medium;
                    QualitySettings.antiAliasing = 2;
                    break;
            }
            optimizations.Add($"Configured for PC {qualityLevel} quality");
        }

        private static void OptimizeLightingSettings(List<string> optimizations)
        {
            var lightingSettings = Lightmapping.lightingSettings;
            if (lightingSettings == null)
            {
                // Create a new LightingSettings object if none exists
                lightingSettings = new LightingSettings();
                Lightmapping.lightingSettings = lightingSettings;
                optimizations.Add("Created new LightingSettings object");
            }
            
            // Configure lighting settings for performance
            lightingSettings.directSampleCount = 16;
            lightingSettings.indirectSampleCount = 64;
            lightingSettings.maxBounces = 2;
            lightingSettings.albedoBoost = 1.0f;
            lightingSettings.indirectScale = 1.0f;
            lightingSettings.lightmapMaxSize = 1024;
            lightingSettings.lightmapResolution = 10.0f;
            lightingSettings.lightmapPadding = 2;
            lightingSettings.lightmapCompression = LightmapCompression.LowQuality;
            
            optimizations.Add("Optimized lighting: reduced sample counts and bounces");
        }

        private static void OptimizeShadowSettings(int targetFPS, List<string> optimizations)
        {
            if (targetFPS >= 60)
            {
                QualitySettings.shadowDistance = 150f;
            }
            else if (targetFPS >= 30)
            {
                QualitySettings.shadowDistance = 100f;
            }
            else
            {
                QualitySettings.shadowDistance = 50f;
            }
            optimizations.Add($"Optimized shadow distance for {targetFPS} FPS target");
        }

        private static void OptimizeTextureSettings(string platform, List<string> optimizations)
        {
            switch (platform.ToLower())
            {
                case "mobile":
                    QualitySettings.globalTextureMipmapLimit = 2;
                    break;
                case "vr":
                    QualitySettings.globalTextureMipmapLimit = 1;
                    break;
                default:
                    QualitySettings.globalTextureMipmapLimit = 0;
                    break;
            }
            optimizations.Add($"Optimized texture quality for {platform}");
        }

        private static void OptimizePostProcessingSettings(int targetFPS, List<string> optimizations)
        {
            // Find and optimize post-processing volumes using Unity's Volume system
            var ppVolumes = UnityEngine.Object.FindObjectsByType<Volume>(FindObjectsSortMode.None);
            foreach (var volume in ppVolumes)
            {
                if (targetFPS < 30)
                {
                    volume.weight *= 0.5f; // Reduce weight for better performance
                }
            }
            
            if (ppVolumes.Length > 0)
            {
                optimizations.Add($"Optimized {ppVolumes.Length} post-processing volumes");
            }
        }

        private static string[] GeneratePerformanceRecommendations()
        {
            var recommendations = new List<string>();
            
            // Check light count
            int lightCount = UnityEngine.Object.FindObjectsByType<Light>(FindObjectsSortMode.None).Length;
            if (lightCount > 8)
            {
                recommendations.Add($"Consider reducing light count from {lightCount} to 8 or fewer for better performance");
            }
            
            // Check shadow settings
            if (QualitySettings.shadowDistance > 200f)
            {
                recommendations.Add("Consider reducing shadow distance for better performance");
            }
            
            // Check texture quality
            if (QualitySettings.globalTextureMipmapLimit == 0)
            {
                recommendations.Add("Consider reducing texture quality on lower-end devices");
            }
            
            return recommendations.ToArray();
        }

        /// <summary>
        /// [HELPER] - Parse Vector3 de JToken.
        /// </summary>
        private static Vector3? ParseVector3(JToken token)
        {
            if (token == null) return null;
            
            if (token is JArray array && array.Count >= 3)
            {
                return new Vector3(
                    array[0]?.ToObject<float>() ?? 0f,
                    array[1]?.ToObject<float>() ?? 0f,
                    array[2]?.ToObject<float>() ?? 0f
                );
            }
            
            return null;
        }

        /// <summary>
        /// [HELPER] - Sistema de logging para operações.
        /// </summary>
        private static void LogOperation(string operation, string details, bool isError = false)
        {
            string message = $"[LightingRendering] {operation}: {details}";
            if (isError)
                Debug.LogError(message);
            else
                Debug.Log(message);
        }
    }

    // Helper component for volumetric light data
    public class VolumetricLightData : MonoBehaviour
    {
        public float volumetricMultiplier = 1.0f;
        public float shadowDimmer = 1.0f;
        public float volumetricShadowDimmer = 1.0f;
    }



    // Helper component for shadow cascades data
    public class ShadowCascadesData : MonoBehaviour
    {
        public int cascadeCount = 4;
        public float[] cascadeSplits = new float[0];
        public float maxDistance = 1000.0f;
        public int resolution = 2048;
        public float shadowBias = 0.05f;
        public float shadowNormalBias = 0.4f;
        public bool configuredLights = false;
    }

    // Helper component for light cookies data
    public class LightCookiesData : MonoBehaviour
    {
        public string lightName = "CookieLight";
        public string cookieTexturePath = "";
        public float cookieSize = 1.0f;
        public Vector3 position = Vector3.zero;
        public Vector3 rotation = Vector3.zero;
        public float intensity = 1.0f;
        public bool cookieApplied = false;
    }

    // Helper component for HDR pipeline data
    public class HDRData : MonoBehaviour
    {
        public string pipelineType = "urp";
        public bool hdrEnabled = true;
        public int msaaSamples = 4;
        public float renderScale = 1.0f;
        public int depthBits = 24;
        public string opaqueDownsampling = "none";
        public bool configuredCameras = false;
        public bool hdrDisplay = true;
    }

    // Helper component for post-processing data
    public class PostProcessingData : MonoBehaviour
    {
        public string profileName = "PostProcessProfile";
        public string[] effects = new string[0];
        public float intensity = 1.0f;
        public int priority = 0;
        public bool isGlobal = true;
    }



    // Helper component for motion blur data
    public class MotionBlurData : MonoBehaviour
    {
        public new bool enabled = true;
        public float shutterAngle = 270.0f;
        public int sampleCount = 8;
        public float intensity = 1.0f;
        public float minimumVelocity = 2.0f;
        public float maximumVelocity = 200.0f;
        public bool cameraRotationBlur = true;
    }

    // Helper component for screen space reflections data
    public class ScreenSpaceReflectionsData : MonoBehaviour
    {
        public new bool enabled = true;
        public float intensity = 1.0f;
        public float thickness = 0.5f;
        public float maxDistance = 1000.0f;
        public int iterationCount = 16;
        public int stepCount = 4;
        public float smoothnessFadeStart = 0.0f;
        public float smoothnessFadeEnd = 0.9f;
        public bool enableVignette = true;
    }

    // Helper component for ambient occlusion data
    public class AmbientOcclusionData : MonoBehaviour
    {
        public new bool enabled = true;
        public float intensity = 1.0f;
        public float radius = 0.3f;
        public float power = 0.302f;
        public float bias = 0.05f;
        public float thicknessModifier = 1.0f;
        public float directLightingStrength = 0.25f;
        public int sampleCount = 6;
        public bool downsampling = false;
    }

    // Helper component for fog effects data
    public class FogEffectsData : MonoBehaviour
    {
        public new bool enabled = true;
        public string fogMode = "exponentialsquared";
        public float fogDensity = 0.01f;
        public float linearFogStart = 0.0f;
        public float linearFogEnd = 300.0f;
        public float[] fogColor = null;
    }

    // Helper component for caustics data
    public class CausticsData : MonoBehaviour
    {
        public float intensity = 1.0f;
        public float scale = 1.0f;
        public Vector3 size = Vector3.one;
        public float speed = 1.0f;
        public string causticsTexture = null;
        public Vector3 lightDirection = Vector3.down;
    }

    // Helper component for light shafts data
    public class LightShaftsData : MonoBehaviour
    {
        public string lightName = null;
        public float intensity = 1.0f;
        public float threshold = 0.75f;
        public float sunShaftBlurRadius = 2.5f;
        public float sunShaftIntensity = 1.15f;
        public int radialBlurIterations = 2;
        public Color sunColor = Color.white;
        public bool useDepthTexture = true;
    }
}